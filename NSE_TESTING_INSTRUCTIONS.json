{"testing_pipeline": {"overview": "Complete pipeline for testing NSE stocks with screener.in + Yahoo Finance integration", "prerequisites": ["Edge browser with debug mode enabled", "screener.in login credentials", "Python environment with required packages"], "steps": [{"step": 1, "description": "Start Edge with debug mode", "command": "Start-Process -FilePath \"C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe\" -ArgumentList \"--remote-debugging-port=9222\", \"--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Edge\\User Data\""}, {"step": 2, "description": "Extract screener.in data", "command": "python screener_mcp_final.py", "input": "Enter stock symbol (e.g., HDFCBANK, TCS, INFY)"}, {"step": 3, "description": "Integrate with Yahoo Finance", "command": "python enhanced_data_integrator.py", "note": "Modify symbol in script or make it accept command line arguments"}, {"step": 4, "description": "Validate against AMD format", "command": "python nse_stock_tester.py", "expected": "100% AMD format compatibility"}]}, "test_stocks": {"priority_1": ["HDFCBANK", "TCS", "INFY"], "priority_2": ["ICICIBANK", "HINDUNILVR", "ITC"], "priority_3": ["SBIN", "BHARTIARTL", "KOTAKBANK"]}, "symbol_mapping": {"screener_in": "Use base symbol (e.g., HDFCBANK)", "yahoo_finance": "Add .NS suffix (e.g., HDFCBANK.NS)", "final_ticker": "Use .NS format in final dataset"}, "validation_criteria": {"column_count": 44, "required_columns": ["ticker", "market_cap", "price_to_earnings_ratio", "return_on_equity"], "data_completeness": "> 70%", "amd_compatibility": "100%"}}