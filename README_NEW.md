# Screener.in Ratio Analyzer MCP

A powerful Model Context Protocol (MCP) server that automates financial ratio extraction from screener.in using your existing Microsoft Edge browser. This tool connects to your Edge browser and follows the exact sequence: **Smart Analysis → Wait 4s → Scroll → Ratio Analysis** to extract comprehensive financial ratios for any stock.

## 🎯 Features

- **Smart Browser Integration**: Connects to your existing Edge browser (preserves login state, extensions, etc.)
- **Comprehensive Ratio Extraction**: Extracts all major financial ratios including:
  - 💰 Leverage Ratios (Debt-to-Equity, Interest Coverage, etc.)
  - ⚡ Efficiency Ratios (Asset Turnover, Working Capital, etc.)
  - 💹 Profitability Ratios (ROE, ROA, ROIC, Margins, etc.)
  - 💎 Valuation Ratios (P/E, P/B, EV/EBITDA, etc.)
  - 🏛️ Capital Allocation Ratios (Dividend Payout, etc.)
- **10-Year Historical Data**: Captures yearly data trends
- **Robust Navigation**: Multiple fallback strategies for reliable element detection
- **MCP Protocol**: Easy integration with AI assistants and other tools

## 🚀 Quick Start

### Option 1: One-Command Setup & Run
```powershell
python quick_run.py
```

### Option 2: Manual Setup

1. **Setup Edge Browser**:
   ```powershell
   powershell -ExecutionPolicy Bypass -File setup_edge_debug.ps1
   ```

2. **Install Dependencies**:
   ```powershell
   pip install -r requirements_ratio_analyzer.txt
   playwright install chromium
   ```

3. **Run the MCP Server**:
   ```powershell
   python screener_ratio_analyzer_mcp.py
   ```

### Option 3: NPM Scripts
```powershell
npm run setup-edge    # Setup Edge with debug mode
npm run setup         # Install dependencies
npm run start         # Start MCP server
npm run test          # Run tests
npm run quick-run     # All-in-one setup and run
```

## 📋 Prerequisites

- **Microsoft Edge** browser installed
- **Python 3.8+** with pip
- **PowerShell** (for setup scripts)
- **Internet connection** for accessing screener.in

## 🔧 How It Works

1. **Browser Connection**: Connects to Edge via Chrome DevTools Protocol (CDP)
2. **Smart Navigation**: 
   - Navigates to `https://www.screener.in/company/{STOCK}/`
   - Clicks "Smart Analysis" button (multiple detection strategies)
   - Waits exactly 4 seconds for content to load
   - Scrolls to find the ratios section
   - Clicks "Ratio Analysis" button
3. **Data Extraction**: 
   - Parses HTML tables using pandas
   - Extracts ratio cards and sections
   - Uses JavaScript for dynamic content
   - Categorizes ratios automatically
4. **Data Formatting**: Returns structured JSON with categorized ratios

## 🛠️ MCP Tools

### `extract_financial_ratios`
Extracts comprehensive financial ratios for a given stock symbol.

**Input**:
```json
{
  "stock_symbol": "RELIANCE"
}
```

**Output**: Categorized ratios with historical data

### `navigate_to_ratio_analysis`
Navigates to the ratio analysis page following the exact sequence.

**Input**:
```json
{
  "stock_symbol": "RELIANCE"
}
```

## 📊 Sample Output

```
📊 Financial Ratios Analysis for RELIANCE
🕒 Extracted at: 2025-05-31T10:30:15.123456
============================================================

💰 Leverage Ratios:
  • Debt to Equity: 
    2024: 0.23
    2023: 0.25
    2022: 0.28
  • Interest Coverage Ratio:
    2024: 15.2
    2023: 12.8
    2022: 10.5

⚡ Efficiency Ratios:
  • Asset Turnover:
    2024: 0.85
    2023: 0.82
    2022: 0.79
  • Working Capital Turnover:
    2024: 4.2
    2023: 3.9
    2022: 3.6

💹 Profitability Ratios:
  • Return on Equity (ROE):
    2024: 18.5%
    2023: 16.2%
    2022: 14.8%
  • Net Profit Margin:
    2024: 12.3%
    2023: 11.8%
    2022: 10.9%

💎 Valuation Ratios:
  • Price to Earnings (P/E):
    2024: 25.4
    2023: 28.1
    2022: 32.5
  • Price to Book (P/B):
    2024: 2.8
    2023: 3.1
    2022: 3.4

📅 Yearly Data Summary:
  📊 2024: 45 metrics
  📊 2023: 43 metrics
  📊 2022: 41 metrics
  📊 2021: 39 metrics
  📊 2020: 37 metrics

📈 Summary: 185 total metrics extracted across all categories
```

## 🧪 Testing

Run the test suite to verify everything works:

```powershell
python test_screener_mcp_new.py
```

The test will:
- Connect to Edge browser
- Test navigation for multiple stocks
- Extract ratios and save results
- Display summary statistics

## 🔍 Troubleshooting

### Edge Connection Issues

If you get connection errors:

1. **Check Edge Debug Mode**:
   ```powershell
   # Check if port 9222 is open
   Test-NetConnection -ComputerName localhost -Port 9222
   ```

2. **Restart Edge with Debug Mode**:
   ```powershell
   # Close all Edge windows first, then:
   "C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe" --remote-debugging-port=9222 --user-data-dir="%LOCALAPPDATA%\Microsoft\Edge\User Data"
   ```

3. **Use Setup Script**:
   ```powershell
   powershell -ExecutionPolicy Bypass -File setup_edge_debug.ps1
   ```

### Navigation Issues

If Smart Analysis or Ratio Analysis buttons aren't found:
- The script has multiple fallback strategies
- Check if screener.in has changed their UI
- Enable debug logging for more details

### Data Extraction Issues

If ratios aren't extracted properly:
- The tool uses multiple extraction methods (tables, cards, JavaScript)
- Some stocks may have different layouts
- Historical data availability varies by stock

## 📁 Project Structure

```
screener_ratio_analyzer_mcp.py    # Main MCP server
setup_edge_debug.ps1              # Edge setup script  
test_screener_mcp_new.py          # Test suite
quick_run.py                      # One-command setup & run
requirements_ratio_analyzer.txt   # Python dependencies
package.json                      # NPM scripts
README_NEW.md                     # This file
```

## 🔧 Configuration

### Supported Stocks
Any stock listed on screener.in:
- `RELIANCE`, `TCS`, `INFY`, `HDFC`, `ICICIBANK`
- `WIPRO`, `LT`, `HCLTECH`, `TECHM`, `MARUTI`
- And many more...

### Custom Edge Port
If you need to use a different debug port:

```python
# In screener_ratio_analyzer_mcp.py
await analyzer.connect_to_existing_edge(port=9223)  # Custom port
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if needed
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

- [Playwright](https://playwright.dev/) for browser automation
- [MCP Protocol](https://github.com/modelcontextprotocol) for the framework
- [Screener.in](https://www.screener.in/) for financial data
- [Pandas](https://pandas.pydata.org/) for data processing

---

**💡 Pro Tip**: Keep your Edge browser open with your screener.in session logged in for the best experience!
