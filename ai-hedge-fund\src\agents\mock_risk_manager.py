"""
Mock risk management agent that uses mock data.
This is a drop-in replacement for the original risk management agent.
"""

from langchain_core.messages import HumanMessage
from graph.state import AgentState, show_agent_reasoning
from utils.progress import progress
import json
import random
import os

##### Mock Risk Management Agent #####
def mock_risk_management_agent(state: AgentState):
    """Controls position sizing based on mock risk factors for multiple tickers."""
    portfolio = state["data"]["portfolio"]
    data = state["data"]
    tickers = data["tickers"]

    # Initialize risk analysis for each ticker
    risk_analysis = {}
    current_prices = {}  # Store prices here to avoid redundant API calls

    for ticker in tickers:
        progress.update_status("risk_management_agent", ticker, "Analyzing mock price data")

        # Generate a mock current price or use real price data for Reliance
        if ticker == "RELIANCE":
            # Check if we should use real price data for Reliance
            mock_data_dir = os.environ.get("MOCK_DATA_DIR")
            if mock_data_dir and os.path.exists(mock_data_dir):
                price_file = os.path.join(mock_data_dir, "price_data.csv")
                if os.path.exists(price_file):
                    print(f"Using real price data for {ticker} from {price_file}")
                    import pandas as pd
                    try:
                        df = pd.read_csv(price_file)
                        if not df.empty:
                            # Get the most recent price
                            latest_price = df.iloc[-1]['close']
                            current_price = float(latest_price)
                            print(f"Latest price for {ticker}: {current_price}")
                        else:
                            current_price = random.uniform(2000.0, 2500.0)  # Default price range for Reliance
                    except Exception as e:
                        print(f"Error reading price file: {e}")
                        current_price = random.uniform(2000.0, 2500.0)  # Default price range for Reliance
                else:
                    current_price = random.uniform(2000.0, 2500.0)  # Default price range for Reliance
            else:
                current_price = random.uniform(2000.0, 2500.0)  # Default price range for Reliance
        else:
            current_price = random.uniform(80.0, 150.0)

        current_prices[ticker] = current_price  # Store the current price

        progress.update_status("risk_management_agent", ticker, "Calculating position limits")

        # Calculate portfolio value
        # Calculate current position value for this ticker
        current_position_value = portfolio.get("positions", {}).get(ticker, {}).get("cost_basis", 0)

        # Calculate total portfolio value using stored prices
        total_portfolio_value = portfolio.get("cash", 100000.0)
        for t, pos in portfolio.get("positions", {}).items():
            total_portfolio_value += pos.get("cost_basis", 0)

        # Base limit is 20% of portfolio for any single position
        position_limit = total_portfolio_value * 0.20

        # For existing positions, subtract current position value from limit
        remaining_position_limit = position_limit - current_position_value

        # Ensure we don't exceed available cash
        max_position_size = min(remaining_position_limit, portfolio.get("cash", 100000.0))

        risk_analysis[ticker] = {
            "remaining_position_limit": float(max_position_size),
            "current_price": float(current_price),
            "reasoning": {
                "portfolio_value": float(total_portfolio_value),
                "current_position": float(current_position_value),
                "position_limit": float(position_limit),
                "remaining_limit": float(remaining_position_limit),
                "available_cash": float(portfolio.get("cash", 100000.0)),
            },
        }

        progress.update_status("risk_management_agent", ticker, "Done")

    message = HumanMessage(
        content=json.dumps(risk_analysis),
        name="risk_management_agent",
    )

    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(risk_analysis, "Risk Management Agent")

    # Add the signal to the analyst_signals list
    state["data"]["analyst_signals"]["risk_management_agent"] = risk_analysis

    return {
        "messages": state["messages"] + [message],
        "data": data,
    }
