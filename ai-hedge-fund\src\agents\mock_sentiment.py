"""
Mock sentiment agent that uses mock API data for any ticker.
This is a drop-in replacement for the original sentiment agent.
"""

from langchain_core.messages import HumanMessage
from graph.state import AgentState, show_agent_reasoning
from utils.progress import progress
import pandas as pd
import numpy as np
import json

from tools.mock_api import get_insider_trades, get_company_news


##### Mock Sentiment Agent #####
def mock_sentiment_agent(state: AgentState):
    """Analyzes market sentiment using mock data and generates trading signals for multiple tickers."""
    data = state.get("data", {})
    end_date = data.get("end_date")
    tickers = data.get("tickers")

    # Initialize sentiment analysis for each ticker
    sentiment_analysis = {}

    for ticker in tickers:
        progress.update_status("mock_sentiment_agent", ticker, "Fetching insider trades")

        # Get the insider trades using mock API
        try:
            insider_trades = get_insider_trades(
                ticker=ticker,
                end_date=end_date,
                limit=1000,
            )
            
            # Get the signals from the insider trades
            transaction_shares = pd.Series([t.transaction_shares for t in insider_trades if t.transaction_shares is not None]).dropna()
            insider_signals = np.where(transaction_shares < 0, "bearish", "bullish").tolist()
        except Exception as e:
            print(f"Error processing insider trades for {ticker}: {e}")
            insider_signals = []

        progress.update_status("mock_sentiment_agent", ticker, "Fetching company news")

        # Get the company news using mock API
        try:
            company_news = get_company_news(ticker, end_date, limit=100)
            
            # Get the sentiment from the company news
            sentiment = pd.Series([n.sentiment for n in company_news if n.sentiment is not None]).dropna()
            news_signals = np.where(sentiment == "negative", "bearish", 
                                np.where(sentiment == "positive", "bullish", "neutral")).tolist()
        except Exception as e:
            print(f"Error processing company news for {ticker}: {e}")
            news_signals = []
        
        progress.update_status("mock_sentiment_agent", ticker, "Combining signals")
        # Combine signals from both sources with weights
        insider_weight = 0.3
        news_weight = 0.7
        
        # Calculate weighted signal counts
        bullish_signals = (
            insider_signals.count("bullish") * insider_weight +
            news_signals.count("bullish") * news_weight
        )
        bearish_signals = (
            insider_signals.count("bearish") * insider_weight +
            news_signals.count("bearish") * news_weight
        )

        if bullish_signals > bearish_signals:
            overall_signal = "bullish"
        elif bearish_signals > bullish_signals:
            overall_signal = "bearish"
        else:
            overall_signal = "neutral"

        # Calculate confidence level based on the weighted proportion
        total_weighted_signals = len(insider_signals) * insider_weight + len(news_signals) * news_weight
        confidence = 0  # Default confidence when there are no signals
        if total_weighted_signals > 0:
            confidence = round(max(bullish_signals, bearish_signals) / total_weighted_signals, 2) * 100
        reasoning = f"Weighted Bullish signals: {bullish_signals:.1f}, Weighted Bearish signals: {bearish_signals:.1f}"

        sentiment_analysis[ticker] = {
            "signal": overall_signal,
            "confidence": confidence,
            "reasoning": reasoning,
        }

        progress.update_status("mock_sentiment_agent", ticker, "Done")

        # Print some sample data for debugging
        print(f"\n{ticker} Mock Sentiment Analysis:")
        print(f"Signal: {overall_signal.upper()}")
        print(f"Confidence: {confidence:.1f}%")
        print(f"Reasoning: {reasoning}")
        
        print(f"\nSample News:")
        try:
            for i, news in enumerate(company_news[:3]):
                if hasattr(news, 'sentiment') and news.sentiment:
                    print(f"{i+1}. {news.title} - {news.sentiment.upper()} ({news.date})")
        except Exception as e:
            print(f"Error displaying news: {e}")
        
        print(f"\nSample Insider Trades:")
        try:
            for i, trade in enumerate(insider_trades[:3]):
                if hasattr(trade, 'transaction_shares') and trade.transaction_shares is not None:
                    trade_type = "SELLING" if trade.transaction_shares < 0 else "BUYING"
                    print(f"{i+1}. {trade.name} ({trade.title}): {trade_type} {abs(trade.transaction_shares)} shares at ${trade.transaction_price_per_share:.2f} ({trade.transaction_date})")
        except Exception as e:
            print(f"Error displaying insider trades: {e}")

    # Create the sentiment message
    message = HumanMessage(
        content=json.dumps(sentiment_analysis),
        name="mock_sentiment_agent",
    )

    # Print the reasoning if the flag is set
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(sentiment_analysis, "Mock Sentiment Analysis Agent")

    # Add the signal to the analyst_signals list
    state["data"]["analyst_signals"]["mock_sentiment_agent"] = sentiment_analysis

    return {
        "messages": [message],
        "data": data,
    }
