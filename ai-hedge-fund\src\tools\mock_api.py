"""
Mock API module that loads data from local CSV files instead of making API calls.
This is useful for testing with tickers that are not in the free tier.
"""

import os
import pandas as pd
import random
from typing import List, Dict, Optional, Any, Union
from datetime import datetime, timedelta

from data.models import (
    CompanyNews,
    CompanyNewsResponse,
    FinancialMetrics,
    FinancialMetricsResponse,
    Price,
    PriceResponse,
    LineItem,
    LineItemResponse,
    InsiderTrade,
    InsiderTradeResponse,
)

# Path to the mock data directory
MOCK_DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), "mock_financial_data")

def get_prices(ticker: str, start_date: str, end_date: str) -> List[Price]:
    """Load mock price data from CSV."""
    print(f"Loading mock price data for {ticker}...")

    # Free tier tickers
    FREE_TICKERS = ["AAPL", "GOOGL", "MSFT", "NVDA", "TSLA"]

    # If ticker is not in free tier and not in mock data, use NVDA as a substitute
    if ticker not in FREE_TICKERS and ticker != "AMD":
        print(f"Warning: {ticker} is not in free tier. Using NVDA data as a substitute.")
        ticker = "NVDA"

    try:
        # For AMD, generate mock price data
        if ticker == "AMD":
            # Generate 30 days of mock price data
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")

            # Generate dates (excluding weekends)
            dates = []
            current = start_dt
            while current <= end_dt:
                if current.weekday() < 5:  # Monday to Friday
                    dates.append(current)
                current += timedelta(days=1)

            # Generate price data
            base_price = 120.0  # Starting price for AMD
            prices = []
            for i, date in enumerate(dates):
                # Generate price with some randomness and a slight upward trend
                daily_change = random.uniform(-0.03, 0.035)  # -3% to +3.5%
                if i > 0:
                    base_price = base_price * (1 + daily_change)

                # Generate high, low, open prices based on close
                high = base_price * random.uniform(1.0, 1.03)
                low = base_price * random.uniform(0.97, 1.0)
                open_price = base_price * random.uniform(0.99, 1.01)

                # Format date
                iso_date = date.strftime("%Y-%m-%dT%H:%M:%SZ")
                ms_timestamp = int(date.timestamp() * 1000)

                prices.append(Price(
                    ticker="AMD",
                    open=round(open_price, 2),
                    close=round(base_price, 2),
                    high=round(high, 2),
                    low=round(low, 2),
                    volume=int(random.uniform(5000000, 15000000)),
                    time=iso_date,
                    time_milliseconds=ms_timestamp
                ))

            return prices

        # For other tickers, load from CSV
        csv_path = os.path.join(MOCK_DATA_DIR, f"prices_{ticker}.csv")
        df = pd.read_csv(csv_path)

        # Filter by date range
        df['time'] = pd.to_datetime(df['time'])
        df = df[(df['time'] >= start_date) & (df['time'] <= end_date)]

        # Convert to Price objects
        prices = [Price(**row) for row in df.to_dict('records')]

        return prices
    except Exception as e:
        print(f"Error loading mock price data for {ticker}: {e}")
        return []

def get_financial_metrics(
    ticker: str,
    end_date: str,
    period: str = "ttm",
    limit: int = 10,
) -> List[FinancialMetrics]:
    """Load mock financial metrics from CSV."""
    print(f"Loading mock financial metrics for {ticker}...")

    # Free tier tickers
    FREE_TICKERS = ["AAPL", "GOOGL", "MSFT", "NVDA", "TSLA"]

    # If ticker is not in free tier and not in mock data, use NVDA as a substitute
    if ticker not in FREE_TICKERS and ticker != "AMD":
        print(f"Warning: {ticker} is not in free tier. Using NVDA data as a substitute.")
        ticker = "NVDA"

    try:
        # For AMD, use our generated mock data
        if ticker == "AMD":
            # Create mock financial metrics for AMD
            # Create base metrics for the most recent period
            base_metrics = {
                "ticker": "AMD",
                "report_period": "2025-03-31",
                "fiscal_period": "2025-Q1",
                "period": "ttm",
                "currency": "USD",
                "market_cap": 180000000000.0,
                "enterprise_value": 185000000000.0,
                "price_to_earnings_ratio": 35.2,
                "price_to_book_ratio": 4.8,
                "price_to_sales_ratio": 8.5,
                "enterprise_value_to_ebitda_ratio": 25.3,
                "enterprise_value_to_revenue_ratio": 8.7,
                "free_cash_flow_yield": 0.03,
                "peg_ratio": 1.2,
                "gross_margin": 0.45,
                "operating_margin": 0.18,
                "net_margin": 0.15,
                "return_on_equity": 0.12,
                "return_on_assets": 0.08,
                "return_on_invested_capital": 0.14,
                "asset_turnover": 0.6,
                "inventory_turnover": 4.5,
                "receivables_turnover": 8.2,
                "days_sales_outstanding": 44.5,
                "operating_cycle": 125.6,
                "working_capital_turnover": 4.8,
                "current_ratio": 2.1,
                "quick_ratio": 1.7,
                "cash_ratio": 0.9,
                "operating_cash_flow_ratio": 1.2,
                "debt_to_equity": 0.15,
                "debt_to_assets": 0.08,
                "interest_coverage": 25.0,
                "revenue_growth": 0.25,
                "earnings_growth": 0.32,
                "book_value_growth": 0.18,
                "earnings_per_share_growth": 0.30,
                "free_cash_flow_growth": 0.22,
                "operating_income_growth": 0.28,
                "ebitda_growth": 0.26,
                "payout_ratio": 0.0,  # AMD doesn't pay dividends
                "earnings_per_share": 3.2,
                "book_value_per_share": 12.5,
                "free_cash_flow_per_share": 2.8
            }

            # Start with the most recent metrics
            mock_metrics = [base_metrics.copy()]

            # Add historical data for the past 4 years
            historical_periods = [
                {"report_period": "2024-12-31", "fiscal_period": "2024-Q4"},
                {"report_period": "2024-09-30", "fiscal_period": "2024-Q3"},
                {"report_period": "2024-06-30", "fiscal_period": "2024-Q2"},
                {"report_period": "2024-03-31", "fiscal_period": "2024-Q1"},
            ]

            # Factors to adjust metrics for historical periods (showing consistent growth)
            for i, period_info in enumerate(historical_periods):
                # Create a copy of the base metrics
                historical_metrics = base_metrics.copy()

                # Update period information
                historical_metrics["report_period"] = period_info["report_period"]
                historical_metrics["fiscal_period"] = period_info["fiscal_period"]

                # Adjust key metrics to show consistent historical performance
                factor = 0.95 - (i * 0.05)  # Each period is smaller than the current

                # Adjust financial metrics
                historical_metrics["market_cap"] *= factor
                historical_metrics["enterprise_value"] *= factor
                historical_metrics["price_to_earnings_ratio"] *= (1 + (i * 0.02))  # P/E was slightly higher in the past
                historical_metrics["operating_margin"] *= (0.95 + (i * 0.01))  # Slightly lower margins in the past
                historical_metrics["net_margin"] *= (0.95 + (i * 0.01))
                historical_metrics["return_on_equity"] *= (0.95 + (i * 0.01))
                historical_metrics["return_on_assets"] *= (0.95 + (i * 0.01))
                historical_metrics["earnings_per_share"] *= factor
                historical_metrics["book_value_per_share"] *= factor
                historical_metrics["free_cash_flow_per_share"] *= factor

                # Add to the list of metrics
                mock_metrics.append(historical_metrics)
            return [FinancialMetrics(**metric) for metric in mock_metrics]

        # For other tickers, load from CSV
        csv_path = os.path.join(MOCK_DATA_DIR, f"financial_metrics_{ticker}.csv")
        df = pd.read_csv(csv_path)

        # Filter by date and period
        df = df[df['report_period'] <= end_date]
        if period != "all":
            df = df[df['period'] == period]

        # Sort by report period (most recent first)
        df = df.sort_values('report_period', ascending=False)

        # Limit the number of results
        df = df.head(limit)

        # Convert to FinancialMetrics objects
        metrics = [FinancialMetrics(**row) for row in df.to_dict('records')]

        return metrics
    except Exception as e:
        print(f"Error loading mock financial metrics for {ticker}: {e}")
        return []

def search_line_items(
    ticker: str,
    line_items: List[str],
    end_date: str,
    period: str = "ttm",
    limit: int = 10,
) -> List[LineItem]:
    """Load mock line items from CSV."""
    print(f"Loading mock line items for {ticker}...")

    # Free tier tickers
    FREE_TICKERS = ["AAPL", "GOOGL", "MSFT", "NVDA", "TSLA"]

    # If ticker is not in free tier and not in mock data, use NVDA as a substitute
    if ticker not in FREE_TICKERS and ticker != "AMD":
        print(f"Warning: {ticker} is not in free tier. Using NVDA data as a substitute.")
        ticker = "NVDA"

    try:
        # For AMD, generate mock line items
        if ticker == "AMD":
            # Create mock line items for AMD
            mock_items = []

            # Create a record for the most recent quarter
            record = {
                "ticker": "AMD",
                "report_period": "2025-03-31",
                "period": "ttm",
                "currency": "USD"
            }

            # Add requested line items
            if "revenue" in line_items:
                record["revenue"] = 25000000000.0  # $25 billion
            if "net_income" in line_items:
                record["net_income"] = 3750000000.0  # $3.75 billion
            if "total_assets" in line_items:
                record["total_assets"] = 65000000000.0  # $65 billion
            if "total_liabilities" in line_items:
                record["total_liabilities"] = 25000000000.0  # $25 billion
            if "total_shareholders_equity" in line_items:
                record["total_shareholders_equity"] = 40000000000.0  # $40 billion
            if "operating_income" in line_items:
                record["operating_income"] = 4500000000.0  # $4.5 billion
            if "ebitda" in line_items:
                record["ebitda"] = 5500000000.0  # $5.5 billion
            if "free_cash_flow" in line_items:
                record["free_cash_flow"] = 3000000000.0  # $3 billion
            if "capital_expenditure" in line_items:
                record["capital_expenditure"] = -2500000000.0  # $2.5 billion (negative for cash outflow)
            if "depreciation_and_amortization" in line_items:
                record["depreciation_and_amortization"] = 1200000000.0  # $1.2 billion
            if "outstanding_shares" in line_items:
                record["outstanding_shares"] = 1620000000.0  # 1.62 billion shares
            if "dividends_and_other_cash_distributions" in line_items:
                record["dividends_and_other_cash_distributions"] = 0.0  # AMD doesn't pay dividends
            if "issuance_or_purchase_of_equity_shares" in line_items:
                record["issuance_or_purchase_of_equity_shares"] = -1500000000.0  # $1.5 billion in share repurchases (negative for buybacks)

            # Add the most recent record
            mock_items.append(LineItem(**record))

            # Add historical data for the past 4 years
            historical_periods = [
                {"report_period": "2024-12-31", "fiscal_period": "2024-Q4"},
                {"report_period": "2024-09-30", "fiscal_period": "2024-Q3"},
                {"report_period": "2024-06-30", "fiscal_period": "2024-Q2"},
                {"report_period": "2024-03-31", "fiscal_period": "2024-Q1"},
                {"report_period": "2023-12-31", "fiscal_period": "2023-Q4"},
            ]

            # Growth factors for historical data (to show consistent growth)
            growth_factors = [0.95, 0.9, 0.85, 0.8, 0.75]  # Each period is smaller than the current

            for i, period_info in enumerate(historical_periods):
                if i < len(growth_factors):
                    factor = growth_factors[i]
                    historical_record = {
                        "ticker": "AMD",
                        "report_period": period_info["report_period"],
                        "fiscal_period": period_info["fiscal_period"],
                        "period": "ttm",
                        "currency": "USD"
                    }

                    # Add the same line items with historical values
                    if "revenue" in line_items:
                        historical_record["revenue"] = 25000000000.0 * factor
                    if "net_income" in line_items:
                        historical_record["net_income"] = 3750000000.0 * factor
                    if "total_assets" in line_items:
                        historical_record["total_assets"] = 65000000000.0 * factor
                    if "total_liabilities" in line_items:
                        historical_record["total_liabilities"] = 25000000000.0 * factor
                    if "total_shareholders_equity" in line_items:
                        historical_record["total_shareholders_equity"] = 40000000000.0 * factor
                    if "operating_income" in line_items:
                        historical_record["operating_income"] = 4500000000.0 * factor
                    if "ebitda" in line_items:
                        historical_record["ebitda"] = 5500000000.0 * factor
                    if "free_cash_flow" in line_items:
                        historical_record["free_cash_flow"] = 3000000000.0 * factor
                    if "capital_expenditure" in line_items:
                        historical_record["capital_expenditure"] = -2500000000.0 * factor
                    if "depreciation_and_amortization" in line_items:
                        historical_record["depreciation_and_amortization"] = 1200000000.0 * factor
                    if "outstanding_shares" in line_items:
                        # Slightly decreasing share count due to buybacks
                        historical_record["outstanding_shares"] = 1620000000.0 * (1 + (0.01 * i))
                    if "dividends_and_other_cash_distributions" in line_items:
                        historical_record["dividends_and_other_cash_distributions"] = 0.0
                    if "issuance_or_purchase_of_equity_shares" in line_items:
                        historical_record["issuance_or_purchase_of_equity_shares"] = -1500000000.0 * factor

                    mock_items.append(LineItem(**historical_record))

            return mock_items

        # For other tickers, load from CSV
        csv_path = os.path.join(MOCK_DATA_DIR, f"line_items_{ticker}.csv")
        df = pd.read_csv(csv_path)

        # Filter by date and period
        df = df[df['report_period'] <= end_date]
        if period != "all":
            df = df[df['period'] == period]

        # Sort by report period (most recent first)
        df = df.sort_values('report_period', ascending=False)

        # Limit the number of results
        df = df.head(limit)

        # Convert to LineItem objects
        items = [LineItem(**row) for row in df.to_dict('records')]

        return items
    except Exception as e:
        print(f"Error loading mock line items for {ticker}: {e}")
        return []

def get_insider_trades(
    ticker: str,
    end_date: str,
    start_date: Optional[str] = None,
    limit: int = 1000,
) -> List[InsiderTrade]:
    """Load mock insider trades from CSV or generate synthetic data."""
    print(f"Loading mock insider trades for {ticker}...")

    try:
        # First try to load from CSV if it exists
        csv_path = os.path.join(MOCK_DATA_DIR, f"insider_trades_{ticker}.csv")
        if os.path.exists(csv_path):
            df = pd.read_csv(csv_path)

            # Filter by date range
            df = df[df['filing_date'] <= end_date]
            if start_date:
                df = df[df['filing_date'] >= start_date]

            # Sort by filing date (most recent first)
            df = df.sort_values('filing_date', ascending=False)

            # Limit the number of results
            df = df.head(limit)

            # Convert to InsiderTrade objects
            trades = []
            for row in df.to_dict('records'):
                # Handle NaN values
                for key, value in row.items():
                    if pd.isna(value):
                        row[key] = None
                trades.append(InsiderTrade(**row))

            if trades:  # Only return if we actually got data
                return trades

        # If CSV doesn't exist or is empty, generate synthetic data
        # Get company name from ticker (simple approximation)
        if ticker == "AMD":
            company_name = "Advanced Micro Devices, Inc."
            executives = [
                {"name": "Lisa Su", "title": "CEO", "is_board_director": True},
                {"name": "Devinder Kumar", "title": "CFO", "is_board_director": False},
                {"name": "Rick Bergman", "title": "EVP", "is_board_director": False}
            ]
        else:
            # Generate a generic company name from the ticker
            company_name = f"{ticker.title()} Corporation"
            # Generate generic executives
            executives = [
                {"name": "John Smith", "title": "CEO", "is_board_director": True},
                {"name": "Jane Doe", "title": "CFO", "is_board_director": False},
                {"name": "Robert Johnson", "title": "CTO", "is_board_director": True}
            ]

        # Parse end_date
        end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")

        # Generate dates for transactions (within last 3 months of end_date)
        transaction_dates = []
        for i in range(10):
            days_back = random.randint(1, 90)
            transaction_date = end_date_dt - timedelta(days=days_back)
            transaction_dates.append(transaction_date)

        # Sort dates in descending order (most recent first)
        transaction_dates.sort(reverse=True)

        # Generate mock trades
        mock_trades = []
        for i in range(min(10, len(transaction_dates))):
            # Randomly select an executive
            executive = random.choice(executives)

            # Randomly decide if buying or selling (70% chance of selling)
            is_selling = random.random() < 0.7

            # Generate transaction details
            transaction_shares = random.randint(5000, 30000) * (-1 if is_selling else 1)
            transaction_price = random.uniform(80.0, 150.0)
            transaction_value = transaction_shares * transaction_price
            shares_before = random.randint(100000, 500000)
            shares_after = shares_before + transaction_shares

            # Format dates
            transaction_date_str = transaction_dates[i].strftime("%Y-%m-%d")
            filing_date = transaction_dates[i] + timedelta(days=random.randint(1, 5))
            filing_date_str = filing_date.strftime("%Y-%m-%d")

            # Create the trade record
            trade = {
                "ticker": ticker,
                "issuer": company_name,
                "name": executive["name"],
                "title": executive["title"],
                "is_board_director": executive["is_board_director"],
                "transaction_date": transaction_date_str,
                "transaction_shares": transaction_shares,
                "transaction_price_per_share": transaction_price,
                "transaction_value": transaction_value,
                "shares_owned_before_transaction": shares_before,
                "shares_owned_after_transaction": shares_after,
                "security_title": "Common Stock",
                "filing_date": filing_date_str
            }

            mock_trades.append(trade)

        return [InsiderTrade(**trade) for trade in mock_trades]
    except Exception as e:
        print(f"Error loading mock insider trades for {ticker}: {e}")
        return []

def get_company_news(
    ticker: str,
    end_date: str,
    start_date: Optional[str] = None,
    limit: int = 1000,
) -> List[CompanyNews]:
    """Load mock company news from CSV or generate synthetic data."""
    print(f"Loading mock company news for {ticker}...")

    try:
        # First try to load from CSV if it exists
        csv_path = os.path.join(MOCK_DATA_DIR, f"company_news_{ticker}.csv")
        if os.path.exists(csv_path):
            df = pd.read_csv(csv_path)

            # Filter by date range
            df['date'] = pd.to_datetime(df['date'])
            df = df[df['date'] <= end_date]
            if start_date:
                df = df[df['date'] >= start_date]

            # Sort by date (most recent first)
            df = df.sort_values('date', ascending=False)

            # Limit the number of results
            df = df.head(limit)

            # Convert to CompanyNews objects
            news_items = []
            for row in df.to_dict('records'):
                # Handle NaN values
                for key, value in row.items():
                    if pd.isna(value):
                        row[key] = None
                news_items.append(CompanyNews(**row))

            if news_items:  # Only return if we actually got data
                return news_items

        # If CSV doesn't exist or is empty, generate synthetic data
        # Create mock company news for any ticker
        # Define news templates that can be used for any company
        news_templates = [
            {
                "title": "{ticker} Announces New Product Line, Expanding Market Reach",
                "sentiment": "positive"
            },
            {
                "title": "{ticker} Reports Strong Q1 Earnings, Raises Full-Year Guidance",
                "sentiment": "positive"
            },
            {
                "title": "{ticker} Gains Market Share Against Competitors",
                "sentiment": "positive"
            },
            {
                "title": "Analysts Concerned About {ticker}'s Increasing Expenses",
                "sentiment": "negative"
            },
            {
                "title": "{ticker} Partners with Major Tech Companies for New Solutions",
                "sentiment": "positive"
            },
            {
                "title": "{ticker} Stock Drops After Disappointing Quarterly Results",
                "sentiment": "negative"
            },
            {
                "title": "Investors Bullish on {ticker} Following Product Announcement",
                "sentiment": "positive"
            },
            {
                "title": "{ticker} Faces Regulatory Scrutiny Over Business Practices",
                "sentiment": "negative"
            },
            {
                "title": "{ticker} Expands International Operations with New Facility",
                "sentiment": "positive"
            },
            {
                "title": "Analyst Upgrades {ticker} Stock, Citing Growth Potential",
                "sentiment": "positive"
            }
        ]

        # Define possible authors and sources
        authors = ["John Smith", "Jane Doe", "Michael Johnson", "Sarah Williams", "David Brown", "Emily Chen", "Robert Taylor"]
        sources = ["Bloomberg", "CNBC", "Reuters", "Wall Street Journal", "TechCrunch", "Financial Times", "MarketWatch"]

        # Parse end_date
        end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")

        # Generate dates for news (within last 3 months of end_date)
        news_dates = []
        for i in range(min(limit, 20)):  # Generate up to 20 news items or limit, whichever is smaller
            days_back = random.randint(1, 90)
            news_date = end_date_dt - timedelta(days=days_back)
            news_dates.append(news_date)

        # Sort dates in descending order (most recent first)
        news_dates.sort(reverse=True)

        # Generate mock news
        mock_news = []
        for i in range(len(news_dates)):
            # Randomly select a news template
            template = random.choice(news_templates)

            # Format the title with the ticker
            title = template["title"].format(ticker=ticker)

            # Generate other news details
            author = random.choice(authors)
            source = random.choice(sources)

            # Format date
            news_date = news_dates[i]
            date_str = news_date.strftime("%Y-%m-%dT%H:%M:%SZ")  # Ensure date is a string

            # Create the news item
            news = {
                "ticker": ticker,
                "title": title,
                "author": author,
                "source": source,
                "date": date_str,
                "url": f"https://example.com/{ticker.lower()}-{i}",
                "sentiment": template["sentiment"]
            }

            mock_news.append(news)
        return [CompanyNews(**news) for news in mock_news]
    except Exception as e:
        print(f"Error loading mock company news for {ticker}: {e}")
        return []

def get_market_cap(
    ticker: str,
    end_date: str,
) -> Optional[float]:
    """Get market cap from mock financial metrics."""
    metrics = get_financial_metrics(ticker, end_date)
    if not metrics:
        return None

    return metrics[0].market_cap

def prices_to_df(prices: List[Price]) -> pd.DataFrame:
    """Convert prices to a DataFrame."""
    df = pd.DataFrame([p.model_dump() for p in prices])
    df["Date"] = pd.to_datetime(df["time"])
    df.set_index("Date", inplace=True)
    numeric_cols = ["open", "close", "high", "low", "volume"]
    for col in numeric_cols:
        df[col] = pd.to_numeric(df[col], errors="coerce")
    df.sort_index(inplace=True)
    return df

def get_price_data(ticker: str, start_date: str, end_date: str) -> pd.DataFrame:
    """Get price data as DataFrame."""
    prices = get_prices(ticker, start_date, end_date)
    return prices_to_df(prices)
