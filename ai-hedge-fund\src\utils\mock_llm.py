"""
Mock LLM module that provides pre-defined responses for portfolio management.
"""

import random

# Sample detailed reasoning templates for different actions
BUY_REASONING_TEMPLATES = [
    "Received a bullish signal from the {analyst} with {confidence}% confidence. The portfolio currently holds {position} shares of {ticker}. Initiating a buy position based on the strong positive sentiment and technical indicators. The risk management assessment allows for a position of up to {limit} shares, so we're allocating {quantity} shares to this opportunity while maintaining a diversified portfolio.",
    
    "The {analyst} has provided a bullish outlook for {ticker} with {confidence}% confidence, suggesting potential upside in the near term. Given the current portfolio position of {position} shares, increasing exposure to {quantity} shares aligns with our strategy. Risk management parameters indicate a maximum position of {limit} shares, ensuring this trade remains within our risk tolerance guidelines.",
    
    "Based on the bullish signal from the {analyst} with {confidence}% confidence, we're establishing a long position in {ticker}. The portfolio currently has {position} shares, and we're adding {quantity} more to capitalize on the positive outlook. Risk assessment shows we can allocate up to {limit} shares while maintaining appropriate diversification and risk exposure levels."
]

SELL_REASONING_TEMPLATES = [
    "Received a bearish signal from the {analyst} with {confidence}% confidence. The portfolio currently holds {position} shares of {ticker}. Initiating a sell position based on the negative sentiment indicators. The risk management assessment suggests limiting exposure, so we're reducing our position by {quantity} shares to mitigate potential downside risk.",
    
    "The {analyst} has provided a bearish outlook for {ticker} with {confidence}% confidence, indicating potential downside risk. Given the current portfolio position of {position} shares, reducing exposure by {quantity} shares aligns with our risk management strategy. This adjustment helps protect capital while maintaining a balanced portfolio allocation.",
    
    "Based on the bearish signal from the {analyst} with {confidence}% confidence, we're reducing our position in {ticker}. The portfolio currently has {position} shares, and we're selling {quantity} shares to limit exposure to anticipated negative price movement. This decision aligns with our risk management framework while preserving some exposure in case the bearish outlook proves temporary."
]

SHORT_REASONING_TEMPLATES = [
    "Received a bearish signal from the {analyst} with {confidence}% confidence. The portfolio currently holds no position in {ticker}. Initiating a short position based on the strong negative sentiment indicators. The specified margin requirement is {margin}, meaning margin usage is not a constraint for this trade based on the provided rules. Sizing the short position based on the max_shares limit ({quantity}) as a risk guideline.",
    
    "The {analyst} has provided a bearish outlook for {ticker} with {confidence}% confidence, suggesting potential downside in the near term. Given that we currently hold no position, establishing a short position of {quantity} shares allows us to capitalize on the expected price decline. Risk parameters indicate this position size is appropriate given our margin requirements of {margin} and available capital.",
    
    "Based on the bearish signal from the {analyst} with {confidence}% confidence, we're establishing a short position in {ticker}. The portfolio has no current exposure, making this an opportune time to benefit from the anticipated price decrease. With a margin requirement of {margin}, we can comfortably maintain a short position of {quantity} shares while staying within our risk management guidelines."
]

HOLD_REASONING_TEMPLATES = [
    "The signals for {ticker} are mixed or neutral, with {confidence}% confidence in the current assessment. The portfolio currently holds {position} shares. Given the lack of a clear directional signal, maintaining the current position is the prudent approach. Risk management parameters support this conservative stance while we await more definitive market signals.",
    
    "With conflicting signals from our analysis of {ticker} and a confidence level of only {confidence}%, the optimal strategy is to hold our current position of {position} shares. This neutral stance allows us to preserve capital while avoiding unnecessary transaction costs in an uncertain environment. Our risk assessment supports maintaining the status quo until a clearer trend emerges.",
    
    "The analysis for {ticker} shows balanced bullish and bearish indicators with {confidence}% confidence, suggesting a sideways market in the near term. The portfolio's current position of {position} shares represents an appropriate exposure given this outlook. Risk management considerations support holding rather than adjusting our position in this period of market uncertainty."
]

def mock_call_llm(prompt, model_name=None, model_provider=None, temperature=0.0, max_tokens=None):
    """
    Mock LLM function that returns a pre-defined response based on the prompt content.
    """
    # Extract key information from the prompt
    ticker = None
    action = None
    quantity = 0
    confidence = 50.0
    position = 0
    limit = 20000
    margin = 0.0
    analyst = "Mock Sentiment Analyst"
    
    # Parse the prompt to extract information
    if "ticker" in prompt:
        # Extract ticker
        ticker_line = [line for line in prompt.split("\n") if "decision for" in line]
        if ticker_line:
            ticker = ticker_line[0].split("decision for")[1].strip().split(" ")[0]
    
    if "preliminary decision is to" in prompt:
        # Extract action and quantity
        decision_line = [line for line in prompt.split("\n") if "preliminary decision is to" in line]
        if decision_line:
            parts = decision_line[0].split("preliminary decision is to")[1].strip().split(" ")
            action = parts[0].lower()
            if len(parts) > 1 and parts[1].isdigit():
                quantity = int(parts[1])
    
    if "confidence" in prompt:
        # Extract confidence
        confidence_line = [line for line in prompt.split("\n") if "confidence" in line]
        if confidence_line:
            for line in confidence_line:
                if "%" in line:
                    try:
                        confidence = float(line.split("%")[0].split(" ")[-1])
                        break
                    except:
                        pass
    
    if "Current Position:" in prompt:
        # Extract current position
        position_line = [line for line in prompt.split("\n") if "Current Position:" in line]
        if position_line:
            try:
                position = int(position_line[0].split("Current Position:")[1].strip().split(" ")[0])
            except:
                position = 0
    
    # Select a template based on the action
    if action == "BUY" or action == "buy":
        template = random.choice(BUY_REASONING_TEMPLATES)
    elif (action == "SELL" or action == "sell") and position <= 0:
        template = random.choice(SHORT_REASONING_TEMPLATES)
    elif action == "SELL" or action == "sell":
        template = random.choice(SELL_REASONING_TEMPLATES)
    else:
        template = random.choice(HOLD_REASONING_TEMPLATES)
    
    # Fill in the template
    reasoning = template.format(
        analyst=analyst,
        confidence=confidence,
        ticker=ticker or "the stock",
        position=position,
        quantity=quantity,
        limit=limit,
        margin=margin
    )
    
    return reasoning
