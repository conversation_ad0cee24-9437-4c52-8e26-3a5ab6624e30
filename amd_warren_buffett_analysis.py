"""
<PERSON><PERSON><PERSON> to run <PERSON> Buffett analysis on AMD using mock data.
This script copies the warren_buffett_agent code from the AI Hedge Fund project
but uses mock API data for AMD.
"""

import sys
import os
import json
from colorama import Fore, Style, init
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any, Literal

# Initialize colorama
init(autoreset=True)

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Import the mock API functions directly
from src.tools.mock_api import (
    get_financial_metrics,
    search_line_items,
    get_market_cap
)

# Import necessary functions from the project
from src.utils.progress import progress

print(f"{Fore.GREEN}Successfully imported mock API functions{Style.RESET_ALL}")

# Define the WarrenBuffettSignal model
class WarrenBuffettSignal(BaseModel):
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(..., ge=0, le=100)
    reasoning: Dict[str, Any]

def analyze_fundamentals(metrics: list) -> dict:
    """Analyze company fundamentals based on Buffett's criteria."""
    if not metrics:
        return {"score": 0, "details": "Insufficient fundamental data"}

    latest_metrics = metrics[0]

    score = 0
    reasoning = []

    # Check ROE (Return on Equity)
    if latest_metrics.return_on_equity and latest_metrics.return_on_equity > 0.15:  # 15% ROE threshold
        score += 2
        reasoning.append(f"Strong ROE of {latest_metrics.return_on_equity:.1%}")
    elif latest_metrics.return_on_equity:
        reasoning.append(f"Weak ROE of {latest_metrics.return_on_equity:.1%}")
    else:
        reasoning.append("ROE data not available")

    # Check Debt to Equity
    if latest_metrics.debt_to_equity and latest_metrics.debt_to_equity < 0.5:  # Low debt
        score += 2
        reasoning.append(f"Low debt-to-equity ratio of {latest_metrics.debt_to_equity:.1f}")
    elif latest_metrics.debt_to_equity and latest_metrics.debt_to_equity < 1.0:  # Moderate debt
        score += 1
        reasoning.append(f"Moderate debt-to-equity ratio of {latest_metrics.debt_to_equity:.1f}")
    elif latest_metrics.debt_to_equity:
        reasoning.append(f"High debt-to-equity ratio of {latest_metrics.debt_to_equity:.1f}")
    else:
        reasoning.append("Debt-to-equity data not available")

    # Check Net Margin
    if latest_metrics.net_margin and latest_metrics.net_margin > 0.20:  # Strong margins
        score += 2
        reasoning.append(f"Excellent profit margins of {latest_metrics.net_margin:.1%}")
    elif latest_metrics.net_margin and latest_metrics.net_margin > 0.10:  # Decent margins
        score += 1
        reasoning.append(f"Good profit margins of {latest_metrics.net_margin:.1%}")
    elif latest_metrics.net_margin:
        reasoning.append(f"Weak profit margins of {latest_metrics.net_margin:.1%}")
    else:
        reasoning.append("Profit margin data not available")

    # Check Current Ratio
    if latest_metrics.current_ratio and latest_metrics.current_ratio > 1.5:
        score += 1
        reasoning.append("Good liquidity position")
    elif latest_metrics.current_ratio:
        reasoning.append(f"Weak liquidity with current ratio of {latest_metrics.current_ratio:.1f}")
    else:
        reasoning.append("Current ratio data not available")

    return {"score": score, "details": "; ".join(reasoning), "metrics": latest_metrics.model_dump()}


def analyze_consistency(financial_line_items: list) -> dict:
    """Analyze earnings consistency and growth."""
    if len(financial_line_items) < 4:  # Need at least 4 periods for trend analysis
        return {"score": 0, "details": "Insufficient historical data"}

    score = 0
    details = []

    # Check for consistent earnings growth
    net_incomes = []
    for item in financial_line_items:
        if hasattr(item, "net_income") and item.net_income is not None:
            net_incomes.append(item.net_income)

    if len(net_incomes) >= 4:
        # Check if earnings are consistently increasing
        is_increasing = all(net_incomes[i] <= net_incomes[i-1] for i in range(1, len(net_incomes)))
        if is_increasing:
            score += 3
            details.append("Consistent earnings growth over multiple periods")
        else:
            # Check if at least the trend is positive
            if net_incomes[0] > net_incomes[-1]:
                score += 1
                details.append("Overall positive earnings trend despite some fluctuations")
            else:
                details.append("Inconsistent or declining earnings")
    else:
        details.append("Insufficient earnings history")

    # Check for consistent free cash flow
    fcfs = []
    for item in financial_line_items:
        if hasattr(item, "free_cash_flow") and item.free_cash_flow is not None:
            fcfs.append(item.free_cash_flow)

    if len(fcfs) >= 4:
        # Check if FCF is consistently positive and growing
        is_positive = all(fcf > 0 for fcf in fcfs)
        is_growing = all(fcfs[i] <= fcfs[i-1] for i in range(1, len(fcfs)))
        
        if is_positive and is_growing:
            score += 2
            details.append("Strong and growing free cash flow")
        elif is_positive:
            score += 1
            details.append("Positive but fluctuating free cash flow")
        else:
            details.append("Inconsistent or negative free cash flow")
    else:
        details.append("Insufficient free cash flow history")

    return {"score": score, "details": "; ".join(details)}


def analyze_moat(metrics: list) -> dict:
    """
    Evaluate whether the company likely has a durable competitive advantage (moat).
    For simplicity, we look at stability of ROE/operating margins over multiple periods
    or high margin over the last few years. Higher stability => higher moat score.
    """
    if not metrics or len(metrics) < 3:
        return {"score": 0, "max_score": 3, "details": "Insufficient data for moat analysis"}

    reasoning = []
    moat_score = 0
    historical_roes = []
    historical_margins = []

    for m in metrics:
        if m.return_on_equity is not None:
            historical_roes.append(m.return_on_equity)
        if m.operating_margin is not None:
            historical_margins.append(m.operating_margin)

    # Check ROE stability and level
    if len(historical_roes) >= 3:
        avg_roe = sum(historical_roes) / len(historical_roes)
        roe_variance = sum((roe - avg_roe) ** 2 for roe in historical_roes) / len(historical_roes)
        
        # High and stable ROE is a sign of moat
        if avg_roe > 0.15 and roe_variance < 0.0025:  # Less than 5% standard deviation
            moat_score += 2
            reasoning.append(f"High and stable ROE averaging {avg_roe:.1%}")
        elif avg_roe > 0.12:
            moat_score += 1
            reasoning.append(f"Good ROE averaging {avg_roe:.1%}")
        else:
            reasoning.append(f"Average ROE of {avg_roe:.1%} is not exceptional")
    else:
        reasoning.append("Insufficient ROE history")

    # Check margin stability and level
    if len(historical_margins) >= 3:
        avg_margin = sum(historical_margins) / len(historical_margins)
        margin_variance = sum((margin - avg_margin) ** 2 for margin in historical_margins) / len(historical_margins)
        
        # High and stable margins are a sign of moat
        if avg_margin > 0.20 and margin_variance < 0.0025:
            moat_score += 1
            reasoning.append(f"High and stable operating margins averaging {avg_margin:.1%}")
        elif avg_margin > 0.15:
            moat_score += 0.5
            reasoning.append(f"Good operating margins averaging {avg_margin:.1%}")
        else:
            reasoning.append(f"Average operating margin of {avg_margin:.1%} is not exceptional")
    else:
        reasoning.append("Insufficient margin history")

    return {
        "score": moat_score,
        "max_score": 3,
        "details": "; ".join(reasoning),
    }


def analyze_management_quality(financial_line_items: list) -> dict:
    """
    Checks for share dilution or consistent buybacks, and some dividend track record.
    A simplified approach:
      - if there's net share repurchase or stable share count, it suggests management
        might be shareholder-friendly.
      - if there's a big new issuance, it might be a negative sign (dilution).
    """
    if not financial_line_items:
        return {"score": 0, "max_score": 2, "details": "Insufficient data for management analysis"}

    reasoning = []
    mgmt_score = 0

    latest = financial_line_items[0]
    if hasattr(latest, "issuance_or_purchase_of_equity_shares") and latest.issuance_or_purchase_of_equity_shares and latest.issuance_or_purchase_of_equity_shares < 0:
        # Negative means the company spent money on buybacks
        mgmt_score += 1
        reasoning.append("Company has been repurchasing shares (shareholder-friendly)")

    if hasattr(latest, "issuance_or_purchase_of_equity_shares") and latest.issuance_or_purchase_of_equity_shares and latest.issuance_or_purchase_of_equity_shares > 0:
        # Positive issuance means new shares => possible dilution
        reasoning.append("Recent common stock issuance (potential dilution)")
    else:
        reasoning.append("No significant new stock issuance detected")

    # Check for any dividends
    if hasattr(latest, "dividends_and_other_cash_distributions") and latest.dividends_and_other_cash_distributions and latest.dividends_and_other_cash_distributions < 0:
        mgmt_score += 1
        reasoning.append("Company has a track record of paying dividends")
    else:
        reasoning.append("No or minimal dividends paid")

    return {
        "score": mgmt_score,
        "max_score": 2,
        "details": "; ".join(reasoning),
    }


def calculate_owner_earnings(financial_line_items: list) -> dict:
    """Calculate owner earnings (Buffett's preferred measure of true earnings power).
    Owner Earnings = Net Income + Depreciation - Maintenance CapEx"""
    if not financial_line_items or len(financial_line_items) < 1:
        return {"owner_earnings": None, "details": ["Insufficient data for owner earnings calculation"]}

    latest = financial_line_items[0]

    net_income = latest.net_income if hasattr(latest, "net_income") else None
    depreciation = latest.depreciation_and_amortization if hasattr(latest, "depreciation_and_amortization") else None
    capex = latest.capital_expenditure if hasattr(latest, "capital_expenditure") else None

    if not all([net_income, depreciation, capex]):
        return {"owner_earnings": None, "details": ["Missing components for owner earnings calculation"]}

    # Estimate maintenance capex (typically 70-80% of total capex)
    maintenance_capex = capex * 0.75
    owner_earnings = net_income + depreciation - maintenance_capex

    return {
        "owner_earnings": owner_earnings,
        "components": {"net_income": net_income, "depreciation": depreciation, "maintenance_capex": maintenance_capex},
        "details": ["Owner earnings calculated successfully"],
    }


def calculate_intrinsic_value(financial_line_items: list) -> dict:
    """Calculate intrinsic value using DCF based on owner earnings."""
    # First calculate owner earnings
    owner_earnings_result = calculate_owner_earnings(financial_line_items)
    
    if not owner_earnings_result["owner_earnings"]:
        return {"intrinsic_value": None, "details": owner_earnings_result["details"]}
    
    owner_earnings = owner_earnings_result["owner_earnings"]
    
    # Buffett's DCF assumptions (conservative approach)
    growth_rate = 0.05  # Conservative 5% growth
    discount_rate = 0.09  # Typical ~9% discount rate
    terminal_multiple = 12
    projection_years = 10

    # Sum of discounted future owner earnings
    future_value = 0
    for year in range(1, projection_years + 1):
        future_earnings = owner_earnings * (1 + growth_rate) ** year
        present_value = future_earnings / (1 + discount_rate) ** year
        future_value += present_value

    # Terminal value
    terminal_value = (owner_earnings * (1 + growth_rate) ** projection_years * terminal_multiple) / ((1 + discount_rate) ** projection_years)

    intrinsic_value = future_value + terminal_value

    return {
        "intrinsic_value": intrinsic_value,
        "owner_earnings": owner_earnings,
        "assumptions": {
            "growth_rate": growth_rate,
            "discount_rate": discount_rate,
            "terminal_multiple": terminal_multiple,
            "projection_years": projection_years,
        },
        "details": ["Intrinsic value calculated using DCF model with owner earnings"],
    }


def warren_buffett_analysis(ticker="AMD", end_date="2025-04-09"):
    """
    Run Warren Buffett analysis on a ticker using the same logic as the warren_buffett_agent
    but with mock data for AMD.
    """
    print(f"\nRunning Warren Buffett analysis for {Fore.CYAN}{ticker}{Style.RESET_ALL}...")
    
    progress.update_status("warren_buffett_analysis", ticker, "Fetching financial metrics")
    # Fetch required data
    metrics = get_financial_metrics(ticker, end_date, period="ttm", limit=5)

    progress.update_status("warren_buffett_analysis", ticker, "Gathering financial line items")
    financial_line_items = search_line_items(
        ticker,
        [
            "capital_expenditure",
            "depreciation_and_amortization",
            "net_income",
            "outstanding_shares",
            "total_assets",
            "total_liabilities",
            "dividends_and_other_cash_distributions",
            "issuance_or_purchase_of_equity_shares",
        ],
        end_date,
    )

    progress.update_status("warren_buffett_analysis", ticker, "Getting market cap")
    # Get current market cap
    market_cap = get_market_cap(ticker, end_date)

    progress.update_status("warren_buffett_analysis", ticker, "Analyzing fundamentals")
    # Analyze fundamentals
    fundamental_analysis = analyze_fundamentals(metrics)

    progress.update_status("warren_buffett_analysis", ticker, "Analyzing consistency")
    consistency_analysis = analyze_consistency(financial_line_items)

    progress.update_status("warren_buffett_analysis", ticker, "Analyzing moat")
    moat_analysis = analyze_moat(metrics)

    progress.update_status("warren_buffett_analysis", ticker, "Analyzing management quality")
    mgmt_analysis = analyze_management_quality(financial_line_items)

    progress.update_status("warren_buffett_analysis", ticker, "Calculating intrinsic value")
    intrinsic_value_analysis = calculate_intrinsic_value(financial_line_items)

    # Calculate total score
    total_score = fundamental_analysis["score"] + consistency_analysis["score"] + moat_analysis["score"] + mgmt_analysis["score"]
    max_possible_score = 10 + moat_analysis["max_score"] + mgmt_analysis["max_score"]
    # fundamental_analysis + consistency combined were up to 10 points total
    # moat can add up to 3, mgmt can add up to 2, for example

    # Calculate margin of safety
    margin_of_safety = None
    if intrinsic_value_analysis["intrinsic_value"] and market_cap:
        # Calculate per-share values
        shares_outstanding = financial_line_items[0].outstanding_shares if hasattr(financial_line_items[0], "outstanding_shares") else None
        
        if shares_outstanding:
            intrinsic_value_per_share = intrinsic_value_analysis["intrinsic_value"] / shares_outstanding
            current_price = market_cap / shares_outstanding
            margin_of_safety = (intrinsic_value_per_share - current_price) / intrinsic_value_per_share

    # Generate trading signal using a stricter margin-of-safety requirement
    # if fundamentals+moat+management are strong but margin_of_safety < 0.3, it's neutral
    # if fundamentals are very weak or margin_of_safety is severely negative -> bearish
    # else bullish
    if (total_score >= 0.7 * max_possible_score) and margin_of_safety and (margin_of_safety >= 0.3):
        signal = "bullish"
    elif total_score <= 0.3 * max_possible_score or (margin_of_safety is not None and margin_of_safety < -0.3):
        # negative margin of safety beyond -30% could be overpriced -> bearish
        signal = "bearish"
    else:
        signal = "neutral"

    # Combine all analysis results
    analysis_data = {
        "signal": signal,
        "score": total_score,
        "max_score": max_possible_score,
        "fundamental_analysis": fundamental_analysis,
        "consistency_analysis": consistency_analysis,
        "moat_analysis": moat_analysis,
        "management_analysis": mgmt_analysis,
        "intrinsic_value_analysis": intrinsic_value_analysis,
        "market_cap": market_cap,
        "margin_of_safety": margin_of_safety,
    }

    # Calculate confidence based on score and margin of safety
    confidence = (total_score / max_possible_score) * 100
    
    # Adjust confidence based on margin of safety
    if margin_of_safety is not None:
        if margin_of_safety > 0.5:  # Very high margin of safety
            confidence = min(confidence + 20, 100)
        elif margin_of_safety > 0.3:  # Good margin of safety
            confidence = min(confidence + 10, 100)
        elif margin_of_safety < -0.3:  # Negative margin of safety
            confidence = max(confidence - 20, 0)
    
    # Create the final output
    buffett_analysis = {
        "signal": signal,
        "confidence": round(confidence, 1),
        "reasoning": {
            "fundamental_score": f"{fundamental_analysis['score']} points: {fundamental_analysis['details']}",
            "consistency_score": f"{consistency_analysis['score']} points: {consistency_analysis['details']}",
            "moat_score": f"{moat_analysis['score']} points: {moat_analysis['details']}",
            "management_score": f"{mgmt_analysis['score']} points: {mgmt_analysis['details']}",
            "intrinsic_value": f"${intrinsic_value_analysis['intrinsic_value'] / 1e9:.1f}B" if intrinsic_value_analysis['intrinsic_value'] else "Not calculable",
            "margin_of_safety": f"{margin_of_safety:.1%}" if margin_of_safety is not None else "Not calculable",
            "total_score": f"{total_score} out of {max_possible_score} points",
        }
    }

    progress.update_status("warren_buffett_analysis", ticker, "Done")

    # Print the analysis
    print(f"\n{Fore.GREEN}Warren Buffett Analysis for {ticker}:{Style.RESET_ALL}")
    print(f"Signal: {Fore.CYAN if signal == 'bullish' else Fore.RED if signal == 'bearish' else Fore.YELLOW}{signal.upper()}{Style.RESET_ALL}")
    print(f"Confidence: {buffett_analysis['confidence']}%")
    
    # Print the reasoning
    print(f"\n{Fore.GREEN}Reasoning:{Style.RESET_ALL}")
    for category, details in buffett_analysis["reasoning"].items():
        print(f"- {category}: {details}")
    
    # Save the analysis to a file
    with open(f"{ticker}_warren_buffett_analysis.json", "w") as f:
        json.dump(buffett_analysis, f, indent=2)
    
    print(f"\nAnalysis saved to {Fore.GREEN}{ticker}_warren_buffett_analysis.json{Style.RESET_ALL}")
    
    return buffett_analysis

if __name__ == "__main__":
    # Run the analysis for AMD
    warren_buffett_analysis()
