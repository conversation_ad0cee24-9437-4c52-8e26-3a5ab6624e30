import json

# Load the extracted data
with open('RELIANCE_NS_screener_data/raw_extracted_data.json', 'r') as f:
    data = json.load(f)

print("=" * 80)
print("CHECKING EXTRACTED TABLES FOR RATIO ANALYSIS FORMAT")
print("=" * 80)

# Look for tables with "Leverage", "Efficiency", "Profitability", etc.
ratio_keywords = ["Leverage", "Efficiency", "Profitability", "Capital Allocation", "Valuation"]

for i, table in enumerate(data['tables']):
    headers = table['headers']
    table_data = table['data']
    
    # Check if this table contains ratio data
    for keyword in ratio_keywords:
        if any(keyword in str(header) for header in headers) or \
           any(keyword in str(row[0]) if row else False for row in table_data):
            
            print(f"\n📊 TABLE {i}: Found {keyword} Ratios")
            print(f"Headers: {headers}")
            print(f"Rows: {len(table_data)}")
            
            # Print first few rows to see the structure
            for j, row in enumerate(table_data[:5]):
                print(f"  Row {j}: {row}")
            
            if len(table_data) > 5:
                print(f"  ... and {len(table_data) - 5} more rows")
            
            break

# Also check for tables with year columns (Mar 2014, Mar 2015, etc.)
print(f"\n" + "=" * 80)
print("TABLES WITH YEAR COLUMNS (ANNUAL DATA)")
print("=" * 80)

for i, table in enumerate(data['tables']):
    headers = table['headers']
    
    # Check for year patterns
    year_count = sum(1 for header in headers if 'Mar 20' in str(header))
    
    if year_count >= 5:  # Tables with multiple years
        print(f"\n📅 TABLE {i}: {year_count} year columns")
        print(f"Headers: {headers}")
        
        # Show first few data rows
        for j, row in enumerate(table['data'][:3]):
            if row and len(row) > 0:
                print(f"  {row[0]}: {row[1:4]}...")  # Show first metric and first 3 values

print(f"\n" + "=" * 80)
print("SUMMARY")
print("=" * 80)
print(f"Total tables extracted: {len(data['tables'])}")
print(f"Tables with year data: {sum(1 for table in data['tables'] if sum(1 for header in table['headers'] if 'Mar 20' in str(header)) >= 5)}")
