"""
Corrected Financial Metrics Calculator
Based on validation against AAPL data, this version uses proper formulas and formats.
"""

import yfinance as yf
import pandas as pd
import json
import os
from datetime import datetime
import warnings
import numpy as np
warnings.filterwarnings('ignore')

class CorrectedFinancialCalculator:
    def __init__(self, symbol):
        self.symbol = symbol
        self.ticker = yf.Ticker(symbol)
        
        # Fetch data
        print(f"Fetching {symbol} data from Yahoo Finance...")
        self.info = self.ticker.info
        self.financials = self.ticker.financials
        self.quarterly_financials = self.ticker.quarterly_financials
        self.balance_sheet = self.ticker.balance_sheet
        self.quarterly_balance_sheet = self.ticker.quarterly_balance_sheet
        self.cashflow = self.ticker.cashflow
        self.quarterly_cashflow = self.ticker.quarterly_cashflow
        
        # Get historical market data for accurate market cap calculation
        self.hist = self.ticker.history(period="2y")
        
        print(f"✓ Loaded {symbol} data successfully")
        print(f"Available quarterly periods: {len(self.quarterly_financials.columns) if not self.quarterly_financials.empty else 0}")
    
    def safe_divide(self, numerator, denominator, default=0.0):
        """Safely divide two numbers."""
        try:
            if pd.isna(numerator) or pd.isna(denominator) or denominator == 0:
                return default
            result = float(numerator) / float(denominator)
            return result if not pd.isna(result) else default
        except:
            return default
    
    def get_financial_item(self, df, item_names, period_col, default=0.0):
        """Get financial item with multiple possible names."""
        if df.empty or period_col not in df.columns:
            return default
        
        # Handle both single string and list of strings
        if isinstance(item_names, str):
            item_names = [item_names]
        
        # Try each possible name
        for item_name in item_names:
            # Try exact match
            if item_name in df.index:
                value = df.loc[item_name, period_col]
                if not pd.isna(value):
                    return float(value)
            
            # Try partial matches
            for idx in df.index:
                if item_name.lower() in idx.lower():
                    value = df.loc[idx, period_col]
                    if not pd.isna(value):
                        return float(value)
        
        return default
    
    def calculate_ttm_value(self, df, item_names, start_index=0):
        """Calculate TTM value with proper handling."""
        try:
            if df.empty:
                return 0.0
            
            # Get 4 quarters starting from start_index
            ttm_value = 0.0
            quarters_found = 0
            
            for i in range(4):
                if start_index + i < len(df.columns):
                    period_col = df.columns[start_index + i]
                    quarter_value = self.get_financial_item(df, item_names, period_col)
                    ttm_value += quarter_value
                    if quarter_value != 0:
                        quarters_found += 1
            
            # If we don't have 4 quarters, extrapolate from available quarters
            if quarters_found > 0 and quarters_found < 4:
                ttm_value = ttm_value * (4 / quarters_found)
            
            return ttm_value
        except:
            return 0.0
    
    def get_market_cap_for_period(self, period_date):
        """Get market cap for a specific period using historical price data."""
        try:
            # Get shares outstanding (assume relatively constant)
            shares = self.info.get('sharesOutstanding', 0)
            if shares == 0:
                return self.info.get('marketCap', 0)
            
            # Find closest price to the period date
            period_date = pd.to_datetime(period_date)
            
            # Get price closest to period date
            if not self.hist.empty:
                hist_dates = pd.to_datetime(self.hist.index)
                closest_idx = (hist_dates - period_date).abs().idxmin()
                price = self.hist.loc[closest_idx, 'Close']
                return float(shares * price)
            
            # Fallback to current market cap
            return self.info.get('marketCap', 0)
        except:
            return self.info.get('marketCap', 0)
    
    def calculate_growth_rate(self, current_value, previous_value):
        """Calculate growth rate in decimal format."""
        if previous_value == 0 or pd.isna(previous_value) or pd.isna(current_value):
            return 0.0
        return (current_value - previous_value) / abs(previous_value)
    
    def calculate_metrics_for_period(self, period_col, period_index=0):
        """Calculate corrected financial metrics for a period."""
        print(f"Calculating corrected metrics for {period_col}")
        
        # Get market cap for this specific period
        market_cap = self.get_market_cap_for_period(period_col)
        shares_outstanding = self.info.get('sharesOutstanding', 0)
        
        # Income Statement Items (using multiple possible names)
        revenue = self.get_financial_item(self.quarterly_financials, 
            ['Total Revenue', 'Revenue', 'Net Sales'], period_col)
        gross_profit = self.get_financial_item(self.quarterly_financials, 
            ['Gross Profit'], period_col)
        operating_income = self.get_financial_item(self.quarterly_financials, 
            ['Operating Income', 'Operating Revenue'], period_col)
        ebitda = self.get_financial_item(self.quarterly_financials, 
            ['EBITDA', 'Normalized EBITDA'], period_col)
        ebit = self.get_financial_item(self.quarterly_financials, 
            ['EBIT', 'Earnings Before Interest and Tax'], period_col)
        net_income = self.get_financial_item(self.quarterly_financials, 
            ['Net Income', 'Net Income Common Stockholders'], period_col)
        interest_expense = self.get_financial_item(self.quarterly_financials, 
            ['Interest Expense', 'Interest Expense Non Operating', 'Net Interest Income'], period_col)
        
        # Balance Sheet Items
        total_assets = self.get_financial_item(self.quarterly_balance_sheet, 
            ['Total Assets'], period_col)
        current_assets = self.get_financial_item(self.quarterly_balance_sheet, 
            ['Current Assets'], period_col)
        cash_and_equivalents = self.get_financial_item(self.quarterly_balance_sheet, 
            ['Cash And Cash Equivalents', 'Cash Cash Equivalents And Short Term Investments'], period_col)
        inventory = self.get_financial_item(self.quarterly_balance_sheet, 
            ['Inventory'], period_col)
        accounts_receivable = self.get_financial_item(self.quarterly_balance_sheet, 
            ['Accounts Receivable', 'Receivables'], period_col)
        
        current_liabilities = self.get_financial_item(self.quarterly_balance_sheet, 
            ['Current Liabilities'], period_col)
        total_debt = self.get_financial_item(self.quarterly_balance_sheet, 
            ['Total Debt'], period_col)
        stockholders_equity = self.get_financial_item(self.quarterly_balance_sheet, 
            ['Stockholders Equity', 'Total Equity Gross Minority Interest'], period_col)
        
        # Cash Flow Items
        operating_cash_flow = self.get_financial_item(self.quarterly_cashflow, 
            ['Operating Cash Flow', 'Cash Flow From Continuing Operating Activities'], period_col)
        free_cash_flow = self.get_financial_item(self.quarterly_cashflow, 
            ['Free Cash Flow'], period_col)
        capital_expenditure = self.get_financial_item(self.quarterly_cashflow, 
            ['Capital Expenditure'], period_col)
        
        # Calculate TTM values for ratios
        ttm_revenue = self.calculate_ttm_value(self.quarterly_financials, 
            ['Total Revenue', 'Revenue'], period_index)
        ttm_net_income = self.calculate_ttm_value(self.quarterly_financials, 
            ['Net Income', 'Net Income Common Stockholders'], period_index)
        ttm_operating_income = self.calculate_ttm_value(self.quarterly_financials, 
            ['Operating Income'], period_index)
        ttm_ebitda = self.calculate_ttm_value(self.quarterly_financials, 
            ['EBITDA'], period_index)
        ttm_free_cash_flow = self.calculate_ttm_value(self.quarterly_cashflow, 
            ['Free Cash Flow'], period_index)
        
        # Calculate enterprise value
        enterprise_value = market_cap + total_debt - cash_and_equivalents
        
        # Growth calculations (compare with previous quarter)
        revenue_growth = 0.0
        earnings_growth = 0.0
        if period_index < len(self.quarterly_financials.columns) - 1:
            prev_period = self.quarterly_financials.columns[period_index + 1]
            prev_revenue = self.get_financial_item(self.quarterly_financials, 
                ['Total Revenue'], prev_period)
            prev_net_income = self.get_financial_item(self.quarterly_financials, 
                ['Net Income'], prev_period)
            
            revenue_growth = self.calculate_growth_rate(revenue, prev_revenue)
            earnings_growth = self.calculate_growth_rate(net_income, prev_net_income)
        
        # Calculate all metrics in correct format (decimals, not percentages)
        metrics = {
            'ticker': self.symbol,
            'report_period': str(period_col)[:10] if hasattr(period_col, 'strftime') else str(period_col),
            'fiscal_period': f"Q{((pd.to_datetime(period_col).month - 1) // 3) + 1}" if hasattr(period_col, 'strftime') else 'Q1',
            'period': 'ttm',  # Match AAPL format
            'currency': 'USD' if self.symbol == 'AAPL' else 'INR',
            
            # Market Valuation Metrics
            'market_cap': float(market_cap),
            'enterprise_value': float(enterprise_value),
            'price_to_earnings_ratio': self.safe_divide(market_cap, ttm_net_income),
            'price_to_book_ratio': self.safe_divide(market_cap, stockholders_equity),
            'price_to_sales_ratio': self.safe_divide(market_cap, ttm_revenue),
            'enterprise_value_to_ebitda_ratio': self.safe_divide(enterprise_value, ttm_ebitda),
            'enterprise_value_to_revenue_ratio': self.safe_divide(enterprise_value, ttm_revenue),
            
            # Profitability Margins (in decimal format)
            'gross_margin': self.safe_divide(gross_profit, revenue),
            'operating_margin': self.safe_divide(operating_income, revenue),
            'net_margin': self.safe_divide(net_income, revenue),
            
            # Return Ratios (in decimal format)
            'return_on_equity': self.safe_divide(ttm_net_income, stockholders_equity),
            'return_on_assets': self.safe_divide(ttm_net_income, total_assets),
            'return_on_invested_capital': self.safe_divide(ttm_operating_income, (stockholders_equity + total_debt)),
            
            # Efficiency Ratios
            'asset_turnover': self.safe_divide(ttm_revenue, total_assets),
            'inventory_turnover': self.safe_divide(ttm_revenue, inventory) if inventory > 0 else 0,
            'receivables_turnover': self.safe_divide(ttm_revenue, accounts_receivable) if accounts_receivable > 0 else 0,
            
            # Liquidity Ratios
            'current_ratio': self.safe_divide(current_assets, current_liabilities),
            'quick_ratio': self.safe_divide((current_assets - inventory), current_liabilities),
            'cash_ratio': self.safe_divide(cash_and_equivalents, current_liabilities),
            'operating_cash_flow_ratio': self.safe_divide(operating_cash_flow, current_liabilities),
            
            # Leverage Ratios
            'debt_to_equity': self.safe_divide(total_debt, stockholders_equity),
            'debt_to_assets': self.safe_divide(total_debt, total_assets),
            'interest_coverage': self.safe_divide(ebit, abs(interest_expense)) if interest_expense != 0 else 0,
            
            # Per Share Metrics
            'earnings_per_share': self.safe_divide(net_income, shares_outstanding) if shares_outstanding > 0 else 0,
            'book_value_per_share': self.safe_divide(stockholders_equity, shares_outstanding) if shares_outstanding > 0 else 0,
            'free_cash_flow_per_share': self.safe_divide(free_cash_flow, shares_outstanding) if shares_outstanding > 0 else 0,
            
            # Growth Metrics (in decimal format)
            'revenue_growth': revenue_growth,
            'earnings_growth': earnings_growth,
            'book_value_growth': 0.0,  # Would need more historical data
            'earnings_per_share_growth': 0.0,  # Would need more historical data
            'free_cash_flow_growth': 0.0,  # Would need more historical data
            'operating_income_growth': 0.0,  # Would need more historical data
            'ebitda_growth': 0.0,  # Would need more historical data
            
            # Additional Metrics
            'free_cash_flow_yield': self.safe_divide(ttm_free_cash_flow, market_cap),
            'operating_cycle': self.safe_divide(365, self.safe_divide(ttm_revenue, inventory)) + self.safe_divide(365, self.safe_divide(ttm_revenue, accounts_receivable)) if inventory > 0 and accounts_receivable > 0 else 0,
            'days_sales_outstanding': self.safe_divide(accounts_receivable * 365, ttm_revenue) if ttm_revenue > 0 else 0,
            'working_capital_turnover': self.safe_divide(ttm_revenue, (current_assets - current_liabilities)) if (current_assets - current_liabilities) != 0 else 0,
            'peg_ratio': self.safe_divide(self.safe_divide(market_cap, ttm_net_income), abs(earnings_growth)) if earnings_growth != 0 else 0,
            'payout_ratio': 0.0,  # Would need dividend data
        }
        
        return metrics
    
    def calculate_all_periods(self, max_periods=10):
        """Calculate metrics for all available periods."""
        print(f"\nCalculating corrected financial metrics for {self.symbol}...")
        
        if self.quarterly_financials.empty:
            print("No quarterly financial data available")
            return pd.DataFrame()
        
        all_metrics = []
        num_periods = min(max_periods, len(self.quarterly_financials.columns))
        
        for i in range(num_periods):
            period_col = self.quarterly_financials.columns[i]
            try:
                metrics = self.calculate_metrics_for_period(period_col, i)
                if metrics:
                    all_metrics.append(metrics)
                    print(f"✓ Calculated corrected metrics for {period_col}")
            except Exception as e:
                print(f"✗ Error calculating metrics for {period_col}: {e}")
        
        if not all_metrics:
            return pd.DataFrame()
        
        return pd.DataFrame(all_metrics)

def test_corrected_formulas():
    """Test corrected formulas on AAPL first."""
    print("=" * 80)
    print("TESTING CORRECTED FORMULAS ON AAPL")
    print("=" * 80)
    
    # Calculate AAPL with corrected formulas
    calculator = CorrectedFinancialCalculator("AAPL")
    corrected_aapl = calculator.calculate_all_periods(6)
    
    if corrected_aapl.empty:
        print("❌ Failed to calculate corrected AAPL metrics")
        return None
    
    # Save corrected AAPL metrics
    corrected_aapl.to_csv("corrected_aapl_metrics.csv", index=False)
    print(f"💾 Saved corrected AAPL metrics to corrected_aapl_metrics.csv")
    
    return corrected_aapl

def apply_to_reliance():
    """Apply corrected formulas to RELIANCE.NS."""
    print("\n" + "=" * 80)
    print("APPLYING CORRECTED FORMULAS TO RELIANCE.NS")
    print("=" * 80)
    
    calculator = CorrectedFinancialCalculator("RELIANCE.NS")
    reliance_metrics = calculator.calculate_all_periods(10)
    
    if reliance_metrics.empty:
        print("❌ Failed to calculate RELIANCE metrics")
        return None
    
    # Save to hedge fund data directory
    output_dir = "RELIANCE_NS_hedge_fund_data"
    os.makedirs(output_dir, exist_ok=True)
    
    output_path = os.path.join(output_dir, "financial_metrics_RELIANCE_CORRECTED.csv")
    reliance_metrics.to_csv(output_path, index=False)
    print(f"💾 Saved corrected RELIANCE metrics to {output_path}")
    
    # Print summary
    print(f"\n📊 RELIANCE.NS Corrected Metrics Summary:")
    print(f"  Periods calculated: {len(reliance_metrics)}")
    print(f"  Metrics per period: {len(reliance_metrics.columns)}")
    
    if len(reliance_metrics) > 0:
        latest = reliance_metrics.iloc[0]
        print(f"\n📈 Latest Period ({latest['report_period']}) Key Metrics:")
        print(f"  P/E Ratio: {latest['price_to_earnings_ratio']:.2f}")
        print(f"  ROE: {latest['return_on_equity']:.3f} ({latest['return_on_equity']*100:.1f}%)")
        print(f"  ROA: {latest['return_on_assets']:.3f} ({latest['return_on_assets']*100:.1f}%)")
        print(f"  Current Ratio: {latest['current_ratio']:.2f}")
        print(f"  Debt/Equity: {latest['debt_to_equity']:.2f}")
        print(f"  Net Margin: {latest['net_margin']:.3f} ({latest['net_margin']*100:.1f}%)")
        print(f"  Revenue Growth: {latest['revenue_growth']:.3f} ({latest['revenue_growth']*100:.1f}%)")
    
    return reliance_metrics

def main():
    """Main function."""
    # Test corrected formulas on AAPL first
    corrected_aapl = test_corrected_formulas()
    
    if corrected_aapl is not None:
        print("\n✅ AAPL corrected formulas completed successfully")
        
        # Apply to RELIANCE.NS
        reliance_metrics = apply_to_reliance()
        
        if reliance_metrics is not None:
            print("\n🎯 SUCCESS: Corrected financial metrics calculated for both AAPL and RELIANCE.NS")
            print("📊 Data is now ready for validation against existing AAPL data")
        else:
            print("\n❌ Failed to calculate RELIANCE metrics")
    else:
        print("\n❌ Failed to calculate corrected AAPL metrics")

if __name__ == "__main__":
    main()
