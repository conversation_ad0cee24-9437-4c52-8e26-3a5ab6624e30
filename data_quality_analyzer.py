#!/usr/bin/env python3
"""
Data Quality Analyzer
Compares the comprehensive NSE data extraction with AMD format and analyzes completeness.
"""

import pandas as pd
import json
import os
from datetime import datetime

class DataQualityAnalyzer:
    def __init__(self):
        self.amd_data = {}
        self.reliance_data = {}
        self.analysis_results = {}
        
    def load_amd_reference_data(self):
        """Load all AMD reference data types."""
        try:
            amd_files = {
                'financial_metrics': 'mock_financial_data/financial_metrics_AMD.csv',
                'line_items': 'mock_financial_data/line_items_AMD.csv',
                'prices': 'mock_financial_data/prices_AMD.csv',
                'company_news': 'mock_financial_data/company_news_AMD.csv',
                'insider_trades': 'mock_financial_data/insider_trades_AMD.csv'
            }
            
            for data_type, file_path in amd_files.items():
                if os.path.exists(file_path):
                    self.amd_data[data_type] = pd.read_csv(file_path)
                    print(f"✅ Loaded AMD {data_type}: {len(self.amd_data[data_type])} records")
                else:
                    print(f"⚠️ AMD {data_type} file not found: {file_path}")
            
            return True
        except Exception as e:
            print(f"❌ Failed to load AMD reference data: {e}")
            return False
    
    def load_reliance_extracted_data(self):
        """Load all RELIANCE extracted data types."""
        try:
            reliance_files = {
                'financial_metrics': 'RELIANCE_NS_complete_data/financial_metrics_RELIANCE.csv',
                'line_items': 'RELIANCE_NS_complete_data/line_items_RELIANCE.csv',
                'prices': 'RELIANCE_NS_complete_data/prices_RELIANCE.csv',
                'company_news': 'RELIANCE_NS_complete_data/company_news_RELIANCE.csv',
                'insider_trades': 'RELIANCE_NS_complete_data/insider_trades_RELIANCE.csv'
            }
            
            for data_type, file_path in reliance_files.items():
                if os.path.exists(file_path):
                    self.reliance_data[data_type] = pd.read_csv(file_path)
                    print(f"✅ Loaded RELIANCE {data_type}: {len(self.reliance_data[data_type])} records")
                else:
                    print(f"⚠️ RELIANCE {data_type} file not found: {file_path}")
            
            return True
        except Exception as e:
            print(f"❌ Failed to load RELIANCE extracted data: {e}")
            return False
    
    def analyze_financial_metrics_quality(self):
        """Analyze financial metrics data quality and completeness."""
        print(f"\n📊 ANALYZING FINANCIAL METRICS QUALITY")
        print("="*60)
        
        if 'financial_metrics' not in self.amd_data or 'financial_metrics' not in self.reliance_data:
            print("❌ Missing financial metrics data")
            return
        
        amd_fm = self.amd_data['financial_metrics']
        rel_fm = self.reliance_data['financial_metrics']
        
        # Column comparison
        amd_columns = set(amd_fm.columns)
        rel_columns = set(rel_fm.columns)
        
        common_columns = amd_columns.intersection(rel_columns)
        missing_columns = amd_columns - rel_columns
        extra_columns = rel_columns - amd_columns
        
        print(f"📋 Column Analysis:")
        print(f"  AMD columns: {len(amd_columns)}")
        print(f"  RELIANCE columns: {len(rel_columns)}")
        print(f"  Common columns: {len(common_columns)} ({len(common_columns)/len(amd_columns)*100:.1f}%)")
        
        if missing_columns:
            print(f"  ❌ Missing: {missing_columns}")
        if extra_columns:
            print(f"  ➕ Extra: {extra_columns}")
        
        # Data completeness analysis
        print(f"\n📈 Data Completeness Analysis:")
        
        # Analyze latest period
        latest_amd = amd_fm.iloc[0]
        latest_rel = rel_fm.iloc[0]
        
        # Count non-zero numeric fields
        amd_numeric_fields = [col for col in latest_amd.index if isinstance(latest_amd[col], (int, float))]
        rel_numeric_fields = [col for col in latest_rel.index if isinstance(latest_rel[col], (int, float))]
        
        amd_non_zero = sum(1 for col in amd_numeric_fields if latest_amd[col] != 0)
        rel_non_zero = sum(1 for col in rel_numeric_fields if latest_rel[col] != 0)
        
        print(f"  AMD non-zero fields: {amd_non_zero}/{len(amd_numeric_fields)} ({amd_non_zero/len(amd_numeric_fields)*100:.1f}%)")
        print(f"  RELIANCE non-zero fields: {rel_non_zero}/{len(rel_numeric_fields)} ({rel_non_zero/len(rel_numeric_fields)*100:.1f}%)")
        
        # Key metrics comparison
        key_metrics = ['market_cap', 'price_to_earnings_ratio', 'return_on_equity', 'debt_to_equity', 'current_ratio']
        
        print(f"\n🎯 Key Metrics Comparison (Latest Period):")
        for metric in key_metrics:
            if metric in latest_amd.index and metric in latest_rel.index:
                amd_val = latest_amd[metric]
                rel_val = latest_rel[metric]
                status = "✅" if rel_val != 0 else "❌"
                print(f"  {status} {metric}: AMD={amd_val}, RELIANCE={rel_val}")
        
        # Historical data analysis
        print(f"\n📅 Historical Data:")
        print(f"  AMD periods: {len(amd_fm)}")
        print(f"  RELIANCE periods: {len(rel_fm)}")
        
        self.analysis_results['financial_metrics'] = {
            'column_match_percentage': len(common_columns)/len(amd_columns)*100,
            'data_completeness_percentage': rel_non_zero/len(rel_numeric_fields)*100,
            'total_periods': len(rel_fm),
            'missing_columns': list(missing_columns),
            'status': 'excellent' if len(common_columns)/len(amd_columns) > 0.9 else 'good'
        }
    
    def analyze_all_data_types(self):
        """Analyze all data types comprehensively."""
        print(f"\n🔍 COMPREHENSIVE DATA TYPE ANALYSIS")
        print("="*60)
        
        data_types = ['financial_metrics', 'line_items', 'prices', 'company_news', 'insider_trades']
        
        for data_type in data_types:
            print(f"\n📊 {data_type.upper()}:")
            
            amd_available = data_type in self.amd_data
            rel_available = data_type in self.reliance_data
            
            if amd_available and rel_available:
                amd_count = len(self.amd_data[data_type])
                rel_count = len(self.reliance_data[data_type])
                
                print(f"  ✅ Both available - AMD: {amd_count}, RELIANCE: {rel_count}")
                
                # Column comparison
                amd_cols = set(self.amd_data[data_type].columns)
                rel_cols = set(self.reliance_data[data_type].columns)
                common_cols = amd_cols.intersection(rel_cols)
                
                print(f"  📋 Column match: {len(common_cols)}/{len(amd_cols)} ({len(common_cols)/len(amd_cols)*100:.1f}%)")
                
                if amd_cols - rel_cols:
                    print(f"  ❌ Missing columns: {amd_cols - rel_cols}")
                
                self.analysis_results[data_type] = {
                    'available': True,
                    'record_count': rel_count,
                    'column_match_percentage': len(common_cols)/len(amd_cols)*100,
                    'status': 'complete'
                }
                
            elif rel_available:
                rel_count = len(self.reliance_data[data_type])
                print(f"  ✅ RELIANCE available: {rel_count} records")
                print(f"  ⚠️ AMD reference not available")
                
                self.analysis_results[data_type] = {
                    'available': True,
                    'record_count': rel_count,
                    'status': 'extracted_only'
                }
                
            else:
                print(f"  ❌ Not available")
                self.analysis_results[data_type] = {
                    'available': False,
                    'status': 'missing'
                }
    
    def calculate_overall_score(self):
        """Calculate overall data quality score."""
        print(f"\n🏆 OVERALL DATA QUALITY SCORE")
        print("="*60)
        
        scores = {
            'financial_metrics': 0,
            'line_items': 0,
            'prices': 0,
            'company_news': 0,
            'insider_trades': 0
        }
        
        weights = {
            'financial_metrics': 0.4,  # Most important
            'line_items': 0.2,
            'prices': 0.2,
            'company_news': 0.1,
            'insider_trades': 0.1
        }
        
        for data_type, result in self.analysis_results.items():
            if result.get('available', False):
                if result.get('column_match_percentage', 0) > 90:
                    scores[data_type] = 100
                elif result.get('column_match_percentage', 0) > 80:
                    scores[data_type] = 85
                elif result.get('record_count', 0) > 0:
                    scores[data_type] = 70
                else:
                    scores[data_type] = 50
            else:
                scores[data_type] = 0
        
        # Calculate weighted score
        overall_score = sum(scores[dt] * weights[dt] for dt in scores.keys())
        
        print(f"📊 Individual Scores:")
        for data_type, score in scores.items():
            weight = weights[data_type]
            weighted = score * weight
            print(f"  {data_type}: {score}/100 (weight: {weight:.1f}, contribution: {weighted:.1f})")
        
        print(f"\n🎯 Overall Score: {overall_score:.1f}/100")
        
        if overall_score >= 90:
            grade = "A+ (Excellent)"
        elif overall_score >= 80:
            grade = "A (Very Good)"
        elif overall_score >= 70:
            grade = "B (Good)"
        elif overall_score >= 60:
            grade = "C (Fair)"
        else:
            grade = "D (Needs Improvement)"
        
        print(f"🏅 Grade: {grade}")
        
        return overall_score, grade
    
    def generate_improvement_recommendations(self):
        """Generate recommendations for data quality improvement."""
        print(f"\n💡 IMPROVEMENT RECOMMENDATIONS")
        print("="*60)
        
        recommendations = []
        
        # Check financial metrics
        fm_result = self.analysis_results.get('financial_metrics', {})
        if fm_result.get('data_completeness_percentage', 0) < 80:
            recommendations.append("🔧 Improve financial metrics completeness by extracting more fields from screener.in raw tables")
        
        if fm_result.get('missing_columns'):
            recommendations.append("📊 Add missing financial metrics columns through Yahoo Finance or calculations")
        
        # Check other data types
        for data_type in ['line_items', 'prices', 'company_news']:
            if not self.analysis_results.get(data_type, {}).get('available', False):
                recommendations.append(f"📈 Implement {data_type} extraction from Yahoo Finance")
        
        # Insider trades
        if not self.analysis_results.get('insider_trades', {}).get('available', False):
            recommendations.append("👥 Add insider trading data source (SEC filings or specialized API)")
        
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                print(f"{i}. {rec}")
        else:
            print("🎉 No major improvements needed - data quality is excellent!")
    
    def save_analysis_report(self):
        """Save comprehensive analysis report."""
        report = {
            "analysis_date": datetime.now().isoformat(),
            "data_quality_analysis": self.analysis_results,
            "summary": {
                "total_data_types": len(self.analysis_results),
                "available_data_types": sum(1 for r in self.analysis_results.values() if r.get('available', False)),
                "overall_score": self.calculate_overall_score()[0],
                "grade": self.calculate_overall_score()[1]
            },
            "data_sources": {
                "screener_in": "Primary source for financial ratios and metrics",
                "yahoo_finance": "Market data, prices, news, financial statements"
            },
            "next_steps": [
                "Test with additional NSE stocks (HDFCBANK, TCS, INFY)",
                "Integrate with AI hedge fund models",
                "Implement automated data refresh",
                "Add more data sources for completeness"
            ]
        }
        
        with open('RELIANCE_NS_data_quality_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n💾 Analysis report saved to: RELIANCE_NS_data_quality_report.json")
        return report

def main():
    """Main analysis function."""
    print("="*80)
    print("NSE DATA QUALITY ANALYZER")
    print("="*80)
    
    analyzer = DataQualityAnalyzer()
    
    # Load reference and extracted data
    if not analyzer.load_amd_reference_data():
        return
    
    if not analyzer.load_reliance_extracted_data():
        return
    
    # Perform comprehensive analysis
    analyzer.analyze_financial_metrics_quality()
    analyzer.analyze_all_data_types()
    
    # Calculate scores and recommendations
    overall_score, grade = analyzer.calculate_overall_score()
    analyzer.generate_improvement_recommendations()
    
    # Save report
    report = analyzer.save_analysis_report()
    
    print(f"\n" + "="*80)
    print("ANALYSIS COMPLETE!")
    print("="*80)
    print(f"🎯 Overall Data Quality: {overall_score:.1f}/100 ({grade})")
    print(f"📊 Available Data Types: {report['summary']['available_data_types']}/{report['summary']['total_data_types']}")
    print(f"💾 Detailed report saved for review")

if __name__ == "__main__":
    main()
