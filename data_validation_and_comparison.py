"""
Data Validation and Comparison Script
Validates the generated RELIANCE.NS data against AAPL structure and provides quality assessment.
"""

import pandas as pd
import json
import os
from pathlib import Path
import numpy as np

def load_aapl_data():
    """Load AAPL data structure for comparison."""
    aapl_data = {}
    financial_data_dir = Path("financial_data")
    
    if financial_data_dir.exists():
        for file in financial_data_dir.glob("*AAPL*"):
            if file.suffix == '.csv':
                try:
                    df = pd.read_csv(file)
                    file_key = file.stem.replace('_AAPL', '')
                    aapl_data[file_key] = df
                    print(f"✓ Loaded AAPL {file_key}: {len(df)} records, {len(df.columns)} columns")
                except Exception as e:
                    print(f"✗ Error loading {file.name}: {e}")
    
    return aapl_data

def load_reliance_data():
    """Load generated RELIANCE.NS data."""
    reliance_data = {}
    reliance_dir = Path("RELIANCE_NS_hedge_fund_data")
    
    if reliance_dir.exists():
        for file in reliance_dir.glob("*.csv"):
            try:
                df = pd.read_csv(file)
                file_key = file.stem.replace('_RELIANCE', '')
                reliance_data[file_key] = df
                print(f"✓ Loaded RELIANCE {file_key}: {len(df)} records, {len(df.columns)} columns")
            except Exception as e:
                print(f"✗ Error loading {file.name}: {e}")
    
    return reliance_data

def compare_data_structures(aapl_data, reliance_data):
    """Compare data structures between AAPL and RELIANCE."""
    print("\n" + "=" * 80)
    print("DATA STRUCTURE COMPARISON")
    print("=" * 80)
    
    comparison_results = {}
    
    for category in aapl_data.keys():
        if category in reliance_data:
            aapl_df = aapl_data[category]
            reliance_df = reliance_data[category]
            
            # Column comparison
            aapl_cols = set(aapl_df.columns)
            reliance_cols = set(reliance_df.columns)
            
            common_cols = aapl_cols & reliance_cols
            missing_cols = aapl_cols - reliance_cols
            extra_cols = reliance_cols - aapl_cols
            
            coverage = len(common_cols) / len(aapl_cols) * 100 if aapl_cols else 0
            
            comparison_results[category] = {
                'status': 'AVAILABLE',
                'aapl_records': len(aapl_df),
                'reliance_records': len(reliance_df),
                'aapl_columns': len(aapl_cols),
                'reliance_columns': len(reliance_cols),
                'common_columns': len(common_cols),
                'missing_columns': list(missing_cols),
                'extra_columns': list(extra_cols),
                'column_coverage': coverage
            }
            
            print(f"\n📊 {category.upper()}:")
            print(f"  Records: AAPL={len(aapl_df)}, RELIANCE={len(reliance_df)}")
            print(f"  Columns: AAPL={len(aapl_cols)}, RELIANCE={len(reliance_cols)}")
            print(f"  Coverage: {coverage:.1f}% ({len(common_cols)}/{len(aapl_cols)} columns)")
            
            if missing_cols:
                print(f"  Missing: {list(missing_cols)[:5]}{'...' if len(missing_cols) > 5 else ''}")
            if extra_cols:
                print(f"  Extra: {list(extra_cols)[:5]}{'...' if len(extra_cols) > 5 else ''}")
        else:
            comparison_results[category] = {
                'status': 'MISSING',
                'aapl_records': len(aapl_data[category]),
                'aapl_columns': len(aapl_data[category].columns)
            }
            print(f"\n❌ {category.upper()}: MISSING")
    
    return comparison_results

def validate_financial_metrics(reliance_data):
    """Validate the quality of financial metrics data."""
    print("\n" + "=" * 80)
    print("FINANCIAL METRICS VALIDATION")
    print("=" * 80)
    
    if 'financial_metrics' not in reliance_data:
        print("❌ Financial metrics data not found")
        return {}
    
    df = reliance_data['financial_metrics']
    
    # Basic validation
    validation_results = {
        'total_records': len(df),
        'total_columns': len(df.columns),
        'periods_covered': df['report_period'].nunique(),
        'data_quality': {}
    }
    
    print(f"📊 Basic Statistics:")
    print(f"  Total records: {len(df)}")
    print(f"  Total columns: {len(df.columns)}")
    print(f"  Periods covered: {df['report_period'].nunique()}")
    print(f"  Date range: {df['report_period'].min()} to {df['report_period'].max()}")
    
    # Key metrics validation
    key_metrics = [
        'price_to_earnings_ratio', 'return_on_equity', 'return_on_assets',
        'current_ratio', 'debt_to_equity', 'net_margin', 'revenue_growth'
    ]
    
    print(f"\n🔍 Key Metrics Analysis:")
    for metric in key_metrics:
        if metric in df.columns:
            values = df[metric].dropna()
            if len(values) > 0:
                avg_val = values.mean()
                min_val = values.min()
                max_val = values.max()
                non_zero = (values != 0).sum()
                
                validation_results['data_quality'][metric] = {
                    'average': float(avg_val),
                    'min': float(min_val),
                    'max': float(max_val),
                    'non_zero_count': int(non_zero),
                    'data_points': len(values)
                }
                
                print(f"  {metric}:")
                print(f"    Average: {avg_val:.2f}")
                print(f"    Range: {min_val:.2f} to {max_val:.2f}")
                print(f"    Non-zero values: {non_zero}/{len(values)}")
    
    # Check for missing data
    missing_data = df.isnull().sum()
    high_missing = missing_data[missing_data > len(df) * 0.5]
    
    if len(high_missing) > 0:
        print(f"\n⚠️  Columns with >50% missing data:")
        for col, missing_count in high_missing.items():
            print(f"    {col}: {missing_count}/{len(df)} ({missing_count/len(df)*100:.1f}%)")
    
    return validation_results

def validate_data_consistency(reliance_data):
    """Validate data consistency across different files."""
    print("\n" + "=" * 80)
    print("DATA CONSISTENCY VALIDATION")
    print("=" * 80)
    
    consistency_results = {}
    
    # Check if ticker symbols are consistent
    tickers = set()
    for category, df in reliance_data.items():
        if 'ticker' in df.columns:
            tickers.update(df['ticker'].unique())
    
    print(f"📋 Ticker symbols found: {tickers}")
    
    # Check date consistency between files
    date_columns = ['report_period', 'date', 'time']
    date_ranges = {}
    
    for category, df in reliance_data.items():
        for date_col in date_columns:
            if date_col in df.columns:
                try:
                    dates = pd.to_datetime(df[date_col], errors='coerce').dropna()
                    if len(dates) > 0:
                        date_ranges[f"{category}_{date_col}"] = {
                            'min_date': dates.min(),
                            'max_date': dates.max(),
                            'count': len(dates)
                        }
                except:
                    pass
    
    print(f"\n📅 Date ranges by category:")
    for key, info in date_ranges.items():
        print(f"  {key}: {info['min_date'].strftime('%Y-%m-%d')} to {info['max_date'].strftime('%Y-%m-%d')} ({info['count']} records)")
    
    consistency_results['tickers'] = list(tickers)
    consistency_results['date_ranges'] = {k: {
        'min_date': v['min_date'].isoformat(),
        'max_date': v['max_date'].isoformat(),
        'count': v['count']
    } for k, v in date_ranges.items()}
    
    return consistency_results

def generate_compatibility_report(comparison_results, validation_results, consistency_results):
    """Generate final compatibility report."""
    print("\n" + "=" * 80)
    print("AI HEDGE FUND COMPATIBILITY REPORT")
    print("=" * 80)
    
    # Calculate overall compatibility score
    total_categories = len(comparison_results)
    available_categories = sum(1 for r in comparison_results.values() if r['status'] == 'AVAILABLE')
    
    # Calculate average column coverage
    coverage_scores = [r['column_coverage'] for r in comparison_results.values() if r['status'] == 'AVAILABLE']
    avg_coverage = np.mean(coverage_scores) if coverage_scores else 0
    
    compatibility_score = (available_categories / total_categories) * (avg_coverage / 100) * 100
    
    print(f"🎯 OVERALL COMPATIBILITY SCORE: {compatibility_score:.1f}%")
    print(f"")
    print(f"📊 Detailed Breakdown:")
    print(f"  Categories available: {available_categories}/{total_categories} ({available_categories/total_categories*100:.1f}%)")
    print(f"  Average column coverage: {avg_coverage:.1f}%")
    print(f"  Data quality: {'GOOD' if validation_results.get('total_records', 0) > 0 else 'POOR'}")
    
    # Status by category
    print(f"\n📋 Category Status:")
    for category, result in comparison_results.items():
        if result['status'] == 'AVAILABLE':
            coverage = result['column_coverage']
            status_icon = "✅" if coverage >= 80 else "⚠️" if coverage >= 60 else "❌"
            print(f"  {status_icon} {category}: {coverage:.1f}% coverage ({result['reliance_records']} records)")
        else:
            print(f"  ❌ {category}: MISSING")
    
    # Recommendations
    print(f"\n🔧 Recommendations:")
    
    missing_categories = [cat for cat, result in comparison_results.items() if result['status'] == 'MISSING']
    if missing_categories:
        print(f"  1. Implement missing categories: {', '.join(missing_categories)}")
    
    low_coverage = [cat for cat, result in comparison_results.items() 
                   if result['status'] == 'AVAILABLE' and result['column_coverage'] < 80]
    if low_coverage:
        print(f"  2. Improve column coverage for: {', '.join(low_coverage)}")
    
    if validation_results.get('total_records', 0) == 0:
        print(f"  3. Generate actual data (currently using placeholders)")
    
    print(f"  4. Implement real-time data updates")
    print(f"  5. Add data quality monitoring")
    
    # Final assessment
    if compatibility_score >= 90:
        assessment = "EXCELLENT - Ready for production use"
    elif compatibility_score >= 75:
        assessment = "GOOD - Minor improvements needed"
    elif compatibility_score >= 60:
        assessment = "FAIR - Moderate improvements needed"
    else:
        assessment = "POOR - Major improvements required"
    
    print(f"\n🏆 FINAL ASSESSMENT: {assessment}")
    
    return {
        'compatibility_score': compatibility_score,
        'available_categories': available_categories,
        'total_categories': total_categories,
        'average_coverage': avg_coverage,
        'assessment': assessment,
        'comparison_results': comparison_results,
        'validation_results': validation_results,
        'consistency_results': consistency_results
    }

def main():
    """Main validation function."""
    print("=" * 80)
    print("RELIANCE.NS DATA VALIDATION AND COMPATIBILITY ASSESSMENT")
    print("=" * 80)
    
    # Load data
    print("\n📂 Loading data...")
    aapl_data = load_aapl_data()
    reliance_data = load_reliance_data()
    
    if not aapl_data:
        print("❌ No AAPL data found for comparison")
        return
    
    if not reliance_data:
        print("❌ No RELIANCE data found for validation")
        return
    
    # Perform comparisons and validations
    comparison_results = compare_data_structures(aapl_data, reliance_data)
    validation_results = validate_financial_metrics(reliance_data)
    consistency_results = validate_data_consistency(reliance_data)
    
    # Generate final report
    final_report = generate_compatibility_report(comparison_results, validation_results, consistency_results)
    
    # Save report
    report_path = "RELIANCE_NS_compatibility_report.json"
    with open(report_path, 'w') as f:
        json.dump(final_report, f, indent=2, default=str)
    
    print(f"\n💾 Detailed report saved to: {report_path}")

if __name__ == "__main__":
    main()
