#!/usr/bin/env python3
"""
Enhanced Data Integrator
Combines screener.in data with Yahoo Finance to create complete AMD-format dataset
"""

import pandas as pd
import json
import yfinance as yf
import os
import re
from datetime import datetime
import asyncio

class EnhancedDataIntegrator:
    def __init__(self):
        self.raw_tables = []
        self.yahoo_data = None
        self.final_dataset = None
        
    def load_raw_screener_data(self):
        """Load raw screener tables."""
        try:
            with open('RELIANCE_NS_screener_data/raw_extracted_data.json', 'r') as f:
                data = json.load(f)
            self.raw_tables = data.get('tables', [])
            print(f"✅ Loaded {len(self.raw_tables)} raw tables from screener.in")
            return True
        except Exception as e:
            print(f"❌ Failed to load raw screener data: {e}")
            return False
    
    def extract_comprehensive_ratios(self, symbol="RELIANCE"):
        """Extract all available ratios from raw tables."""
        print(f"\n📊 Extracting comprehensive ratios for {symbol}...")
        
        # Enhanced ratio mapping with multiple search terms
        ratio_mappings = {
            # Valuation Ratios
            'price_to_earnings_ratio': ['pe ratio', 'p/e', 'price/earnings', 'price to earnings'],
            'price_to_book_ratio': ['pb ratio', 'p/b', 'price/book', 'price to book'],
            'price_to_sales_ratio': ['ps ratio', 'p/s', 'price/sales', 'price to sales'],
            'enterprise_value_to_ebitda_ratio': ['ev/ebitda', 'enterprise value/ebitda'],
            'enterprise_value_to_revenue_ratio': ['ev/sales', 'ev/revenue', 'enterprise value/sales'],
            'enterprise_value': ['enterprise value'],
            'market_cap': ['market cap', 'market capitalisation'],
            'peg_ratio': ['peg ratio', 'peg'],
            
            # Profitability Ratios
            'gross_margin': ['gross margin', 'gross profit margin'],
            'operating_margin': ['operating margin', 'operating profit margin', 'ebit margin'],
            'net_margin': ['net margin', 'net profit margin', 'profit margin'],
            'ebitda_margin': ['ebitda margin'],
            'return_on_equity': ['roe', 'return on equity'],
            'return_on_assets': ['roa', 'return on assets'],
            'return_on_invested_capital': ['roic', 'roce', 'return on invested capital', 'return on capital employed'],
            
            # Efficiency Ratios
            'asset_turnover': ['asset turnover', 'total asset turnover'],
            'inventory_turnover': ['inventory turnover'],
            'receivables_turnover': ['receivables turnover', 'receivable turnover'],
            'days_sales_outstanding': ['receivable days', 'dso', 'days sales outstanding'],
            'operating_cycle': ['operating cycle'],
            'working_capital_turnover': ['working capital turnover'],
            
            # Liquidity Ratios
            'current_ratio': ['current ratio'],
            'quick_ratio': ['quick ratio', 'acid test ratio'],
            'cash_ratio': ['cash ratio'],
            'operating_cash_flow_ratio': ['operating cash flow ratio'],
            
            # Leverage Ratios
            'debt_to_equity': ['debt/equity', 'debt to equity', 'debt equity ratio'],
            'debt_to_assets': ['debt/assets', 'debt to assets', 'debt ratio'],
            'interest_coverage': ['interest coverage', 'interest coverage ratio', 'times interest earned'],
            
            # Growth Ratios
            'revenue_growth': ['revenue growth', 'sales growth', 'yoy sales growth', 'compounded sales growth'],
            'earnings_growth': ['earnings growth', 'profit growth', 'yoy profit growth', 'compounded profit growth'],
            'earnings_per_share_growth': ['eps growth', 'earnings per share growth'],
            'book_value_growth': ['book value growth'],
            'free_cash_flow_growth': ['fcf growth', 'free cash flow growth'],
            'operating_income_growth': ['operating income growth', 'ebit growth'],
            'ebitda_growth': ['ebitda growth'],
            
            # Per Share Metrics
            'earnings_per_share': ['eps', 'earnings per share'],
            'book_value_per_share': ['bvps', 'book value per share'],
            'free_cash_flow_per_share': ['fcf per share', 'free cash flow per share'],
            
            # Other Ratios
            'free_cash_flow_yield': ['fcf yield', 'free cash flow yield'],
            'payout_ratio': ['payout ratio', 'dividend payout ratio'],
        }
        
        # Extract data for each year
        extracted_data = {}
        
        for table_idx, table in enumerate(self.raw_tables):
            table_data = table.get('data', [])
            if not table_data or len(table_data) < 2:
                continue
            
            headers = table_data[0]
            
            # Find year columns
            year_columns = []
            for i, header in enumerate(headers):
                if re.search(r'Mar 20\d{2}', str(header)):
                    year_columns.append((i, header))
            
            if not year_columns:
                continue
            
            print(f"  📋 Processing Table {table_idx}: {len(year_columns)} years, {len(table_data)-1} ratios")
            
            # Process each ratio row
            for row in table_data[1:]:
                if not row or len(row) == 0:
                    continue
                
                ratio_name = str(row[0]).lower().strip()
                
                # Find matching ratio
                matched_ratio = None
                for target_ratio, search_terms in ratio_mappings.items():
                    for search_term in search_terms:
                        if search_term in ratio_name:
                            matched_ratio = target_ratio
                            break
                    if matched_ratio:
                        break
                
                if not matched_ratio:
                    continue
                
                # Extract values for each year
                for col_idx, year_header in year_columns:
                    if col_idx < len(row):
                        value = self.clean_numeric_value(row[col_idx])
                        
                        if year_header not in extracted_data:
                            extracted_data[year_header] = {
                                'ticker': f'{symbol}.NS',
                                'report_period': year_header,
                                'fiscal_period': year_header,
                                'period': 'annual',
                                'currency': 'INR'
                            }
                        
                        extracted_data[year_header][matched_ratio] = value
        
        print(f"✅ Extracted data for {len(extracted_data)} periods")
        return extracted_data
    
    def clean_numeric_value(self, value_str):
        """Clean and convert string values to numeric."""
        if not value_str or value_str in ['-', 'N/A', 'NA', '', 'nil', 'Nil', '--']:
            return 0.0
        
        try:
            cleaned = str(value_str).strip()
            is_percentage = '%' in cleaned
            
            # Remove common characters
            cleaned = re.sub(r'[,%₹$\s]', '', cleaned)
            
            # Handle negative values in parentheses
            if '(' in cleaned and ')' in cleaned:
                cleaned = '-' + cleaned.replace('(', '').replace(')', '')
            
            # Handle multipliers
            multiplier = 1
            if 'cr' in cleaned.lower() or 'crore' in cleaned.lower():
                cleaned = re.sub(r'(cr|crore)', '', cleaned, flags=re.IGNORECASE)
                multiplier = 10000000
            elif 'l' in cleaned.lower() or 'lakh' in cleaned.lower():
                cleaned = re.sub(r'(l|lakh)', '', cleaned, flags=re.IGNORECASE)
                multiplier = 100000
            
            # Remove any remaining non-numeric characters
            cleaned = re.sub(r'[^0-9.-]', '', cleaned)
            
            if not cleaned or cleaned in ['-', '.']:
                return 0.0
            
            numeric_value = float(cleaned) * multiplier
            
            # Convert percentages to decimals
            if is_percentage:
                numeric_value = numeric_value / 100
            
            return numeric_value
            
        except:
            return 0.0
    
    def fetch_yahoo_finance_data(self, symbol="RELIANCE.NS"):
        """Fetch Yahoo Finance data for missing fields."""
        print(f"\n🌐 Fetching Yahoo Finance data for {symbol}...")
        
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            # Get key financial data
            yahoo_data = {
                'market_cap': info.get('marketCap', 0),
                'shares_outstanding': info.get('sharesOutstanding', 0),
                'enterprise_value': info.get('enterpriseValue', 0),
                'trailing_pe': info.get('trailingPE', 0),
                'price_to_book': info.get('priceToBook', 0),
                'price_to_sales': info.get('priceToSalesTrailing12Months', 0),
                'current_ratio': info.get('currentRatio', 0),
                'quick_ratio': info.get('quickRatio', 0),
                'debt_to_equity': info.get('debtToEquity', 0),
                'return_on_equity': info.get('returnOnEquity', 0),
                'return_on_assets': info.get('returnOnAssets', 0),
                'gross_margins': info.get('grossMargins', 0),
                'operating_margins': info.get('operatingMargins', 0),
                'profit_margins': info.get('profitMargins', 0),
                'revenue_growth': info.get('revenueGrowth', 0),
                'earnings_growth': info.get('earningsGrowth', 0),
                'free_cash_flow': info.get('freeCashflow', 0),
                'operating_cash_flow': info.get('operatingCashflow', 0),
                'book_value': info.get('bookValue', 0),
                'earnings_per_share': info.get('trailingEps', 0),
                'peg_ratio': info.get('pegRatio', 0)
            }
            
            self.yahoo_data = yahoo_data
            print(f"✅ Yahoo Finance data fetched: {len([k for k, v in yahoo_data.items() if v != 0])} non-zero fields")
            return yahoo_data
            
        except Exception as e:
            print(f"❌ Failed to fetch Yahoo Finance data: {e}")
            return {}
    
    def integrate_data(self, screener_data, yahoo_data):
        """Integrate screener.in and Yahoo Finance data."""
        print(f"\n🔗 Integrating screener.in and Yahoo Finance data...")
        
        # Convert screener data to DataFrame
        periods = list(screener_data.keys())
        periods.sort(key=lambda x: self.extract_year_from_period(x), reverse=True)
        
        integrated_data = []
        
        for period in periods:
            period_data = screener_data[period].copy()
            
            # Add Yahoo Finance data where missing
            for yahoo_field, yahoo_value in yahoo_data.items():
                if yahoo_value != 0:
                    # Map Yahoo Finance fields to AMD format
                    amd_field_mapping = {
                        'market_cap': 'market_cap',
                        'enterprise_value': 'enterprise_value',
                        'trailing_pe': 'price_to_earnings_ratio',
                        'price_to_book': 'price_to_book_ratio',
                        'price_to_sales': 'price_to_sales_ratio',
                        'current_ratio': 'current_ratio',
                        'quick_ratio': 'quick_ratio',
                        'debt_to_equity': 'debt_to_equity',
                        'return_on_equity': 'return_on_equity',
                        'return_on_assets': 'return_on_assets',
                        'gross_margins': 'gross_margin',
                        'operating_margins': 'operating_margin',
                        'profit_margins': 'net_margin',
                        'revenue_growth': 'revenue_growth',
                        'earnings_growth': 'earnings_growth',
                        'earnings_per_share': 'earnings_per_share',
                        'peg_ratio': 'peg_ratio',
                        'book_value': 'book_value_per_share'
                    }
                    
                    amd_field = amd_field_mapping.get(yahoo_field)
                    if amd_field and amd_field not in period_data:
                        period_data[amd_field] = yahoo_value
            
            # Calculate missing fields
            self.calculate_missing_fields(period_data, yahoo_data)
            
            integrated_data.append(period_data)
        
        print(f"✅ Integrated data for {len(integrated_data)} periods")
        return integrated_data
    
    def calculate_missing_fields(self, period_data, yahoo_data):
        """Calculate missing fields from available data."""
        try:
            # Calculate enterprise value ratios
            if 'enterprise_value' in period_data and period_data['enterprise_value'] > 0:
                if 'ebitda' in period_data and period_data['ebitda'] > 0:
                    period_data['enterprise_value_to_ebitda_ratio'] = period_data['enterprise_value'] / period_data['ebitda']
                
                if 'revenue' in period_data and period_data['revenue'] > 0:
                    period_data['enterprise_value_to_revenue_ratio'] = period_data['enterprise_value'] / period_data['revenue']
            
            # Calculate free cash flow yield
            if 'free_cash_flow' in yahoo_data and 'market_cap' in period_data:
                if yahoo_data['free_cash_flow'] > 0 and period_data['market_cap'] > 0:
                    period_data['free_cash_flow_yield'] = yahoo_data['free_cash_flow'] / period_data['market_cap']
            
            # Calculate per-share metrics
            shares = yahoo_data.get('shares_outstanding', 0)
            if shares > 0:
                if 'book_value' in yahoo_data and yahoo_data['book_value'] > 0:
                    period_data['book_value_per_share'] = yahoo_data['book_value']
                
                if 'free_cash_flow' in yahoo_data and yahoo_data['free_cash_flow'] > 0:
                    period_data['free_cash_flow_per_share'] = yahoo_data['free_cash_flow'] / shares
            
        except Exception as e:
            print(f"⚠️ Error in calculations: {e}")
    
    def extract_year_from_period(self, period_str):
        """Extract year from period string for sorting."""
        try:
            match = re.search(r'20\d{2}', str(period_str))
            return int(match.group()) if match else 0
        except:
            return 0
    
    def format_to_amd_structure(self, integrated_data):
        """Format to exact AMD structure."""
        print(f"\n📋 Formatting to AMD structure...")
        
        # AMD column order
        amd_columns = [
            'ticker', 'report_period', 'fiscal_period', 'period', 'currency',
            'market_cap', 'enterprise_value', 'price_to_earnings_ratio', 'price_to_book_ratio',
            'price_to_sales_ratio', 'enterprise_value_to_ebitda_ratio', 'enterprise_value_to_revenue_ratio',
            'free_cash_flow_yield', 'peg_ratio', 'gross_margin', 'operating_margin', 'net_margin',
            'return_on_equity', 'return_on_assets', 'return_on_invested_capital', 'asset_turnover',
            'inventory_turnover', 'receivables_turnover', 'days_sales_outstanding', 'operating_cycle',
            'working_capital_turnover', 'current_ratio', 'quick_ratio', 'cash_ratio',
            'operating_cash_flow_ratio', 'debt_to_equity', 'debt_to_assets', 'interest_coverage',
            'revenue_growth', 'earnings_growth', 'book_value_growth', 'earnings_per_share_growth',
            'free_cash_flow_growth', 'operating_income_growth', 'ebitda_growth', 'payout_ratio',
            'earnings_per_share', 'book_value_per_share', 'free_cash_flow_per_share'
        ]
        
        # Ensure all columns exist with default values
        for period_data in integrated_data:
            for col in amd_columns:
                if col not in period_data:
                    period_data[col] = 0.0
        
        # Create DataFrame
        df = pd.DataFrame(integrated_data)
        
        # Reorder columns
        df = df[amd_columns]
        
        print(f"✅ Formatted to AMD structure: {len(df)} rows × {len(df.columns)} columns")
        return df
    
    def save_final_dataset(self, df, symbol="RELIANCE"):
        """Save the final integrated dataset."""
        output_dir = f"{symbol}_NS_final_data"
        os.makedirs(output_dir, exist_ok=True)
        
        # Save main dataset
        csv_path = os.path.join(output_dir, f"financial_metrics_{symbol}_FINAL_INTEGRATED.csv")
        df.to_csv(csv_path, index=False)
        
        # Create summary
        summary = {
            "integration_date": datetime.now().isoformat(),
            "symbol": f"{symbol}.NS",
            "total_periods": len(df),
            "total_fields": len(df.columns),
            "data_sources": {
                "screener_in_ratios": "Primary source for financial ratios",
                "yahoo_finance": "Supplementary market data and missing fields",
                "calculated_fields": "Derived from available data"
            },
            "amd_format_compatibility": "100%",
            "ready_for_testing": True,
            "sample_data": df.head(1).to_dict('records')[0] if len(df) > 0 else {}
        }
        
        summary_path = os.path.join(output_dir, f"integration_summary_{symbol}.json")
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        print(f"✅ Saved final dataset to {csv_path}")
        print(f"✅ Saved summary to {summary_path}")
        
        return csv_path, summary_path

async def create_final_dataset(symbol="RELIANCE"):
    """Create final integrated dataset for any NSE stock."""
    print(f"="*80)
    print(f"CREATING FINAL INTEGRATED DATASET FOR {symbol}")
    print(f"="*80)
    
    integrator = EnhancedDataIntegrator()
    
    # Step 1: Load raw screener data
    if not integrator.load_raw_screener_data():
        return None
    
    # Step 2: Extract comprehensive ratios from screener.in
    screener_data = integrator.extract_comprehensive_ratios(symbol)
    
    # Step 3: Fetch Yahoo Finance data (add .NS for Indian stocks)
    yahoo_symbol = f"{symbol}.NS"
    yahoo_data = integrator.fetch_yahoo_finance_data(yahoo_symbol)
    
    # Step 4: Integrate all data
    integrated_data = integrator.integrate_data(screener_data, yahoo_data)
    
    # Step 5: Format to AMD structure
    final_df = integrator.format_to_amd_structure(integrated_data)
    
    # Step 6: Save final dataset
    csv_path, summary_path = integrator.save_final_dataset(final_df, symbol)
    
    print(f"\n" + "="*80)
    print(f"FINAL DATASET CREATED SUCCESSFULLY!")
    print(f"="*80)
    print(f"📊 Dataset: {csv_path}")
    print(f"📋 Summary: {summary_path}")
    print(f"🎯 Ready for AI hedge fund integration!")
    
    return final_df

async def main():
    """Main function."""
    # Create final dataset for RELIANCE
    reliance_df = await create_final_dataset("RELIANCE")
    
    if reliance_df is not None:
        print(f"\n🎯 Testing with other NSE stocks...")
        
        # Test with HDFCBANK
        print(f"\n" + "-"*60)
        print(f"TESTING WITH HDFCBANK")
        print(f"-"*60)
        
        # Note: For other stocks, we would need to run the screener MCP first
        # This is just showing the structure for testing
        print(f"📋 To test with HDFCBANK:")
        print(f"1. Run screener MCP with symbol 'HDFCBANK'")
        print(f"2. Run this integrator with symbol 'HDFCBANK'")
        print(f"3. Yahoo Finance will use 'HDFCBANK.NS'")

if __name__ == "__main__":
    asyncio.run(main())
