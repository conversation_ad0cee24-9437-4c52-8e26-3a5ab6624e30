import os
import sys
import requests
import pandas as pd
import json
from datetime import datetime, timedelta
import time

# Add the project directory to the Python path
project_dir = os.path.join(os.path.dirname(__file__), 'ai-hedge-fund')
src_dir = os.path.join(project_dir, 'src')
sys.path.append(project_dir)
sys.path.append(src_dir)

# Free tier tickers
FREE_TICKERS = ["AAPL", "GOOGL", "MSFT", "NVDA", "TSLA"]

# Set up dates
end_date = datetime.now().strftime("%Y-%m-%d")
start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")

# Create output directory
os.makedirs("financial_data", exist_ok=True)

def fetch_prices(ticker):
    """Fetch price data from API."""
    print(f"Fetching price data for {ticker}...")
    url = f"https://api.financialdatasets.ai/prices/?ticker={ticker}&interval=day&interval_multiplier=1&start_date={start_date}&end_date={end_date}"
    response = requests.get(url)
    if response.status_code != 200:
        print(f"Error fetching price data for {ticker}: {response.status_code} - {response.text}")
        return None

    data = response.json()
    return data

def fetch_financial_metrics(ticker):
    """Fetch financial metrics from API."""
    print(f"Fetching financial metrics for {ticker}...")
    url = f"https://api.financialdatasets.ai/financial-metrics/?ticker={ticker}&report_period_lte={end_date}&limit=10&period=ttm"
    response = requests.get(url)
    if response.status_code != 200:
        print(f"Error fetching financial metrics for {ticker}: {response.status_code} - {response.text}")
        return None

    data = response.json()
    return data

def fetch_line_items(ticker, line_items):
    """Fetch line items from API."""
    print(f"Fetching line items for {ticker}...")
    url = "https://api.financialdatasets.ai/financials/search/line-items"
    body = {
        "tickers": [ticker],
        "line_items": line_items,
        "end_date": end_date,
        "period": "ttm",
        "limit": 10,
    }
    response = requests.post(url, json=body)
    if response.status_code != 200:
        print(f"Error fetching line items for {ticker}: {response.status_code} - {response.text}")
        return None

    data = response.json()
    return data

def fetch_insider_trades(ticker):
    """Fetch insider trades from API."""
    print(f"Fetching insider trades for {ticker}...")
    url = f"https://api.financialdatasets.ai/insider-trades/?ticker={ticker}&filing_date_lte={end_date}&limit=100"
    response = requests.get(url)
    if response.status_code != 200:
        print(f"Error fetching insider trades for {ticker}: {response.status_code} - {response.text}")
        return None

    data = response.json()
    return data

def fetch_company_news(ticker):
    """Fetch company news from API."""
    print(f"Fetching company news for {ticker}...")
    url = f"https://api.financialdatasets.ai/news/?ticker={ticker}&end_date={end_date}&limit=100"
    response = requests.get(url)
    if response.status_code != 200:
        print(f"Error fetching company news for {ticker}: {response.status_code} - {response.text}")
        return None

    data = response.json()
    return data

def save_to_csv(data, filename, ticker):
    """Save data to CSV file."""
    if data is None:
        print(f"No data to save for {filename} - {ticker}")
        return

    # Save raw JSON for reference
    with open(f"financial_data/{filename}_{ticker}_raw.json", "w") as f:
        json.dump(data, f, indent=2)

    # Convert to DataFrame and save as CSV
    try:
        if filename == "prices":
            df = pd.DataFrame(data.get("prices", []))
        elif filename == "financial_metrics":
            df = pd.DataFrame(data.get("financial_metrics", []))
        elif filename == "line_items":
            df = pd.DataFrame(data.get("search_results", []))
        elif filename == "insider_trades":
            df = pd.DataFrame(data.get("insider_trades", []))
        elif filename == "company_news":
            df = pd.DataFrame(data.get("news", []))
        else:
            df = pd.DataFrame([data])

        if not df.empty:
            df.to_csv(f"financial_data/{filename}_{ticker}.csv", index=False)
            print(f"Saved {filename} data for {ticker} to CSV")
        else:
            print(f"No data found in the response for {filename} - {ticker}")
    except Exception as e:
        print(f"Error converting {filename} data for {ticker} to CSV: {e}")
        # Save as JSON as fallback
        with open(f"financial_data/{filename}_{ticker}.json", "w") as f:
            json.dump(data, f, indent=2)

# Common line items to fetch
LINE_ITEMS = [
    "revenue",
    "net_income"
]

# Fetch and save data for each ticker
for ticker in FREE_TICKERS:
    # Fetch and save price data
    price_data = fetch_prices(ticker)
    save_to_csv(price_data, "prices", ticker)

    # Fetch and save financial metrics
    metrics_data = fetch_financial_metrics(ticker)
    save_to_csv(metrics_data, "financial_metrics", ticker)

    # Fetch and save line items
    line_items_data = fetch_line_items(ticker, LINE_ITEMS)
    save_to_csv(line_items_data, "line_items", ticker)

    # Fetch and save insider trades
    insider_trades_data = fetch_insider_trades(ticker)
    save_to_csv(insider_trades_data, "insider_trades", ticker)

    # Fetch and save company news
    company_news_data = fetch_company_news(ticker)
    save_to_csv(company_news_data, "company_news", ticker)

    # Add a delay to avoid rate limiting
    time.sleep(1)

print("Data fetching and saving complete!")
