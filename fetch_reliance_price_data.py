"""
<PERSON>ript to fetch price data for Reliance Industries from yfinance and save it in the format
expected by the AI Hedge Fund analysis agents.
"""

import yfinance as yf
import pandas as pd
import os
from datetime import datetime, timedelta
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

def fetch_price_data(ticker_symbol, output_file, start_date=None, end_date=None):
    """
    Fetch price data for a ticker from yfinance and save it in the format expected by the AI Hedge Fund.

    Args:
        ticker_symbol (str): Ticker symbol for the stock (e.g., 'RELIANCE.NS' for Reliance on NSE)
        output_file (str): Path to save the CSV file
        start_date (str, optional): Start date in YYYY-MM-DD format. If None, uses 1 year ago.
        end_date (str, optional): End date in YYYY-MM-DD format. If None, uses today.

    Returns:
        pd.DataFrame: DataFrame containing the price data
    """
    print(f"{Fore.CYAN}Fetching price data for {ticker_symbol}...{Style.RESET_ALL}")

    # Set default dates if not provided
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")

    if start_date is None:
        # Default to 1 year before end_date
        end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")
        start_date_dt = end_date_dt - timedelta(days=365)
        start_date = start_date_dt.strftime("%Y-%m-%d")

    print(f"Date range: {start_date} to {end_date}")

    try:
        # Fetch data from yfinance
        data = yf.download(ticker_symbol, start=start_date, end=end_date, progress=False)

        if data.empty:
            print(f"{Fore.RED}No data found for {ticker_symbol}. Check if the ticker symbol is correct.{Style.RESET_ALL}")
            return None

        # Reset index to make Date a column
        data = data.reset_index()

        # Rename columns to match expected format
        data = data.rename(columns={
            'Date': 'date',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Adj Close': 'adj_close',
            'Volume': 'volume'
        })

        # Fix any multi-level column headers (common issue with yfinance)
        if isinstance(data.columns, pd.MultiIndex):
            # Get the second level of the MultiIndex (which contains the actual column names)
            data.columns = data.columns.get_level_values(1)

            # Rename columns again to ensure they match the expected format
            data = data.rename(columns={
                'Date': 'date',
                'Open': 'open',
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Adj Close': 'adj_close',
                'Volume': 'volume'
            })

        # Convert date to string format
        if 'date' in data.columns:
            data['date'] = data['date'].dt.strftime('%Y-%m-%d')
        elif 'Date' in data.columns:
            data['date'] = data['Date'].dt.strftime('%Y-%m-%d')
            data = data.drop('Date', axis=1)

        # Select only the required columns
        if 'adj_close' in data.columns:
            data = data[['date', 'open', 'high', 'low', 'close', 'adj_close', 'volume']]
        else:
            data = data[['date', 'open', 'high', 'low', 'close', 'volume']]

        # Save to CSV
        data.to_csv(output_file, index=False)
        print(f"{Fore.GREEN}Price data saved to {output_file}{Style.RESET_ALL}")

        return data

    except Exception as e:
        print(f"{Fore.RED}Error fetching data: {e}{Style.RESET_ALL}")
        return None

if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Fetch price data for Reliance Industries from yfinance')
    parser.add_argument('--ticker', type=str, default='RELIANCE.NS',
                        help='Ticker symbol (default: RELIANCE.NS for NSE, use RELIANCE.BO for BSE)')
    parser.add_argument('--start-date', type=str, default=None,
                        help='Start date in YYYY-MM-DD format')
    parser.add_argument('--end-date', type=str, default=None,
                        help='End date in YYYY-MM-DD format')
    parser.add_argument('--output-dir', type=str, default='reliance_data_transformed',
                        help='Directory to save price data')

    args = parser.parse_args()

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Fetch price data
    output_file = os.path.join(args.output_dir, 'price_data.csv')
    fetch_price_data(args.ticker, output_file, args.start_date, args.end_date)
