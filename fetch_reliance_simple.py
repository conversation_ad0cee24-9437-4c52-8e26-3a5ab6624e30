"""
Simple script to fetch price data for Reliance Industries from yfinance.
"""

import yfinance as yf
import pandas as pd
import os

# Ticker symbol for Reliance Industries on NSE
ticker_symbol = "RELIANCE.NS"

# Date range
start_date = "2023-01-01"
end_date = "2024-12-31"

print(f"Fetching price data for {ticker_symbol} from {start_date} to {end_date}...")

# Fetch data from yfinance
data = yf.download(ticker_symbol, start=start_date, end=end_date, progress=False)

# Reset index to make Date a column
data = data.reset_index()

# Print column names to debug
print("Original columns:", data.columns)

# Save raw data for inspection
data.to_csv("reliance_raw_data.csv")
print("Raw data saved to reliance_raw_data.csv")

# Create a new DataFrame with the expected format
price_data = pd.DataFrame()
price_data['date'] = data['Date'].dt.strftime('%Y-%m-%d')
price_data['open'] = data['Open']
price_data['high'] = data['High']
price_data['low'] = data['Low']
price_data['close'] = data['Close']
price_data['volume'] = data['Volume']

# Save to CSV
output_file = os.path.join("reliance_data_transformed", "price_data.csv")
price_data.to_csv(output_file, index=False)
print(f"Price data saved to {output_file}")

# Print the first few rows
print("\nFirst few rows of the transformed data:")
print(price_data.head())
