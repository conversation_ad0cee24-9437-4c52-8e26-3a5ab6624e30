# Financial Datasets API Data Format

This document describes the data format for each endpoint of the financialdatasets.ai API based on the data fetched for the free tier stocks (AAPL, GOOGL, MSFT, NVDA, TSLA).

## 1. Prices API

**Endpoint:** `https://api.financialdatasets.ai/prices/`

**Description:** Provides historical price data for a given ticker.

**Parameters:**
- `ticker`: Stock ticker symbol
- `interval`: Time interval (e.g., "day")
- `interval_multiplier`: Multiplier for the interval (e.g., 1)
- `start_date`: Start date in YYYY-MM-DD format
- `end_date`: End date in YYYY-MM-DD format

**Response Format:**
```
{
  "ticker": "AAPL",
  "prices": [
    {
      "ticker": "AAPL",
      "open": 168.7,
      "close": 169.67,
      "high": 170.08,
      "low": 168.35,
      "volume": 42451209,
      "time": "2024-04-09T04:00:00Z",
      "time_milliseconds": 1712635200000
    },
    ...
  ]
}
```

**CSV Columns:**
- `ticker`: Stock ticker symbol
- `open`: Opening price
- `close`: Closing price
- `high`: Highest price during the period
- `low`: Lowest price during the period
- `volume`: Trading volume
- `time`: Timestamp in ISO format
- `time_milliseconds`: Timestamp in milliseconds

## 2. Financial Metrics API

**Endpoint:** `https://api.financialdatasets.ai/financial-metrics/`

**Description:** Provides financial metrics for a given ticker.

**Parameters:**
- `ticker`: Stock ticker symbol
- `report_period_lte`: Report period less than or equal to (YYYY-MM-DD)
- `limit`: Maximum number of results to return
- `period`: Period type (e.g., "ttm" for trailing twelve months)

**Response Format:**
```
{
  "financial_metrics": [
    {
      "ticker": "AAPL",
      "report_period": "2024-12-28",
      "fiscal_period": "2025-Q1",
      "period": "ttm",
      "currency": "USD",
      "market_cap": 3863453200570.0,
      "enterprise_value": 3940139200570.0,
      "price_to_earnings_ratio": 40.182,
      "price_to_book_ratio": 57.873,
      "price_to_sales_ratio": 9.762,
      ...
    },
    ...
  ]
}
```

**CSV Columns:**
- `ticker`: Stock ticker symbol
- `report_period`: End date of the reporting period
- `fiscal_period`: Fiscal period (e.g., "2025-Q1")
- `period`: Period type (e.g., "ttm" for trailing twelve months)
- `currency`: Currency code
- `market_cap`: Market capitalization
- `enterprise_value`: Enterprise value
- `price_to_earnings_ratio`: P/E ratio
- `price_to_book_ratio`: P/B ratio
- `price_to_sales_ratio`: P/S ratio
- `enterprise_value_to_ebitda_ratio`: EV/EBITDA ratio
- `enterprise_value_to_revenue_ratio`: EV/Revenue ratio
- `free_cash_flow_yield`: Free cash flow yield
- `peg_ratio`: PEG ratio
- `gross_margin`: Gross margin
- `operating_margin`: Operating margin
- `net_margin`: Net margin
- `return_on_equity`: Return on equity (ROE)
- `return_on_assets`: Return on assets (ROA)
- `return_on_invested_capital`: Return on invested capital (ROIC)
- `asset_turnover`: Asset turnover
- `inventory_turnover`: Inventory turnover
- `receivables_turnover`: Receivables turnover
- `days_sales_outstanding`: Days sales outstanding
- `operating_cycle`: Operating cycle
- `working_capital_turnover`: Working capital turnover
- `current_ratio`: Current ratio
- `quick_ratio`: Quick ratio
- `cash_ratio`: Cash ratio
- `operating_cash_flow_ratio`: Operating cash flow ratio
- `debt_to_equity`: Debt-to-equity ratio
- `debt_to_assets`: Debt-to-assets ratio
- `interest_coverage`: Interest coverage ratio
- `revenue_growth`: Revenue growth
- `earnings_growth`: Earnings growth
- `book_value_growth`: Book value growth
- `earnings_per_share_growth`: EPS growth
- `free_cash_flow_growth`: Free cash flow growth
- `operating_income_growth`: Operating income growth
- `ebitda_growth`: EBITDA growth
- `payout_ratio`: Payout ratio
- `earnings_per_share`: Earnings per share (EPS)
- `book_value_per_share`: Book value per share
- `free_cash_flow_per_share`: Free cash flow per share

## 3. Line Items API

**Endpoint:** `https://api.financialdatasets.ai/financials/search/line-items`

**Description:** Provides specific financial line items for a given ticker.

**Parameters:**
- `tickers`: Array of stock ticker symbols
- `line_items`: Array of line item names to fetch
- `end_date`: End date in YYYY-MM-DD format
- `period`: Period type (e.g., "ttm" for trailing twelve months)
- `limit`: Maximum number of results to return

**Response Format:**
```
{
  "search_results": [
    {
      "ticker": "AAPL",
      "report_period": "2024-12-28",
      "period": "ttm",
      "currency": "USD",
      "net_income": 96150000000.0,
      "revenue": 395760000000.0
    },
    ...
  ]
}
```

**CSV Columns:**
- `ticker`: Stock ticker symbol
- `report_period`: End date of the reporting period
- `period`: Period type (e.g., "ttm" for trailing twelve months)
- `currency`: Currency code
- Additional columns for each requested line item (e.g., `net_income`, `revenue`)

## 4. Insider Trades API

**Endpoint:** `https://api.financialdatasets.ai/insider-trades/`

**Description:** Provides insider trading data for a given ticker.

**Parameters:**
- `ticker`: Stock ticker symbol
- `filing_date_lte`: Filing date less than or equal to (YYYY-MM-DD)
- `limit`: Maximum number of results to return

**Response Format:**
```
{
  "insider_trades": [
    {
      "ticker": "AAPL",
      "issuer": "Apple Inc",
      "name": "Jeffrey E Williams",
      "title": "COO",
      "is_board_director": false,
      "transaction_date": "2025-04-01",
      "transaction_shares": -22159,
      "transaction_price_per_share": null,
      "transaction_value": null,
      "shares_owned_before_transaction": 66477,
      "shares_owned_after_transaction": 44318,
      "security_title": "Restricted Stock Unit",
      "filing_date": "2025-04-03"
    },
    ...
  ]
}
```

**CSV Columns:**
- `ticker`: Stock ticker symbol
- `issuer`: Issuer name
- `name`: Insider's name
- `title`: Insider's title
- `is_board_director`: Whether the insider is a board director
- `transaction_date`: Date of the transaction
- `transaction_shares`: Number of shares involved in the transaction (negative for sales)
- `transaction_price_per_share`: Price per share in the transaction
- `transaction_value`: Total value of the transaction
- `shares_owned_before_transaction`: Shares owned before the transaction
- `shares_owned_after_transaction`: Shares owned after the transaction
- `security_title`: Title of the security
- `filing_date`: Date of the filing

## 5. Company News API

**Endpoint:** `https://api.financialdatasets.ai/news/`

**Description:** Provides news articles related to a given ticker.

**Parameters:**
- `ticker`: Stock ticker symbol
- `end_date`: End date in YYYY-MM-DD format
- `limit`: Maximum number of results to return

**Response Format:**
```
{
  "news": [
    {
      "ticker": "AAPL",
      "title": "US Stocks Futures Trade Mixed Ahead Of Opening Bell: 'Preserve Capital In Conditions Of High Uncertainty,' Says Expert",
      "author": "Rishabh Mishra",
      "source": "Benzinga",
      "date": "2025-04-09T09:53:59Z",
      "url": "https://www.benzinga.com/25/04/44706800/us-stocks-futures-trade-mixed-ahead-of-opening-bell-preserve-capital-in-conditions-of-high-uncertainty-says-exper",
      "sentiment": "negative"
    },
    ...
  ]
}
```

**CSV Columns:**
- `ticker`: Stock ticker symbol
- `title`: Article title
- `author`: Article author
- `source`: News source
- `date`: Publication date
- `url`: URL to the article
- `sentiment`: Sentiment of the article (e.g., "positive", "negative", "neutral")

## Notes

1. The free tier stocks (AAPL, GOOGL, MSFT, NVDA, TSLA) can be accessed without an API key.
2. For other stocks, a valid API key from financialdatasets.ai is required.
3. The data format may vary slightly depending on the specific ticker and time period.
4. Some fields may be null or empty depending on data availability.
