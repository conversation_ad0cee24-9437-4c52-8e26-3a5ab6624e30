"""
Formula Validation Script - AAPL Test
This script validates financial metric formulas by:
1. Fetching AAPL data from Yahoo Finance
2. Applying formulas to calculate metrics
3. Comparing with existing AAPL financial metrics data
4. Identifying and correcting formula discrepancies
"""

import yfinance as yf
import pandas as pd
import json
import os
from datetime import datetime
import warnings
import numpy as np
warnings.filterwarnings('ignore')

class FormulaValidator:
    def __init__(self, symbol="AAPL"):
        self.symbol = symbol
        self.ticker = yf.Ticker(symbol)

        # Load existing AAPL data for comparison
        self.existing_aapl_data = self.load_existing_aapl_data()

        # Fetch fresh AAPL data from Yahoo Finance
        print(f"Fetching fresh {symbol} data from Yahoo Finance...")
        self.info = self.ticker.info
        self.financials = self.ticker.financials
        self.quarterly_financials = self.ticker.quarterly_financials
        self.balance_sheet = self.ticker.balance_sheet
        self.quarterly_balance_sheet = self.ticker.quarterly_balance_sheet
        self.cashflow = self.ticker.cashflow
        self.quarterly_cashflow = self.ticker.quarterly_cashflow

        print(f"✓ Loaded {symbol} data successfully")
        print(f"Available quarterly periods: {len(self.quarterly_financials.columns) if not self.quarterly_financials.empty else 0}")

    def load_existing_aapl_data(self):
        """Load existing AAPL financial metrics for comparison."""
        try:
            existing_data = pd.read_csv("financial_data/financial_metrics_AAPL.csv")
            print(f"✓ Loaded existing AAPL data: {len(existing_data)} records, {len(existing_data.columns)} columns")
            return existing_data
        except Exception as e:
            print(f"✗ Error loading existing AAPL data: {e}")
            return pd.DataFrame()

    def safe_divide(self, numerator, denominator, default=0.0):
        """Safely divide two numbers, returning default if denominator is 0 or NaN."""
        try:
            if pd.isna(numerator) or pd.isna(denominator) or denominator == 0:
                return default
            result = float(numerator) / float(denominator)
            return result if not pd.isna(result) else default
        except:
            return default

    def get_financial_item(self, df, item_name, period_col, default=0.0):
        """Safely get a financial statement item for a specific period."""
        try:
            if df.empty or period_col not in df.columns:
                return default

            # Try exact match first
            if item_name in df.index:
                value = df.loc[item_name, period_col]
                return float(value) if not pd.isna(value) else default

            # Try partial matches for common variations
            variations = [
                item_name,
                item_name.replace(' ', ''),
                item_name.title(),
                item_name.upper(),
                item_name.lower()
            ]

            for variation in variations:
                matching_indices = [idx for idx in df.index if variation.lower() in idx.lower()]
                if matching_indices:
                    value = df.loc[matching_indices[0], period_col]
                    return float(value) if not pd.isna(value) else default

            return default
        except:
            return default

    def calculate_ttm_value(self, df, item_name, start_index=0):
        """Calculate Trailing Twelve Months (TTM) value for a financial item."""
        try:
            if df.empty or start_index + 4 > len(df.columns):
                if start_index < len(df.columns):
                    single_quarter = self.get_financial_item(df, item_name, df.columns[start_index])
                    return single_quarter * 4
                return 0.0

            ttm_value = 0.0
            for i in range(4):
                if start_index + i < len(df.columns):
                    quarter_value = self.get_financial_item(df, item_name, df.columns[start_index + i])
                    ttm_value += quarter_value

            return ttm_value
        except:
            return 0.0

    def calculate_growth_rate(self, current_value, previous_value):
        """Calculate growth rate between two periods."""
        if previous_value == 0 or pd.isna(previous_value) or pd.isna(current_value):
            return 0.0
        return ((current_value - previous_value) / abs(previous_value)) * 100

    def calculate_aapl_metrics_with_formulas(self):
        """Calculate AAPL metrics using our formulas."""
        print("\nCalculating AAPL metrics using our formulas...")

        if self.quarterly_financials.empty:
            print("No quarterly financial data available")
            return pd.DataFrame()

        calculated_metrics = []

        # Calculate for the same number of periods as existing data
        num_periods = min(len(self.existing_aapl_data), len(self.quarterly_financials.columns))

        for i in range(num_periods):
            period_col = self.quarterly_financials.columns[i]

            # Basic info (current/latest values)
            market_cap = self.info.get('marketCap', 0)
            shares_outstanding = self.info.get('sharesOutstanding', 0)
            current_price = self.info.get('currentPrice', 0)
            enterprise_value = self.info.get('enterpriseValue', 0)

            # Income Statement Items
            revenue = self.get_financial_item(self.quarterly_financials, 'Total Revenue', period_col)
            gross_profit = self.get_financial_item(self.quarterly_financials, 'Gross Profit', period_col)
            operating_income = self.get_financial_item(self.quarterly_financials, 'Operating Income', period_col)
            ebitda = self.get_financial_item(self.quarterly_financials, 'EBITDA', period_col)
            ebit = self.get_financial_item(self.quarterly_financials, 'EBIT', period_col)
            net_income = self.get_financial_item(self.quarterly_financials, 'Net Income', period_col)
            interest_expense = self.get_financial_item(self.quarterly_financials, 'Interest Expense', period_col)

            # Balance Sheet Items
            total_assets = self.get_financial_item(self.quarterly_balance_sheet, 'Total Assets', period_col)
            current_assets = self.get_financial_item(self.quarterly_balance_sheet, 'Current Assets', period_col)
            cash_and_equivalents = self.get_financial_item(self.quarterly_balance_sheet, 'Cash And Cash Equivalents', period_col)
            inventory = self.get_financial_item(self.quarterly_balance_sheet, 'Inventory', period_col)
            accounts_receivable = self.get_financial_item(self.quarterly_balance_sheet, 'Accounts Receivable', period_col)

            total_liabilities = self.get_financial_item(self.quarterly_balance_sheet, 'Total Liabilities Net Minority Interest', period_col)
            current_liabilities = self.get_financial_item(self.quarterly_balance_sheet, 'Current Liabilities', period_col)
            total_debt = self.get_financial_item(self.quarterly_balance_sheet, 'Total Debt', period_col)
            stockholders_equity = self.get_financial_item(self.quarterly_balance_sheet, 'Stockholders Equity', period_col)

            # Cash Flow Items
            operating_cash_flow = self.get_financial_item(self.quarterly_cashflow, 'Operating Cash Flow', period_col)
            free_cash_flow = self.get_financial_item(self.quarterly_cashflow, 'Free Cash Flow', period_col)
            capital_expenditure = self.get_financial_item(self.quarterly_cashflow, 'Capital Expenditure', period_col)

            # Calculate TTM values
            ttm_revenue = self.calculate_ttm_value(self.quarterly_financials, 'Total Revenue', i)
            ttm_net_income = self.calculate_ttm_value(self.quarterly_financials, 'Net Income', i)
            ttm_operating_income = self.calculate_ttm_value(self.quarterly_financials, 'Operating Income', i)
            ttm_ebitda = self.calculate_ttm_value(self.quarterly_financials, 'EBITDA', i)

            # Calculate derived values
            if enterprise_value == 0:
                enterprise_value = market_cap + total_debt - cash_and_equivalents

            working_capital = current_assets - current_liabilities

            # Growth calculations
            revenue_growth = 0.0
            earnings_growth = 0.0
            if i < len(self.quarterly_financials.columns) - 1:
                prev_period = self.quarterly_financials.columns[i + 1]
                prev_revenue = self.get_financial_item(self.quarterly_financials, 'Total Revenue', prev_period)
                prev_net_income = self.get_financial_item(self.quarterly_financials, 'Net Income', prev_period)

                revenue_growth = self.calculate_growth_rate(revenue, prev_revenue)
                earnings_growth = self.calculate_growth_rate(net_income, prev_net_income)

            # Calculate key metrics that exist in AAPL data
            metrics = {
                'ticker': self.symbol,
                'report_period': str(period_col)[:10] if hasattr(period_col, 'strftime') else str(period_col),
                'fiscal_period': f"Q{((pd.to_datetime(period_col).month - 1) // 3) + 1}" if hasattr(period_col, 'strftime') else 'Q1',
                'period': 'quarterly',
                'currency': 'USD',

                # Market Valuation Metrics
                'market_cap': float(market_cap),
                'enterprise_value': float(enterprise_value),
                'price_to_earnings_ratio': self.safe_divide(market_cap, ttm_net_income),
                'price_to_book_ratio': self.safe_divide(market_cap, stockholders_equity),
                'price_to_sales_ratio': self.safe_divide(market_cap, ttm_revenue),
                'enterprise_value_to_ebitda_ratio': self.safe_divide(enterprise_value, ttm_ebitda),
                'enterprise_value_to_revenue_ratio': self.safe_divide(enterprise_value, ttm_revenue),

                # Profitability Margins
                'gross_margin': self.safe_divide(gross_profit, revenue) * 100,
                'operating_margin': self.safe_divide(operating_income, revenue) * 100,
                'net_margin': self.safe_divide(net_income, revenue) * 100,

                # Return Ratios
                'return_on_equity': self.safe_divide(ttm_net_income, stockholders_equity) * 100,
                'return_on_assets': self.safe_divide(ttm_net_income, total_assets) * 100,
                'return_on_invested_capital': self.safe_divide(ttm_operating_income, (stockholders_equity + total_debt)) * 100,

                # Efficiency Ratios
                'asset_turnover': self.safe_divide(ttm_revenue, total_assets),
                'inventory_turnover': self.safe_divide(ttm_revenue, inventory) if inventory > 0 else 0,
                'receivables_turnover': self.safe_divide(ttm_revenue, accounts_receivable) if accounts_receivable > 0 else 0,

                # Liquidity Ratios
                'current_ratio': self.safe_divide(current_assets, current_liabilities),
                'quick_ratio': self.safe_divide((current_assets - inventory), current_liabilities),
                'cash_ratio': self.safe_divide(cash_and_equivalents, current_liabilities),

                # Leverage Ratios
                'debt_to_equity': self.safe_divide(total_debt, stockholders_equity),
                'debt_to_assets': self.safe_divide(total_debt, total_assets),
                'interest_coverage': self.safe_divide(ebit, abs(interest_expense)) if interest_expense != 0 else 0,

                # Per Share Metrics
                'earnings_per_share': self.safe_divide(net_income, shares_outstanding) if shares_outstanding > 0 else 0,
                'book_value_per_share': self.safe_divide(stockholders_equity, shares_outstanding) if shares_outstanding > 0 else 0,

                # Growth Metrics
                'revenue_growth': float(revenue_growth),
                'earnings_growth': float(earnings_growth),

                # Additional common metrics
                'free_cash_flow_yield': self.safe_divide(free_cash_flow * 4, market_cap) * 100 if market_cap > 0 else 0,
                'operating_cycle': self.safe_divide(365, self.safe_divide(ttm_revenue, inventory)) + self.safe_divide(365, self.safe_divide(ttm_revenue, accounts_receivable)) if inventory > 0 and accounts_receivable > 0 else 0,
                'days_sales_outstanding': self.safe_divide(accounts_receivable * 365, ttm_revenue) if ttm_revenue > 0 else 0,
            }

            calculated_metrics.append(metrics)
            print(f"✓ Calculated metrics for {period_col}")

        return pd.DataFrame(calculated_metrics)

    def compare_with_existing_data(self, calculated_df):
        """Compare calculated metrics with existing AAPL data."""
        print("\n" + "=" * 80)
        print("FORMULA VALIDATION - COMPARING CALCULATED VS EXISTING AAPL DATA")
        print("=" * 80)

        if calculated_df.empty or self.existing_aapl_data.empty:
            print("❌ Cannot compare - missing data")
            return {}

        comparison_results = {}
        tolerance = 0.05  # 5% tolerance for differences

        # Get common columns
        common_columns = set(calculated_df.columns) & set(self.existing_aapl_data.columns)
        print(f"Comparing {len(common_columns)} common metrics...")

        # Compare each metric
        for col in common_columns:
            if col in ['ticker', 'report_period', 'fiscal_period', 'period', 'currency']:
                continue  # Skip non-numeric columns

            try:
                calc_values = calculated_df[col].dropna()
                existing_values = self.existing_aapl_data[col].dropna()

                if len(calc_values) == 0 or len(existing_values) == 0:
                    continue

                # Compare first few values
                min_len = min(len(calc_values), len(existing_values))

                differences = []
                for i in range(min_len):
                    calc_val = calc_values.iloc[i]
                    existing_val = existing_values.iloc[i]

                    if existing_val != 0:
                        pct_diff = abs((calc_val - existing_val) / existing_val) * 100
                    else:
                        pct_diff = abs(calc_val - existing_val)

                    differences.append(pct_diff)

                avg_diff = np.mean(differences)
                max_diff = np.max(differences)

                status = "✅ MATCH" if avg_diff <= tolerance * 100 else "⚠️ DIFFER" if avg_diff <= 20 else "❌ MAJOR DIFF"

                comparison_results[col] = {
                    'status': status,
                    'avg_difference_pct': avg_diff,
                    'max_difference_pct': max_diff,
                    'calculated_avg': np.mean(calc_values),
                    'existing_avg': np.mean(existing_values),
                    'sample_calculated': calc_values.iloc[0] if len(calc_values) > 0 else 0,
                    'sample_existing': existing_values.iloc[0] if len(existing_values) > 0 else 0
                }

                print(f"{status} {col}: Avg diff {avg_diff:.1f}%, Max diff {max_diff:.1f}%")

            except Exception as e:
                print(f"❌ Error comparing {col}: {e}")

        return comparison_results

    def generate_validation_report(self, comparison_results):
        """Generate validation report and recommendations."""
        print("\n" + "=" * 80)
        print("FORMULA VALIDATION REPORT")
        print("=" * 80)

        total_metrics = len(comparison_results)
        matching_metrics = sum(1 for r in comparison_results.values() if "MATCH" in r['status'])
        differing_metrics = sum(1 for r in comparison_results.values() if "DIFFER" in r['status'])
        major_diff_metrics = sum(1 for r in comparison_results.values() if "MAJOR DIFF" in r['status'])

        accuracy_score = (matching_metrics / total_metrics * 100) if total_metrics > 0 else 0

        print(f"📊 Validation Summary:")
        print(f"  Total metrics compared: {total_metrics}")
        print(f"  ✅ Matching (≤5% diff): {matching_metrics}")
        print(f"  ⚠️ Minor differences (5-20%): {differing_metrics}")
        print(f"  ❌ Major differences (>20%): {major_diff_metrics}")
        print(f"  🎯 Formula accuracy: {accuracy_score:.1f}%")

        # Show metrics that need attention
        if major_diff_metrics > 0:
            print(f"\n❌ Metrics with major differences (>20%):")
            for metric, result in comparison_results.items():
                if "MAJOR DIFF" in result['status']:
                    print(f"  {metric}: {result['avg_difference_pct']:.1f}% avg diff")
                    print(f"    Calculated: {result['sample_calculated']:.2f}")
                    print(f"    Existing: {result['sample_existing']:.2f}")

        if differing_metrics > 0:
            print(f"\n⚠️ Metrics with minor differences (5-20%):")
            for metric, result in comparison_results.items():
                if "DIFFER" in result['status']:
                    print(f"  {metric}: {result['avg_difference_pct']:.1f}% avg diff")

        # Assessment
        if accuracy_score >= 90:
            assessment = "EXCELLENT - Formulas are highly accurate"
        elif accuracy_score >= 75:
            assessment = "GOOD - Minor formula adjustments needed"
        elif accuracy_score >= 50:
            assessment = "FAIR - Moderate formula corrections required"
        else:
            assessment = "POOR - Major formula revisions needed"

        print(f"\n🏆 Assessment: {assessment}")

        return {
            'total_metrics': total_metrics,
            'matching_metrics': matching_metrics,
            'accuracy_score': accuracy_score,
            'assessment': assessment,
            'comparison_results': comparison_results
        }

def main():
    """Main validation function."""
    print("=" * 80)
    print("FINANCIAL FORMULA VALIDATION - AAPL TEST")
    print("=" * 80)

    # Initialize validator
    validator = FormulaValidator("AAPL")

    # Calculate AAPL metrics using our formulas
    calculated_metrics = validator.calculate_aapl_metrics_with_formulas()

    if calculated_metrics.empty:
        print("❌ Failed to calculate metrics")
        return

    # Save calculated metrics for inspection
    calculated_metrics.to_csv("calculated_aapl_metrics.csv", index=False)
    print(f"💾 Saved calculated metrics to calculated_aapl_metrics.csv")

    # Also load and test corrected metrics if available
    try:
        corrected_metrics = pd.read_csv("corrected_aapl_metrics.csv")
        print(f"✓ Also loaded corrected AAPL metrics: {len(corrected_metrics)} records")

        # Compare corrected metrics with existing data
        print("\n" + "=" * 80)
        print("TESTING CORRECTED FORMULAS")
        print("=" * 80)

        corrected_comparison = validator.compare_with_existing_data(corrected_metrics)
        corrected_report = validator.generate_validation_report(corrected_comparison)

        # Save corrected validation report
        with open("corrected_formula_validation_report.json", 'w') as f:
            json.dump(corrected_report, f, indent=2, default=str)
        print(f"💾 Corrected validation report saved to corrected_formula_validation_report.json")

    except Exception as e:
        print(f"⚠️ Could not test corrected metrics: {e}")

    # Compare with existing data
    comparison_results = validator.compare_with_existing_data(calculated_metrics)

    # Generate validation report
    validation_report = validator.generate_validation_report(comparison_results)

    # Save validation report
    with open("formula_validation_report.json", 'w') as f:
        json.dump(validation_report, f, indent=2, default=str)

    print(f"\n💾 Validation report saved to formula_validation_report.json")

    return validation_report

if __name__ == "__main__":
    main()
