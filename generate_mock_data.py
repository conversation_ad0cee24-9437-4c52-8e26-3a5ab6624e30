import os
import json
import pandas as pd
import random
from datetime import datetime, timedelta
import numpy as np

# Create output directory
os.makedirs("mock_financial_data", exist_ok=True)

def generate_mock_prices(ticker, start_date, end_date):
    """Generate mock price data for a ticker."""
    print(f"Generating mock price data for {ticker}...")
    
    # Convert dates to datetime objects
    start = datetime.strptime(start_date, "%Y-%m-%d")
    end = datetime.strptime(end_date, "%Y-%m-%d")
    
    # Generate a list of dates
    date_list = []
    current = start
    while current <= end:
        # Skip weekends
        if current.weekday() < 5:  # Monday to Friday
            date_list.append(current)
        current += timedelta(days=1)
    
    # Generate random price data
    base_price = random.uniform(50, 500)
    volatility = random.uniform(0.01, 0.05)
    
    prices = []
    for date in date_list:
        # Generate random price movement
        price_change = random.normalvariate(0, volatility)
        close_price = max(0.01, base_price * (1 + price_change))
        
        # Generate other price data
        open_price = close_price * random.uniform(0.98, 1.02)
        high_price = max(open_price, close_price) * random.uniform(1.0, 1.05)
        low_price = min(open_price, close_price) * random.uniform(0.95, 1.0)
        volume = int(random.uniform(1000000, 50000000))
        
        # Format date
        iso_date = date.strftime("%Y-%m-%dT%H:%M:%SZ")
        ms_timestamp = int(date.timestamp() * 1000)
        
        # Update base price for next iteration
        base_price = close_price
        
        prices.append({
            "ticker": ticker,
            "open": round(open_price, 2),
            "close": round(close_price, 2),
            "high": round(high_price, 2),
            "low": round(low_price, 2),
            "volume": volume,
            "time": iso_date,
            "time_milliseconds": ms_timestamp
        })
    
    return {"ticker": ticker, "prices": prices}

def generate_mock_financial_metrics(ticker, end_date):
    """Generate mock financial metrics for a ticker."""
    print(f"Generating mock financial metrics for {ticker}...")
    
    # Convert end_date to datetime
    end = datetime.strptime(end_date, "%Y-%m-%d")
    
    # Generate quarterly report periods (4 quarters)
    report_periods = []
    for i in range(4):
        report_date = end - timedelta(days=90 * i)
        report_periods.append(report_date.strftime("%Y-%m-%d"))
    
    financial_metrics = []
    for i, report_period in enumerate(report_periods):
        # Generate fiscal period
        year = int(report_period.split("-")[0])
        quarter = 4 - i
        if quarter <= 0:
            quarter += 4
            year -= 1
        fiscal_period = f"{year}-Q{quarter}"
        
        # Generate random financial metrics
        market_cap = random.uniform(10e9, 3e12)
        enterprise_value = market_cap * random.uniform(0.9, 1.1)
        pe_ratio = random.uniform(10, 50)
        pb_ratio = random.uniform(1, 60)
        ps_ratio = random.uniform(1, 20)
        ev_ebitda = random.uniform(5, 30)
        ev_revenue = random.uniform(1, 15)
        fcf_yield = random.uniform(0.01, 0.1)
        peg_ratio = random.uniform(0.5, 5)
        
        # Generate margins
        gross_margin = random.uniform(0.2, 0.8)
        operating_margin = gross_margin * random.uniform(0.3, 0.8)
        net_margin = operating_margin * random.uniform(0.5, 0.9)
        
        # Generate returns
        roe = random.uniform(0.05, 2.0)
        roa = random.uniform(0.02, 0.5)
        roic = random.uniform(0.05, 0.8)
        
        # Generate turnover ratios
        asset_turnover = random.uniform(0.2, 2.0)
        inventory_turnover = random.uniform(5, 100)
        receivables_turnover = random.uniform(4, 20)
        
        # Generate liquidity ratios
        current_ratio = random.uniform(0.8, 3.0)
        quick_ratio = current_ratio * random.uniform(0.7, 0.95)
        cash_ratio = quick_ratio * random.uniform(0.2, 0.8)
        
        # Generate leverage ratios
        debt_to_equity = random.uniform(0.1, 5.0)
        debt_to_assets = random.uniform(0.1, 0.7)
        
        # Generate growth rates
        revenue_growth = random.uniform(-0.1, 0.5)
        earnings_growth = random.uniform(-0.2, 0.8)
        
        # Generate per share values
        eps = random.uniform(0.5, 20)
        book_value_per_share = random.uniform(1, 50)
        fcf_per_share = random.uniform(0.5, 15)
        
        financial_metrics.append({
            "ticker": ticker,
            "report_period": report_period,
            "fiscal_period": fiscal_period,
            "period": "ttm",
            "currency": "USD",
            "market_cap": market_cap,
            "enterprise_value": enterprise_value,
            "price_to_earnings_ratio": pe_ratio,
            "price_to_book_ratio": pb_ratio,
            "price_to_sales_ratio": ps_ratio,
            "enterprise_value_to_ebitda_ratio": ev_ebitda,
            "enterprise_value_to_revenue_ratio": ev_revenue,
            "free_cash_flow_yield": fcf_yield,
            "peg_ratio": peg_ratio,
            "gross_margin": gross_margin,
            "operating_margin": operating_margin,
            "net_margin": net_margin,
            "return_on_equity": roe,
            "return_on_assets": roa,
            "return_on_invested_capital": roic,
            "asset_turnover": asset_turnover,
            "inventory_turnover": inventory_turnover,
            "receivables_turnover": receivables_turnover,
            "days_sales_outstanding": 365 / receivables_turnover,
            "operating_cycle": 365 / inventory_turnover + 365 / receivables_turnover,
            "working_capital_turnover": random.uniform(5, 20),
            "current_ratio": current_ratio,
            "quick_ratio": quick_ratio,
            "cash_ratio": cash_ratio,
            "operating_cash_flow_ratio": random.uniform(0.5, 2.0),
            "debt_to_equity": debt_to_equity,
            "debt_to_assets": debt_to_assets,
            "interest_coverage": random.uniform(5, 50),
            "revenue_growth": revenue_growth,
            "earnings_growth": earnings_growth,
            "book_value_growth": random.uniform(-0.1, 0.3),
            "earnings_per_share_growth": random.uniform(-0.2, 0.5),
            "free_cash_flow_growth": random.uniform(-0.2, 0.5),
            "operating_income_growth": random.uniform(-0.1, 0.4),
            "ebitda_growth": random.uniform(-0.1, 0.4),
            "payout_ratio": random.uniform(0, 0.8),
            "earnings_per_share": eps,
            "book_value_per_share": book_value_per_share,
            "free_cash_flow_per_share": fcf_per_share
        })
    
    return {"financial_metrics": financial_metrics}

def generate_mock_line_items(ticker, line_items, end_date):
    """Generate mock line items for a ticker."""
    print(f"Generating mock line items for {ticker}...")
    
    # Convert end_date to datetime
    end = datetime.strptime(end_date, "%Y-%m-%d")
    
    # Generate quarterly report periods (4 quarters)
    report_periods = []
    for i in range(4):
        report_date = end - timedelta(days=90 * i)
        report_periods.append(report_date.strftime("%Y-%m-%d"))
    
    search_results = []
    for report_period in report_periods:
        # Generate base revenue
        revenue = random.uniform(1e9, 100e9)
        
        # Create result dictionary
        result = {
            "ticker": ticker,
            "report_period": report_period,
            "period": "ttm",
            "currency": "USD"
        }
        
        # Add requested line items
        for item in line_items:
            if item == "revenue":
                result[item] = revenue
            elif item == "net_income":
                result[item] = revenue * random.uniform(0.05, 0.3)
            elif item == "total_assets":
                result[item] = revenue * random.uniform(1, 5)
            elif item == "total_liabilities":
                result[item] = revenue * random.uniform(0.5, 3)
            elif item == "total_shareholders_equity":
                result[item] = revenue * random.uniform(0.5, 2)
            elif item == "operating_income":
                result[item] = revenue * random.uniform(0.1, 0.4)
            elif item == "ebitda":
                result[item] = revenue * random.uniform(0.15, 0.5)
            elif item == "free_cash_flow":
                result[item] = revenue * random.uniform(0.05, 0.25)
            else:
                result[item] = random.uniform(1e6, 1e10)  # Generic value for other items
        
        search_results.append(result)
    
    return {"search_results": search_results}

def generate_mock_insider_trades(ticker, end_date):
    """Generate mock insider trades for a ticker."""
    print(f"Generating mock insider trades for {ticker}...")
    
    # Convert end_date to datetime
    end = datetime.strptime(end_date, "%Y-%m-%d")
    
    # Generate random number of insider trades
    num_trades = random.randint(5, 20)
    
    # List of possible insider titles
    titles = ["CEO", "CFO", "COO", "CTO", "Director", "VP", "SVP", "EVP"]
    
    # List of possible security titles
    security_titles = ["Common Stock", "Restricted Stock Unit", "Stock Option"]
    
    insider_trades = []
    for _ in range(num_trades):
        # Generate random dates
        days_ago = random.randint(1, 180)
        transaction_date = (end - timedelta(days=days_ago)).strftime("%Y-%m-%d")
        filing_date = (end - timedelta(days=days_ago-random.randint(1, 3))).strftime("%Y-%m-%d")
        
        # Generate random insider name
        first_names = ["John", "Jane", "Michael", "Sarah", "David", "Lisa", "Robert", "Emily"]
        last_names = ["Smith", "Johnson", "Williams", "Jones", "Brown", "Davis", "Miller", "Wilson"]
        name = f"{random.choice(first_names)} {random.choice(last_names)}"
        
        # Generate random transaction details
        is_purchase = random.random() > 0.6  # 40% chance of purchase
        transaction_shares = random.randint(1000, 50000)
        if not is_purchase:
            transaction_shares = -transaction_shares
        
        transaction_price = random.uniform(10, 500)
        transaction_value = transaction_shares * transaction_price
        if transaction_shares < 0:
            transaction_value = -transaction_value
        
        # Generate random share ownership
        shares_after = random.randint(10000, 1000000)
        shares_before = shares_after - transaction_shares
        
        insider_trades.append({
            "ticker": ticker,
            "issuer": f"{ticker} Inc",
            "name": name,
            "title": random.choice(titles),
            "is_board_director": random.random() > 0.7,  # 30% chance of being a director
            "transaction_date": transaction_date,
            "transaction_shares": transaction_shares,
            "transaction_price_per_share": transaction_price if random.random() > 0.2 else None,
            "transaction_value": transaction_value if random.random() > 0.2 else None,
            "shares_owned_before_transaction": shares_before,
            "shares_owned_after_transaction": shares_after,
            "security_title": random.choice(security_titles),
            "filing_date": filing_date
        })
    
    # Sort by filing date (most recent first)
    insider_trades.sort(key=lambda x: x["filing_date"], reverse=True)
    
    return {"insider_trades": insider_trades}

def generate_mock_company_news(ticker, end_date):
    """Generate mock company news for a ticker."""
    print(f"Generating mock company news for {ticker}...")
    
    # Convert end_date to datetime
    end = datetime.strptime(end_date, "%Y-%m-%d")
    
    # Generate random number of news articles
    num_articles = random.randint(10, 30)
    
    # List of possible news sources
    sources = ["Bloomberg", "Reuters", "CNBC", "Wall Street Journal", "Financial Times", "Benzinga", "The Motley Fool"]
    
    # List of possible authors
    authors = ["John Smith", "Jane Doe", "Michael Johnson", "Sarah Williams", "David Brown", "Lisa Davis", "Robert Miller"]
    
    # List of possible sentiments
    sentiments = ["positive", "negative", "neutral"]
    
    # List of possible headline templates
    headline_templates = [
        f"{ticker} Reports Q{{quarter}} Earnings: {{sentiment}} Surprise",
        f"{ticker} Announces New Product Line",
        f"{ticker} CEO Discusses Future Growth Plans",
        f"Analysts {{sentiment}} on {ticker}'s Latest Moves",
        f"{ticker} Stock {{movement}} After Earnings Report",
        f"Is {ticker} a Buy Right Now?",
        f"{ticker} Faces {{sentiment}} Outlook Amid Market Uncertainty",
        f"Why {ticker} Stock {{movement}} Today",
        f"{ticker} Announces Partnership with {{partner}}",
        f"Investors React to {ticker}'s Latest Announcement"
    ]
    
    news = []
    for _ in range(num_articles):
        # Generate random date
        days_ago = random.randint(1, 90)
        article_date = (end - timedelta(days=days_ago))
        iso_date = article_date.strftime("%Y-%m-%dT%H:%M:%SZ")
        
        # Generate random headline
        template = random.choice(headline_templates)
        sentiment_word = random.choice(["Bullish", "Bearish", "Neutral", "Optimistic", "Pessimistic", "Cautious"])
        movement = random.choice(["Jumps", "Drops", "Surges", "Plunges", "Rises", "Falls", "Climbs", "Dips"])
        quarter = random.randint(1, 4)
        partner = random.choice(["Microsoft", "Google", "Amazon", "Apple", "Meta", "IBM", "Oracle"])
        
        headline = template.format(
            quarter=quarter,
            sentiment=sentiment_word,
            movement=movement,
            partner=partner
        )
        
        # Generate random sentiment
        sentiment_weights = {"positive": 0.4, "negative": 0.4, "neutral": 0.2}
        sentiment = random.choices(list(sentiment_weights.keys()), weights=list(sentiment_weights.values()))[0]
        
        # Generate fake URL
        url = f"https://www.example.com/news/{ticker.lower()}/{article_date.strftime('%Y/%m/%d')}/{headline.lower().replace(' ', '-')}"
        
        news.append({
            "ticker": ticker,
            "title": headline,
            "author": random.choice(authors),
            "source": random.choice(sources),
            "date": iso_date,
            "url": url,
            "sentiment": sentiment
        })
    
    # Sort by date (most recent first)
    news.sort(key=lambda x: x["date"], reverse=True)
    
    return {"news": news}

def save_to_csv(data, filename):
    """Save data to CSV file."""
    if data is None:
        print(f"No data to save for {filename}")
        return
    
    # Save raw JSON for reference
    with open(f"mock_financial_data/{filename}_raw.json", "w") as f:
        json.dump(data, f, indent=2)
    
    # Convert to DataFrame and save as CSV
    try:
        if "prices" in data:
            df = pd.DataFrame(data.get("prices", []))
        elif "financial_metrics" in data:
            df = pd.DataFrame(data.get("financial_metrics", []))
        elif "search_results" in data:
            df = pd.DataFrame(data.get("search_results", []))
        elif "insider_trades" in data:
            df = pd.DataFrame(data.get("insider_trades", []))
        elif "news" in data:
            df = pd.DataFrame(data.get("news", []))
        else:
            df = pd.DataFrame([data])
        
        if not df.empty:
            df.to_csv(f"mock_financial_data/{filename}.csv", index=False)
            print(f"Saved {filename} to CSV")
        else:
            print(f"No data found in the response for {filename}")
    except Exception as e:
        print(f"Error converting {filename} data to CSV: {e}")

def generate_mock_data_for_ticker(ticker):
    """Generate all mock data for a ticker."""
    print(f"Generating mock data for {ticker}...")
    
    # Set up dates
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
    
    # Generate and save price data
    price_data = generate_mock_prices(ticker, start_date, end_date)
    save_to_csv(price_data, f"prices_{ticker}")
    
    # Generate and save financial metrics
    metrics_data = generate_mock_financial_metrics(ticker, end_date)
    save_to_csv(metrics_data, f"financial_metrics_{ticker}")
    
    # Generate and save line items
    line_items = ["revenue", "net_income"]
    line_items_data = generate_mock_line_items(ticker, line_items, end_date)
    save_to_csv(line_items_data, f"line_items_{ticker}")
    
    # Generate and save insider trades
    insider_trades_data = generate_mock_insider_trades(ticker, end_date)
    save_to_csv(insider_trades_data, f"insider_trades_{ticker}")
    
    # Generate and save company news
    company_news_data = generate_mock_company_news(ticker, end_date)
    save_to_csv(company_news_data, f"company_news_{ticker}")
    
    print(f"Mock data generation complete for {ticker}!")

# Example usage
if __name__ == "__main__":
    # Generate mock data for a custom ticker
    ticker = input("Enter a ticker symbol to generate mock data for (e.g., AMD): ")
    if not ticker:
        ticker = "AMD"
    
    generate_mock_data_for_ticker(ticker.upper())
