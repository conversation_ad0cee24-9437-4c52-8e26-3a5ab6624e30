"""
<PERSON><PERSON><PERSON> to integrate the mock sentiment analysis into the AI Hedge Fund workflow.
This script doesn't modify any existing files.
"""

import sys
import os
import json
from datetime import datetime
from dateutil.relativedelta import relativedelta
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Import the mock sentiment analysis function
from run_mock_analyst import mock_sentiment_analysis

print(f"{Fore.GREEN}Setting up mock environment...{Style.RESET_ALL}")

# Define a function to run the AI Hedge Fund with mock sentiment analysis
def run_hedge_fund_with_mock(ticker, end_date=None):
    """
    Run the AI Hedge Fund with mock sentiment analysis.
    """
    # If end_date is not provided, use today's date
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")
    
    # Set start_date to 3 months before end_date
    end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")
    start_date_dt = end_date_dt - relativedelta(months=3)
    start_date = start_date_dt.strftime("%Y-%m-%d")
    
    print(f"\nRunning AI Hedge Fund for {Fore.CYAN}{ticker}{Style.RESET_ALL} from {start_date} to {end_date}...")
    
    # Run the mock sentiment analysis
    sentiment_analysis = mock_sentiment_analysis(ticker, end_date)
    
    # Generate a mock portfolio management decision
    ticker_sentiment = sentiment_analysis[ticker]
    
    if ticker_sentiment["signal"] == "bullish" and ticker_sentiment["confidence"] > 60:
        action = "buy"
        quantity = 100
        confidence = ticker_sentiment["confidence"]
        reasoning = f"Strong bullish signal with {confidence:.1f}% confidence. Buying 100 shares."
    elif ticker_sentiment["signal"] == "bearish" and ticker_sentiment["confidence"] > 60:
        action = "sell"
        quantity = 100
        confidence = ticker_sentiment["confidence"]
        reasoning = f"Strong bearish signal with {confidence:.1f}% confidence. Selling 100 shares."
    else:
        action = "hold"
        quantity = 0
        confidence = ticker_sentiment["confidence"]
        reasoning = f"Neutral or low confidence signal ({confidence:.1f}%). Holding current position."
    
    # Create the portfolio management decision
    portfolio_decision = {
        ticker: {
            "action": action,
            "quantity": quantity,
            "confidence": confidence,
            "reasoning": reasoning
        }
    }
    
    # Save the portfolio decision to a file
    with open(f"{ticker}_portfolio_decision.json", "w") as f:
        json.dump(portfolio_decision, f, indent=2)
    
    print(f"\n{Fore.GREEN}Portfolio Decision for {ticker}:{Style.RESET_ALL}")
    print(f"Action: {Fore.CYAN if action == 'buy' else Fore.RED if action == 'sell' else Fore.YELLOW}{action.upper()}{Style.RESET_ALL}")
    print(f"Quantity: {quantity}")
    print(f"Confidence: {confidence:.1f}%")
    print(f"Reasoning: {reasoning}")
    
    print(f"\nDecision saved to {Fore.GREEN}{ticker}_portfolio_decision.json{Style.RESET_ALL}")
    
    # Print a summary table
    print(f"\n{Fore.GREEN}Analysis for {ticker}{Style.RESET_ALL}")
    print("=" * 50)
    print(f"\n{Fore.CYAN}AGENT ANALYSIS: [{ticker}]{Style.RESET_ALL}")
    print(f"+----------------+----------+--------------+--------------------------------------------------------------+")
    print(f"| Agent          |  Signal  |   Confidence | Reasoning                                                    |")
    print(f"+================+==========+==============+==============================================================+")
    signal_color = Fore.CYAN if ticker_sentiment["signal"] == "bullish" else Fore.RED if ticker_sentiment["signal"] == "bearish" else Fore.YELLOW
    print(f"| Mock Sentiment | {signal_color}{ticker_sentiment['signal'].upper():8}{Style.RESET_ALL} | {ticker_sentiment['confidence']:12.1f}% | {ticker_sentiment['reasoning']:<60} |")
    print(f"+----------------+----------+--------------+--------------------------------------------------------------+")
    
    print(f"\n{Fore.CYAN}TRADING DECISION: [{ticker}]{Style.RESET_ALL}")
    print(f"+------------+---------------------------------------------------+")
    action_color = Fore.CYAN if action == "buy" else Fore.RED if action == "sell" else Fore.YELLOW
    print(f"| Action     | {action_color}{action.upper():<48}{Style.RESET_ALL} |")
    print(f"+------------+---------------------------------------------------+")
    print(f"| Quantity   | {quantity:<49} |")
    print(f"+------------+---------------------------------------------------+")
    print(f"| Confidence | {confidence:<48.1f}% |")
    print(f"+------------+---------------------------------------------------+")
    print(f"| Reasoning  | {reasoning:<49} |")
    print(f"+------------+---------------------------------------------------+")
    
    return {
        "sentiment_analysis": sentiment_analysis,
        "portfolio_decision": portfolio_decision
    }

if __name__ == "__main__":
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run AI Hedge Fund with mock sentiment analysis.')
    parser.add_argument('ticker', type=str, help='Stock ticker symbol (e.g., AMD, AAPL)')
    parser.add_argument('--date', type=str, help='End date for analysis (YYYY-MM-DD format)', default=None)
    args = parser.parse_args()
    
    # Run the analysis
    run_hedge_fund_with_mock(args.ticker, args.date)
