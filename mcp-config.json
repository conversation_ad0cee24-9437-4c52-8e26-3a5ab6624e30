{"name": "Screener Ratio Analysis MCP", "version": "1.0.0", "description": "MCP server for automated ratio analysis extraction from Screener.in using Microsoft Edge", "tools": [{"name": "extract_screener_ratios", "description": "Extract comprehensive financial ratios from Screener.in for a given stock symbol", "inputSchema": {"type": "object", "properties": {"symbol": {"type": "string", "description": "Stock symbol (e.g., RELIANCE, TCS, INFY)", "default": "RELIANCE"}, "headless": {"type": "boolean", "description": "Run browser in headless mode", "default": false}, "useExistingBrowser": {"type": "boolean", "description": "Try to connect to existing Edge browser session", "default": false}}, "required": ["symbol"]}}, {"name": "navigate_to_ratio_analysis", "description": "Navigate to ratio analysis page and extract data", "inputSchema": {"type": "object", "properties": {"url": {"type": "string", "description": "Screener.in company URL", "pattern": "^https://www\\.screener\\.in/company/[A-Z]+/$"}}, "required": ["url"]}}, {"name": "test_edge_connection", "description": "Test Microsoft Edge browser connection and automation capabilities", "inputSchema": {"type": "object", "properties": {"testType": {"type": "string", "enum": ["basic", "navigation", "full"], "description": "Type of test to perform", "default": "basic"}}}}], "workflows": [{"name": "full_ratio_analysis", "description": "Complete workflow: navigate to company page, find Smart Analyze, click Ratio Analysis, extract all data", "steps": [{"tool": "test_edge_connection", "params": {"testType": "basic"}}, {"tool": "extract_screener_ratios", "params": {"symbol": "{{ input.symbol }}", "headless": false}}]}], "browserConfig": {"browser": "msedge", "executablePath": "auto-detect", "args": ["--start-maximized", "--disable-blink-features=AutomationControlled", "--no-first-run", "--disable-infobars", "--remote-debugging-port=9222"], "defaultViewport": null, "headless": false}, "outputFormats": [{"name": "json", "description": "Complete ratio data in JSON format", "filename": "{symbol}_comprehensive_ratios_{timestamp}.json"}, {"name": "csv", "description": "Year-wise ratio summary in CSV format", "filename": "{symbol}_ratios_summary_{timestamp}.csv"}, {"name": "screenshots", "description": "Screenshots of each step for debugging", "pattern": "{symbol}_step_{number}_{description}.png"}]}