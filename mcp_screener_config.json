{"mcpServers": {"screener-financial-ratios": {"command": "python", "args": ["screener_mcp_final.py"], "cwd": "C:\\Users\\<USER>\\Downloads\\study\\ssd backup\\Projects\\git\\vs insider\\stock17", "env": {"PYTHONPATH": "C:\\Users\\<USER>\\Downloads\\study\\ssd backup\\Projects\\git\\vs insider\\stock17", "CONDA_DEFAULT_ENV": "stock17"}, "description": "Extracts comprehensive financial ratios from screener.in using existing Edge browser", "capabilities": ["financial_data_extraction", "ratio_analysis", "multi_stock_support", "aapl_format_compatibility"], "supported_functions": ["extract_screener_financial_ratios"], "data_sources": ["screener.in"], "output_formats": ["CSV (AAPL-compatible)", "JSON (structured)", "Raw data backup"], "supported_stocks": ["RELIANCE", "TCS", "INFY", "HDFCBANK", "ICICIBANK", "HINDUNILVR", "ITC", "SBIN", "BHARTIARTL", "KOTAKBANK", "Any stock available on screener.in"], "ratio_categories": ["Lev<PERSON><PERSON> (11 metrics)", "Efficiency Ratios (7 metrics)", "Profitability Ratios (18 metrics)", "Capital Allocation Ratios (7 metrics)", "Valuation Ratios (6 metrics)"], "historical_data": "12 years (Mar 2014 to Mar 2025)", "requirements": ["Edge browser with debug mode (port 9222)", "screener.in login credentials", "Python environment with playwright, pandas"], "setup_commands": ["conda activate stock17", "pip install playwright pandas", "Start-Process -FilePath \"C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe\" -ArgumentList \"--remote-debugging-port=9222\", \"--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Edge\\User Data\""]}}}