"""
Script to scrape financial data from multiple financial websites for a given company.
Combines data from screener.in, dhan.co, goodreturns.in, trendlyne.com,
investing.com, and stockanalysis.com.
"""

import asyncio
from playwright.async_api import async_playwright
import pandas as pd
import os
import json
import re
from datetime import datetime
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Custom JSON encoder to handle DataFrames
class DataFrameEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, pd.DataFrame):
            return obj.to_dict(orient='records')
        return super().default(obj)

# Main function to orchestrate scraping from all sources
async def scrape_all_sources(company_symbol="RELIANCE", output_dir=None):
    """
    Scrape financial data from multiple sources for a given company and save it to CSV.

    Args:
        company_symbol (str): Stock symbol of the company (default: RELIANCE)
        output_dir (str, optional): Directory to save CSV files. If None, creates a directory based on company symbol.
    """
    print(f"{Fore.CYAN}Starting multi-source scraping for {company_symbol}...{Style.RESET_ALL}")

    # Create output directory if it doesn't exist
    if output_dir is None:
        output_dir = f"{company_symbol}_multi_source_data"

    os.makedirs(output_dir, exist_ok=True)

    # Dictionary to store all data from different sources
    all_data = {}

    # Launch Playwright browser once and reuse for all scrapers
    try:
        async with async_playwright() as p:
            # Launch browser with increased timeout and viewport size
            browser = await p.chromium.launch(headless=True)

            # Define the list of sources to scrape
            sources = [
                ("screener.in", scrape_screener),
                ("dhan.co (technical analysis)", scrape_dhan_technical),
                ("dhan.co (news)", scrape_dhan_news),
                ("goodreturns.in", scrape_goodreturns),
                ("trendlyne.com", scrape_trendlyne),
                ("investing.com", scrape_investing),
                ("stockanalysis.com", scrape_stockanalysis)
            ]

            # Scrape each source, handling errors for each one separately
            for source_name, scraper_func in sources:
                try:
                    print(f"{Fore.CYAN}Scraping from {source_name}...{Style.RESET_ALL}")
                    source_data = await scraper_func(browser, company_symbol, output_dir)

                    # Convert source name to a valid dictionary key
                    source_key = source_name.split(' ')[0].replace('.', '_')
                    all_data[source_key] = source_data

                    print(f"{Fore.GREEN}Successfully scraped data from {source_name}{Style.RESET_ALL}")
                except Exception as e:
                    print(f"{Fore.RED}Error scraping from {source_name}: {e}{Style.RESET_ALL}")
                    # Continue with the next source even if this one fails
                    all_data[source_name.split(' ')[0].replace('.', '_')] = {}

            # Close the browser
            await browser.close()

        # Save combined data
        try:
            # Convert DataFrames to dictionaries for JSON serialization
            serializable_data = {}
            for source, data in all_data.items():
                if isinstance(data, dict):
                    serializable_data[source] = {}
                    for key, value in data.items():
                        if isinstance(value, pd.DataFrame):
                            serializable_data[source][key] = value.to_dict(orient='records')
                        else:
                            serializable_data[source][key] = value
                else:
                    serializable_data[source] = data

            combined_data_path = os.path.join(output_dir, "combined_data.json")
            with open(combined_data_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_data, f, indent=2, cls=DataFrameEncoder)

            print(f"{Fore.GREEN}All scraping completed. Combined data saved to {combined_data_path}{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}Error saving combined data: {e}{Style.RESET_ALL}")

        # Create a unified dataset with key metrics from all sources
        create_unified_dataset(all_data, output_dir, company_symbol)

        return all_data

    except Exception as e:
        print(f"{Fore.RED}Critical error in scraping process: {e}{Style.RESET_ALL}")

        # Save whatever data we have so far
        try:
            if all_data:
                # Convert DataFrames to dictionaries for JSON serialization
                serializable_data = {}
                for source, data in all_data.items():
                    if isinstance(data, dict):
                        serializable_data[source] = {}
                        for key, value in data.items():
                            if isinstance(value, pd.DataFrame):
                                serializable_data[source][key] = value.to_dict(orient='records')
                            else:
                                serializable_data[source][key] = value
                    else:
                        serializable_data[source] = data

                error_data_path = os.path.join(output_dir, "partial_data.json")
                with open(error_data_path, 'w', encoding='utf-8') as f:
                    json.dump(serializable_data, f, indent=2, cls=DataFrameEncoder)
                print(f"{Fore.YELLOW}Saved partial data to {error_data_path}{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}Error saving partial data: {e}{Style.RESET_ALL}")

        return all_data

# Scraper for screener.in
async def scrape_screener(browser, company_symbol, output_dir):
    """
    Scrape financial data from screener.in for a given company.

    Args:
        browser: Playwright browser instance
        company_symbol: Stock symbol of the company
        output_dir: Directory to save CSV files

    Returns:
        dict: Dictionary containing extracted data
    """
    # Create a subdirectory for screener data
    screener_dir = os.path.join(output_dir, "screener")
    os.makedirs(screener_dir, exist_ok=True)

    # URL for screener.in
    company_url = f"https://www.screener.in/company/{company_symbol}/"
    print(f"{Fore.CYAN}Scraping data from {company_url}...{Style.RESET_ALL}")

    # Create a new page
    page = await browser.new_page()

    # Go to the URL
    await page.goto(company_url, wait_until="networkidle")

    # Wait for the content to load
    await page.wait_for_selector('table')

    # Extract company name
    company_name = await page.locator('h1.margin-0').text_content()
    company_name = company_name.strip()
    print(f"{Fore.GREEN}Scraping screener.in data for {company_name}{Style.RESET_ALL}")

    # Dictionary to store all tables
    all_tables = {}

    # Extract all tables
    tables = await page.locator('table').all()

    # Process each table
    for i, table in enumerate(tables):
        # Get table title if available
        table_title = None

        # Try to find the heading for this table
        headings = await page.locator('h2, h3, h4').all()
        for heading in headings:
            heading_text = await heading.text_content()
            heading_text = heading_text.strip()

            # Check if this heading is before the table
            heading_box = await heading.bounding_box()
            table_box = await table.bounding_box()

            if heading_box and table_box and heading_box['y'] < table_box['y']:
                # Check if there's no other table between this heading and our table
                is_closest = True
                for other_table in tables:
                    if other_table != table:
                        other_box = await other_table.bounding_box()
                        if other_box and heading_box['y'] < other_box['y'] < table_box['y']:
                            is_closest = False
                            break

                if is_closest:
                    table_title = heading_text
                    break

        if not table_title:
            table_title = f"Table_{i+1}"

        # Extract table data
        table_data = await extract_table_data(table)

        if table_data and table_data['headers'] and (table_data['rows'] or table_data.get('is_financial_table', False)):
            # Create DataFrame based on table type
            if table_data.get('is_financial_table', False) and 'row_labels' in table_data:
                # For financial tables, create a properly structured DataFrame
                columns = table_data['headers'][1:] if len(table_data['headers']) > 1 else []

                # Create DataFrame with row labels as the first column
                df = pd.DataFrame(table_data['rows'], columns=columns)

                # Add row labels as the first column
                df.insert(0, 'Metric', table_data['row_labels'])

                # Clean up column names (remove special characters)
                df.columns = [col.replace('Â', '').replace('+', '').strip() for col in df.columns]

                # Convert numeric columns to appropriate types
                for col in df.columns[1:]:  # Skip the 'Metric' column
                    try:
                        # Try to convert to numeric, coercing errors to NaN
                        df[col] = pd.to_numeric(df[col].str.replace(',', ''), errors='coerce')
                    except:
                        pass  # Keep as string if conversion fails
            else:
                # For regular tables, create a standard DataFrame
                df = pd.DataFrame(table_data['rows'], columns=table_data['headers'])

            # Check if this table title already exists
            if table_title in all_tables:
                # Append a number to make it unique
                j = 1
                while f"{table_title}_{j}" in all_tables:
                    j += 1
                table_title = f"{table_title}_{j}"

            all_tables[table_title] = df

            print(f"{Fore.GREEN}Extracted screener.in table: {table_title} with {len(df)} rows and {len(df.columns)} columns{Style.RESET_ALL}")

            # Save to CSV
            save_dataframe(df, screener_dir, table_title, "screener")

    # Try to extract data from JavaScript
    js_data = await page.evaluate('''() => {
        // Try to find data in the window object
        if (window.data) {
            return window.data;
        }

        // Try to find data in script tags
        const scripts = document.querySelectorAll('script');
        for (const script of scripts) {
            const content = script.textContent;
            if (content && content.includes('var data =')) {
                const match = content.match(/var data = (\{.*?\});/s);
                if (match && match[1]) {
                    try {
                        return JSON.parse(match[1]);
                    } catch (e) {
                        console.error('Error parsing JSON:', e);
                    }
                }
            }
        }

        return null;
    }''')

    if js_data:
        print(f"{Fore.GREEN}Found JavaScript data in screener.in{Style.RESET_ALL}")

        # Save the raw JavaScript data
        js_filepath = os.path.join(screener_dir, "js_data.json")
        try:
            with open(js_filepath, 'w', encoding='utf-8') as f:
                json.dump(js_data, f, indent=2)
            print(f"{Fore.GREEN}Saved screener.in JavaScript data to {js_filepath}{Style.RESET_ALL}")
        except PermissionError:
            print(f"{Fore.RED}Permission denied when trying to write to {js_filepath}. Skipping this file.{Style.RESET_ALL}")

        # Process each section in the JavaScript data
        for section_name, section_data in js_data.items():
            if isinstance(section_data, dict) and 'data' in section_data:
                # Extract table data
                table_data = section_data['data']
                if table_data:
                    # Create DataFrame
                    df = pd.DataFrame(table_data)

                    # Save to CSV
                    save_dataframe(df, screener_dir, f"{section_name}_js", "screener")

    # Close the page
    await page.close()

    return all_tables

# Scraper for dhan.co technical analysis
async def scrape_dhan_technical(browser, company_symbol, output_dir):
    """
    Scrape technical analysis data from dhan.co for a given company.

    Args:
        browser: Playwright browser instance
        company_symbol: Stock symbol of the company
        output_dir: Directory to save CSV files

    Returns:
        dict: Dictionary containing extracted data
    """
    # Create a subdirectory for dhan technical data
    dhan_tech_dir = os.path.join(output_dir, "dhan_technical")
    os.makedirs(dhan_tech_dir, exist_ok=True)

    # URL for dhan.co technical analysis
    # For Reliance, the URL format is different from the symbol
    company_url = f"https://dhan.co/stocks/reliance-industries-ltd-technical-analysis/"
    print(f"{Fore.CYAN}Scraping data from {company_url}...{Style.RESET_ALL}")

    # Create a new page
    page = await browser.new_page()

    # Go to the URL
    try:
        await page.goto(company_url, wait_until="networkidle", timeout=60000)

        # Wait for any content to load
        try:
            await page.wait_for_selector('table, div.stock-detail-container, div.technical-analysis-container', timeout=30000)
        except Exception as e:
            print(f"{Fore.YELLOW}Warning: Could not find expected selectors on dhan.co: {e}{Style.RESET_ALL}")
            # Take a screenshot to see what's on the page
            screenshot_path = os.path.join(dhan_tech_dir, "dhan_page_screenshot.png")
            await page.screenshot(path=screenshot_path)
            print(f"{Fore.YELLOW}Saved screenshot to {screenshot_path}{Style.RESET_ALL}")

        # Dictionary to store all extracted data
        all_data = {}

        # Extract technical indicators
        try:
            # Extract technical indicators table
            tech_indicators = await page.locator('table').first
            if tech_indicators:
                table_data = await extract_table_data(tech_indicators)
                if table_data and table_data['headers'] and table_data['rows']:
                    df = pd.DataFrame(table_data['rows'], columns=table_data['headers'])
                    all_data['technical_indicators'] = df
                    save_dataframe(df, dhan_tech_dir, "technical_indicators", "dhan")
                    print(f"{Fore.GREEN}Extracted technical indicators with {len(df)} rows{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}Error extracting technical indicators: {e}{Style.RESET_ALL}")

        # Extract all tables as a fallback
        try:
            # Find all tables
            tables = await page.locator('table').all()

            for i, table in enumerate(tables):
                try:
                    table_data = await extract_table_data(table)
                    if table_data and table_data['headers'] and table_data['rows']:
                        df = pd.DataFrame(table_data['rows'], columns=table_data['headers'])
                        table_name = f"Table_{i+1}"
                        all_data[table_name] = df
                        save_dataframe(df, dhan_tech_dir, table_name, "dhan")
                        print(f"{Fore.GREEN}Extracted dhan.co table {table_name} with {len(df)} rows{Style.RESET_ALL}")
                except Exception as e:
                    print(f"{Fore.RED}Error extracting table {i+1}: {e}{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}Error extracting tables: {e}{Style.RESET_ALL}")

        # Extract HTML content for manual inspection
        try:
            html_content = await page.content()
            html_path = os.path.join(dhan_tech_dir, "dhan_page.html")
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"{Fore.GREEN}Saved page HTML to {html_path} for inspection{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}Error saving HTML content: {e}{Style.RESET_ALL}")

        # Close the page
        await page.close()

        return all_data

    except Exception as e:
        print(f"{Fore.RED}Error accessing dhan.co: {e}{Style.RESET_ALL}")
        await page.close()
        return {}

# Scraper for dhan.co news
async def scrape_dhan_news(browser, company_symbol, output_dir):
    """
    Scrape news data from dhan.co for a given company.

    Args:
        browser: Playwright browser instance
        company_symbol: Stock symbol of the company
        output_dir: Directory to save CSV files

    Returns:
        dict: Dictionary containing extracted data
    """
    # Create a subdirectory for dhan news data
    dhan_news_dir = os.path.join(output_dir, "dhan_news")
    os.makedirs(dhan_news_dir, exist_ok=True)

    # URL for dhan.co news
    company_url = f"https://dhan.co/stocks/reliance-industries-ltd-news/"
    print(f"{Fore.CYAN}Scraping news from {company_url}...{Style.RESET_ALL}")

    # Create a new page
    page = await browser.new_page()

    # Go to the URL
    try:
        await page.goto(company_url, wait_until="networkidle", timeout=60000)

        # Wait for any content to load
        try:
            await page.wait_for_selector('div.news-container, article, .news-item, .news-article', timeout=30000)
        except Exception as e:
            print(f"{Fore.YELLOW}Warning: Could not find expected news selectors on dhan.co: {e}{Style.RESET_ALL}")
            # Take a screenshot to see what's on the page
            screenshot_path = os.path.join(dhan_news_dir, "dhan_news_screenshot.png")
            await page.screenshot(path=screenshot_path)
            print(f"{Fore.YELLOW}Saved screenshot to {screenshot_path}{Style.RESET_ALL}")

        # Dictionary to store all extracted data
        all_news = []

        # Extract news articles
        try:
            # Try different selectors for news items
            selectors = ['div.news-item', 'article', '.news-article', '.news-card']
            news_items = []

            for selector in selectors:
                items = await page.locator(selector).all()
                if items:
                    news_items = items
                    print(f"{Fore.GREEN}Found news items using selector: {selector}{Style.RESET_ALL}")
                    break

            if not news_items:
                # Fallback: try to find any divs with news-related classes
                news_items = await page.locator('div:has-text("news")').all()
                print(f"{Fore.YELLOW}Using fallback selector, found {len(news_items)} potential news items{Style.RESET_ALL}")

            for item in news_items:
                news_data = {}

                # Extract title (try different selectors)
                try:
                    title_selectors = ['h3.news-title', 'h3', 'h2', '.title', '.headline']
                    for selector in title_selectors:
                        title_element = await item.locator(selector).first
                        if title_element:
                            news_data['title'] = await title_element.text_content()
                            break

                    if 'title' not in news_data or not news_data['title']:
                        # Fallback: get the first text node
                        news_data['title'] = await item.text_content()
                except Exception as e:
                    print(f"{Fore.YELLOW}Error extracting title: {e}{Style.RESET_ALL}")
                    news_data['title'] = ""

                # Extract date
                try:
                    date_selectors = ['div.news-date', '.date', '.time', 'time']
                    for selector in date_selectors:
                        date_element = await item.locator(selector).first
                        if date_element:
                            news_data['date'] = await date_element.text_content()
                            break
                except Exception as e:
                    print(f"{Fore.YELLOW}Error extracting date: {e}{Style.RESET_ALL}")
                    news_data['date'] = ""

                # Extract source
                try:
                    source_selectors = ['div.news-source', '.source', '.publisher']
                    for selector in source_selectors:
                        source_element = await item.locator(selector).first
                        if source_element:
                            news_data['source'] = await source_element.text_content()
                            break
                except Exception as e:
                    print(f"{Fore.YELLOW}Error extracting source: {e}{Style.RESET_ALL}")
                    news_data['source'] = ""

                # Extract summary
                try:
                    summary_selectors = ['div.news-summary', '.summary', '.description', 'p']
                    for selector in summary_selectors:
                        summary_element = await item.locator(selector).first
                        if summary_element:
                            news_data['summary'] = await summary_element.text_content()
                            break
                except Exception as e:
                    print(f"{Fore.YELLOW}Error extracting summary: {e}{Style.RESET_ALL}")
                    news_data['summary'] = ""

                # Extract URL if available
                try:
                    link_element = await item.locator('a').first
                    if link_element:
                        news_data['url'] = await link_element.get_attribute('href')
                except Exception as e:
                    print(f"{Fore.YELLOW}Error extracting URL: {e}{Style.RESET_ALL}")
                    news_data['url'] = ""

                # Only add if we have at least a title
                if news_data['title'].strip():
                    all_news.append(news_data)

            # Save news data to CSV
            if all_news:
                df = pd.DataFrame(all_news)
                save_dataframe(df, dhan_news_dir, "news_articles", "dhan")
                print(f"{Fore.GREEN}Extracted {len(all_news)} news articles{Style.RESET_ALL}")
            else:
                print(f"{Fore.YELLOW}No news articles found{Style.RESET_ALL}")

            # Save raw news data to JSON
            news_filepath = os.path.join(dhan_news_dir, "news_data.json")
            with open(news_filepath, 'w', encoding='utf-8') as f:
                json.dump(all_news, f, indent=2)
            print(f"{Fore.GREEN}Saved news data to {news_filepath}{Style.RESET_ALL}")

        except Exception as e:
            print(f"{Fore.RED}Error extracting news: {e}{Style.RESET_ALL}")

        # Extract HTML content for manual inspection
        try:
            html_content = await page.content()
            html_path = os.path.join(dhan_news_dir, "dhan_news_page.html")
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"{Fore.GREEN}Saved page HTML to {html_path} for inspection{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}Error saving HTML content: {e}{Style.RESET_ALL}")

        # Close the page
        await page.close()

        return all_news

    except Exception as e:
        print(f"{Fore.RED}Error accessing dhan.co news: {e}{Style.RESET_ALL}")
        await page.close()
        return []

# Scraper for goodreturns.in
async def scrape_goodreturns(browser, company_symbol, output_dir):
    """
    Scrape financial ratios from goodreturns.in for a given company.

    Args:
        browser: Playwright browser instance
        company_symbol: Stock symbol of the company
        output_dir: Directory to save CSV files

    Returns:
        dict: Dictionary containing extracted data
    """
    # Create a subdirectory for goodreturns data
    goodreturns_dir = os.path.join(output_dir, "goodreturns")
    os.makedirs(goodreturns_dir, exist_ok=True)

    # URL for goodreturns.in ratios
    company_url = f"https://www.goodreturns.in/company/reliance-industries/ratios.html"
    print(f"{Fore.CYAN}Scraping data from {company_url}...{Style.RESET_ALL}")

    # Create a new page
    page = await browser.new_page()

    # Go to the URL
    await page.goto(company_url, wait_until="networkidle")

    # Wait for the content to load
    await page.wait_for_selector('table')

    # Dictionary to store all tables
    all_tables = {}

    # Extract all tables
    tables = await page.locator('table').all()

    # Process each table
    for i, table in enumerate(tables):
        # Try to find the heading for this table
        table_title = f"Table_{i+1}"

        # Look for table caption or nearby heading
        try:
            caption = await table.locator('caption').first
            if caption:
                caption_text = await caption.text_content()
                if caption_text.strip():
                    table_title = caption_text.strip()
        except:
            pass

        # If no caption, try to find nearby heading
        if table_title == f"Table_{i+1}":
            try:
                # Get all headings on the page
                headings = await page.locator('h1, h2, h3, h4, h5').all()

                # Get table position
                table_box = await table.bounding_box()

                # Find the closest heading above the table
                closest_heading = None
                min_distance = float('inf')

                for heading in headings:
                    heading_box = await heading.bounding_box()

                    # Check if heading is above the table
                    if heading_box and table_box and heading_box['y'] < table_box['y']:
                        distance = table_box['y'] - heading_box['y']
                        if distance < min_distance:
                            min_distance = distance
                            closest_heading = heading

                if closest_heading:
                    heading_text = await closest_heading.text_content()
                    if heading_text.strip():
                        table_title = heading_text.strip()
            except:
                pass

        # Extract table data
        table_data = await extract_table_data(table)

        if table_data and table_data['headers'] and table_data['rows']:
            # Create DataFrame
            df = pd.DataFrame(table_data['rows'], columns=table_data['headers'])

            # Check if this table title already exists
            if table_title in all_tables:
                # Append a number to make it unique
                j = 1
                while f"{table_title}_{j}" in all_tables:
                    j += 1
                table_title = f"{table_title}_{j}"

            all_tables[table_title] = df

            print(f"{Fore.GREEN}Extracted goodreturns.in table: {table_title} with {len(df)} rows and {len(df.columns)} columns{Style.RESET_ALL}")

            # Save to CSV
            save_dataframe(df, goodreturns_dir, table_title, "goodreturns")

    # Close the page
    await page.close()

    return all_tables

# Scraper for trendlyne.com
async def scrape_trendlyne(browser, company_symbol, output_dir):
    """
    Scrape performance data from trendlyne.com for a given company.

    Args:
        browser: Playwright browser instance
        company_symbol: Stock symbol of the company
        output_dir: Directory to save CSV files

    Returns:
        dict: Dictionary containing extracted data
    """
    # Create a subdirectory for trendlyne data
    trendlyne_dir = os.path.join(output_dir, "trendlyne")
    os.makedirs(trendlyne_dir, exist_ok=True)

    # URL for trendlyne.com performance
    company_url = f"https://trendlyne.com/share-price/performance/1127/RELIANCE/reliance-industries-ltd/"
    print(f"{Fore.CYAN}Scraping data from {company_url}...{Style.RESET_ALL}")

    # Create a new page
    page = await browser.new_page()

    # Go to the URL
    await page.goto(company_url, wait_until="networkidle")

    # Wait for the content to load
    await page.wait_for_selector('table')

    # Dictionary to store all extracted data
    all_data = {}

    # Extract performance tables
    try:
        # Extract all tables
        tables = await page.locator('table').all()

        # Process each table
        for i, table in enumerate(tables):
            # Try to find the heading for this table
            table_title = f"Performance_Table_{i+1}"

            # Look for nearby heading
            try:
                # Get all headings on the page
                headings = await page.locator('h1, h2, h3, h4, h5, .section-title').all()

                # Get table position
                table_box = await table.bounding_box()

                # Find the closest heading above the table
                closest_heading = None
                min_distance = float('inf')

                for heading in headings:
                    heading_box = await heading.bounding_box()

                    # Check if heading is above the table
                    if heading_box and table_box and heading_box['y'] < table_box['y']:
                        distance = table_box['y'] - heading_box['y']
                        if distance < min_distance:
                            min_distance = distance
                            closest_heading = heading

                if closest_heading:
                    heading_text = await closest_heading.text_content()
                    if heading_text.strip():
                        table_title = heading_text.strip()
            except Exception as e:
                print(f"{Fore.YELLOW}Error finding heading for table {i+1}: {e}{Style.RESET_ALL}")

            # Extract table data
            table_data = await extract_table_data(table)

            if table_data and table_data['headers'] and table_data['rows']:
                # Create DataFrame
                df = pd.DataFrame(table_data['rows'], columns=table_data['headers'])

                # Check if this table title already exists
                if table_title in all_data:
                    # Append a number to make it unique
                    j = 1
                    while f"{table_title}_{j}" in all_data:
                        j += 1
                    table_title = f"{table_title}_{j}"

                all_data[table_title] = df

                print(f"{Fore.GREEN}Extracted trendlyne.com table: {table_title} with {len(df)} rows and {len(df.columns)} columns{Style.RESET_ALL}")

                # Save to CSV
                save_dataframe(df, trendlyne_dir, table_title, "trendlyne")

    except Exception as e:
        print(f"{Fore.RED}Error extracting performance tables: {e}{Style.RESET_ALL}")

    # Extract key metrics
    try:
        # Extract key metrics sections
        metric_sections = await page.locator('.metric-card, .key-metric-card').all()

        key_metrics = {}

        for section in metric_sections:
            try:
                # Extract metric name
                name_element = await section.locator('.metric-name, .card-title').first
                if name_element:
                    metric_name = await name_element.text_content()
                    metric_name = metric_name.strip()
                else:
                    continue

                # Extract metric value
                value_element = await section.locator('.metric-value, .card-value').first
                if value_element:
                    metric_value = await value_element.text_content()
                    metric_value = metric_value.strip()
                else:
                    continue

                key_metrics[metric_name] = metric_value
            except Exception as e:
                print(f"{Fore.YELLOW}Error extracting a metric: {e}{Style.RESET_ALL}")

        if key_metrics:
            all_data['key_metrics'] = key_metrics

            # Save to JSON
            metrics_filepath = os.path.join(trendlyne_dir, "key_metrics.json")
            with open(metrics_filepath, 'w', encoding='utf-8') as f:
                json.dump(key_metrics, f, indent=2)
            print(f"{Fore.GREEN}Saved {len(key_metrics)} key metrics to {metrics_filepath}{Style.RESET_ALL}")

            # Also save as CSV for consistency
            metrics_df = pd.DataFrame(list(key_metrics.items()), columns=['Metric', 'Value'])
            save_dataframe(metrics_df, trendlyne_dir, "key_metrics", "trendlyne")

    except Exception as e:
        print(f"{Fore.RED}Error extracting key metrics: {e}{Style.RESET_ALL}")

    # Close the page
    await page.close()

    return all_data

# Scraper for investing.com
async def scrape_investing(browser, company_symbol, output_dir):
    """
    Scrape financial ratios from investing.com for a given company.

    Args:
        browser: Playwright browser instance
        company_symbol: Stock symbol of the company
        output_dir: Directory to save CSV files

    Returns:
        dict: Dictionary containing extracted data
    """
    # Create a subdirectory for investing.com data
    investing_dir = os.path.join(output_dir, "investing")
    os.makedirs(investing_dir, exist_ok=True)

    # URL for investing.com ratios
    company_url = f"https://in.investing.com/equities/reliance-industries-ratios"
    print(f"{Fore.CYAN}Scraping data from {company_url}...{Style.RESET_ALL}")

    # Create a new page
    page = await browser.new_page()

    # Go to the URL with a user agent to avoid detection
    await page.set_extra_http_headers({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    })

    await page.goto(company_url, wait_until="networkidle")

    # Wait for the content to load
    await page.wait_for_selector('table')

    # Dictionary to store all tables
    all_tables = {}

    # Extract all tables
    tables = await page.locator('table').all()

    # Process each table
    for i, table in enumerate(tables):
        # Try to find the heading for this table
        table_title = f"Ratio_Table_{i+1}"

        # Look for nearby heading
        try:
            # Get all headings on the page
            headings = await page.locator('h2, h3, h4, .sectionHeader').all()

            # Get table position
            table_box = await table.bounding_box()

            # Find the closest heading above the table
            closest_heading = None
            min_distance = float('inf')

            for heading in headings:
                heading_box = await heading.bounding_box()

                # Check if heading is above the table
                if heading_box and table_box and heading_box['y'] < table_box['y']:
                    distance = table_box['y'] - heading_box['y']
                    if distance < min_distance:
                        min_distance = distance
                        closest_heading = heading

            if closest_heading:
                heading_text = await closest_heading.text_content()
                if heading_text.strip():
                    table_title = heading_text.strip()
        except Exception as e:
            print(f"{Fore.YELLOW}Error finding heading for table {i+1}: {e}{Style.RESET_ALL}")

        # Extract table data
        table_data = await extract_table_data(table)

        if table_data and table_data['headers'] and table_data['rows']:
            # Create DataFrame
            df = pd.DataFrame(table_data['rows'], columns=table_data['headers'])

            # Check if this table title already exists
            if table_title in all_tables:
                # Append a number to make it unique
                j = 1
                while f"{table_title}_{j}" in all_tables:
                    j += 1
                table_title = f"{table_title}_{j}"

            all_tables[table_title] = df

            print(f"{Fore.GREEN}Extracted investing.com table: {table_title} with {len(df)} rows and {len(df.columns)} columns{Style.RESET_ALL}")

            # Save to CSV
            save_dataframe(df, investing_dir, table_title, "investing")

    # Close the page
    await page.close()

    return all_tables

# Scraper for stockanalysis.com
async def scrape_stockanalysis(browser, company_symbol, output_dir):
    """
    Scrape financial ratios from stockanalysis.com for a given company.

    Args:
        browser: Playwright browser instance
        company_symbol: Stock symbol of the company
        output_dir: Directory to save CSV files

    Returns:
        dict: Dictionary containing extracted data
    """
    # Create a subdirectory for stockanalysis data
    stockanalysis_dir = os.path.join(output_dir, "stockanalysis")
    os.makedirs(stockanalysis_dir, exist_ok=True)

    # URL for stockanalysis.com ratios
    company_url = f"https://stockanalysis.com/quote/nse/RELIANCE/financials/ratios/"
    print(f"{Fore.CYAN}Scraping data from {company_url}...{Style.RESET_ALL}")

    # Create a new page
    page = await browser.new_page()

    # Go to the URL
    await page.goto(company_url, wait_until="networkidle")

    # Wait for the content to load - this site uses React, so we need to wait for the data to load
    await page.wait_for_selector('table')

    # Dictionary to store all tables
    all_tables = {}

    # Extract main financial ratios table
    try:
        # The main table is usually the first one
        main_table = await page.locator('table').first
        if main_table:
            # Extract table data
            table_data = await extract_table_data(main_table)

            if table_data and table_data['headers'] and table_data['rows']:
                # Create DataFrame
                df = pd.DataFrame(table_data['rows'], columns=table_data['headers'])

                all_tables['Financial_Ratios'] = df

                print(f"{Fore.GREEN}Extracted stockanalysis.com main ratios table with {len(df)} rows and {len(df.columns)} columns{Style.RESET_ALL}")

                # Save to CSV
                save_dataframe(df, stockanalysis_dir, "Financial_Ratios", "stockanalysis")
    except Exception as e:
        print(f"{Fore.RED}Error extracting main ratios table: {e}{Style.RESET_ALL}")

    # Extract other ratio sections
    try:
        # Find all section headings
        section_headings = await page.locator('h2, h3').all()

        for heading in section_headings:
            heading_text = await heading.text_content()
            heading_text = heading_text.strip()

            # Skip if not a ratio section
            if not any(keyword in heading_text.lower() for keyword in ['ratio', 'margin', 'growth', 'return', 'valuation']):
                continue

            # Get the heading position
            heading_box = await heading.bounding_box()

            # Find the closest table below this heading
            tables = await page.locator('table').all()
            closest_table = None
            min_distance = float('inf')

            for table in tables:
                table_box = await table.bounding_box()

                # Check if table is below the heading
                if table_box and heading_box and table_box['y'] > heading_box['y']:
                    distance = table_box['y'] - heading_box['y']
                    if distance < min_distance:
                        min_distance = distance
                        closest_table = table

            if closest_table:
                # Extract table data
                table_data = await extract_table_data(closest_table)

                if table_data and table_data['headers'] and table_data['rows']:
                    # Create DataFrame
                    df = pd.DataFrame(table_data['rows'], columns=table_data['headers'])

                    # Clean up section name for filename
                    section_name = re.sub(r'[^\w\s]', '', heading_text).strip().replace(' ', '_')

                    # Check if this section name already exists
                    if section_name in all_tables:
                        # Append a number to make it unique
                        j = 1
                        while f"{section_name}_{j}" in all_tables:
                            j += 1
                        section_name = f"{section_name}_{j}"

                    all_tables[section_name] = df

                    print(f"{Fore.GREEN}Extracted stockanalysis.com section: {heading_text} with {len(df)} rows and {len(df.columns)} columns{Style.RESET_ALL}")

                    # Save to CSV
                    save_dataframe(df, stockanalysis_dir, section_name, "stockanalysis")
    except Exception as e:
        print(f"{Fore.RED}Error extracting ratio sections: {e}{Style.RESET_ALL}")

    # Close the page
    await page.close()

    return all_tables

# Helper function to extract table data (reused from scrape_screener_playwright.py)
async def extract_table_data(table):
    """
    Extract data from a table element.

    Args:
        table: Playwright locator for the table

    Returns:
        dict: Dictionary with headers and rows
    """
    # Get all rows first
    all_rows = await table.locator('tr').all()

    if len(all_rows) < 2:
        return {'headers': [], 'rows': []}

    # Extract headers from the first row
    headers = []
    header_cells = await all_rows[0].locator('th, td').all()
    for cell in header_cells:
        header_text = await cell.text_content()
        headers.append(header_text.strip())

    # Check if headers are actually data (common in financial tables)
    first_cell = headers[0] if headers else ''
    date_pattern = any(month in ''.join(headers[1:]) for month in ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'])
    year_pattern = any(str(year) in ''.join(headers[1:]) for year in range(2010, 2025))

    is_financial_table = (first_cell == '' or any(keyword in first_cell for keyword in ['Sales', 'Revenue', 'Profit', 'Assets', 'Liabilities'])) and (date_pattern or year_pattern)

    # If this looks like a financial table with dates/years as columns
    if is_financial_table:
        # The first row contains the real headers (dates/years)
        # The first column of each subsequent row contains the row labels
        row_labels = []
        data_rows = []

        # Process each row after the header row
        for row_idx in range(1, len(all_rows)):
            row_element = all_rows[row_idx]
            cells = await row_element.locator('td, th').all()

            if not cells:
                continue

            # First cell is the row label
            label_cell = await cells[0].text_content()
            row_labels.append(label_cell.strip())

            # Rest of the cells are data
            row_data = []
            for cell_idx in range(1, len(cells)):
                if cell_idx < len(cells):
                    cell_text = await cells[cell_idx].text_content()
                    row_data.append(cell_text.strip())
                else:
                    row_data.append('')  # Padding for missing cells

            # Make sure all rows have the same length
            if len(row_data) < len(headers) - 1:  # -1 because we exclude the empty first header
                row_data.extend([''] * (len(headers) - 1 - len(row_data)))
            elif len(row_data) > len(headers) - 1:
                row_data = row_data[:len(headers) - 1]

            data_rows.append(row_data)

        # Create a proper DataFrame structure
        return {
            'headers': headers,
            'rows': data_rows,
            'row_labels': row_labels,
            'is_financial_table': True
        }

    # For regular tables, extract rows normally
    rows = []
    for row_idx in range(1, len(all_rows)):
        row_element = all_rows[row_idx]
        row_data = []
        cells = await row_element.locator('td, th').all()

        for cell in cells:
            cell_text = await cell.text_content()
            row_data.append(cell_text.strip())

        # Only add non-empty rows
        if row_data:
            # Make sure all rows have the same length as headers
            if len(row_data) < len(headers):
                row_data.extend([''] * (len(headers) - len(row_data)))
            elif len(row_data) > len(headers):
                row_data = row_data[:len(headers)]

            rows.append(row_data)

    return {
        'headers': headers,
        'rows': rows,
        'is_financial_table': False
    }

# Function to save DataFrame to CSV
def save_dataframe(df, output_dir, filename, source_name):
    """
    Save DataFrame to CSV file.

    Args:
        df: DataFrame to save
        output_dir: Directory to save the file
        filename: Name of the file
        source_name: Name of the data source
    """
    # Create a clean filename
    clean_filename = source_name + "_" + filename.replace(' ', '_').replace('/', '_').replace('\\', '_') + ".csv"
    filepath = os.path.join(output_dir, clean_filename)

    try:
        df.to_csv(filepath, index=False)
        print(f"{Fore.GREEN}Saved {source_name} table '{filename}' to {filepath}{Style.RESET_ALL}")
        return filepath
    except PermissionError:
        print(f"{Fore.RED}Permission denied when trying to write to {filepath}. Skipping this file.{Style.RESET_ALL}")
        return None

# Function to create a unified dataset from all sources
def create_unified_dataset(all_data, output_dir, company_symbol):
    """
    Create a unified dataset with key metrics from all sources.

    Args:
        all_data: Dictionary containing data from all sources
        output_dir: Directory to save the unified dataset
        company_symbol: Stock symbol of the company
    """
    # Create a dictionary to store unified data
    unified_data = {
        'company_symbol': company_symbol,
        'scrape_date': datetime.now().strftime('%Y-%m-%d'),
        'fundamental_metrics': {},
        'technical_indicators': {},
        'performance_metrics': {},
        'news_sentiment': {},
        'valuation_ratios': {},
        'profitability_ratios': {},
        'growth_metrics': {},
        'balance_sheet_metrics': {},
    }

    # Extract and combine key metrics from all sources
    try:
        # Extract fundamental metrics from screener.in
        if 'screener_in' in all_data:
            screener_data = all_data['screener_in']

            # Look for quarterly results table
            for table_name, df_data in screener_data.items():
                if 'Quarterly Results' in table_name:
                    # Convert to DataFrame if it's a list of dictionaries
                    if isinstance(df_data, list):
                        df = pd.DataFrame(df_data)
                    elif isinstance(df_data, pd.DataFrame):
                        df = df_data
                    else:
                        continue

                    # Get the most recent quarter data
                    if 'Metric' in df.columns and len(df) > 0:
                        for i, row in df.iterrows():
                            metric = row['Metric']
                            # Get the most recent value (usually the second column after 'Metric')
                            if len(df.columns) > 1:
                                # Find the most recent quarter column (excluding 'Metric')
                                data_cols = [col for col in df.columns if col != 'Metric']
                                if data_cols:
                                    recent_col = data_cols[0]  # Most recent quarter is first
                                    value = row[recent_col]
                                    unified_data['fundamental_metrics'][f"quarterly_{metric}"] = value

                # Look for profit & loss table
                elif 'Profit & Loss' in table_name:
                    # Convert to DataFrame if it's a list of dictionaries
                    if isinstance(df_data, list):
                        df = pd.DataFrame(df_data)
                    elif isinstance(df_data, pd.DataFrame):
                        df = df_data
                    else:
                        continue

                    if 'Metric' in df.columns and len(df) > 0:
                        for i, row in df.iterrows():
                            metric = row['Metric']
                            # Get the most recent value (usually the second column after 'Metric')
                            if len(df.columns) > 1:
                                # Find the most recent year column (excluding 'Metric' and 'TTM')
                                data_cols = [col for col in df.columns if col != 'Metric' and col != 'TTM']
                                if data_cols:
                                    recent_col = data_cols[0]  # Most recent year is first
                                    value = row[recent_col]
                                    unified_data['fundamental_metrics'][f"annual_{metric}"] = value
                                # Also add TTM data if available
                                if 'TTM' in df.columns:
                                    ttm_value = row['TTM']
                                    unified_data['fundamental_metrics'][f"ttm_{metric}"] = ttm_value

                # Look for ratios table
                elif 'Ratios' in table_name:
                    # Convert to DataFrame if it's a list of dictionaries
                    if isinstance(df_data, list):
                        df = pd.DataFrame(df_data)
                    elif isinstance(df_data, pd.DataFrame):
                        df = df_data
                    else:
                        continue

                    if 'Metric' in df.columns and len(df) > 0:
                        for i, row in df.iterrows():
                            metric = row['Metric']
                            # Get the most recent value
                            if len(df.columns) > 1:
                                # Find the most recent year column (excluding 'Metric' and 'TTM')
                                data_cols = [col for col in df.columns if col != 'Metric' and col != 'TTM']
                                if data_cols:
                                    recent_col = data_cols[0]  # Most recent year is first
                                    value = row[recent_col]
                                    unified_data['valuation_ratios'][f"screener_{metric}"] = value

        # Extract technical indicators from dhan.co
        if 'dhan' in all_data:
            dhan_tech_data = all_data['dhan']

            # Extract tables
            for table_name, df_data in dhan_tech_data.items():
                # Convert to DataFrame if it's a list of dictionaries
                if isinstance(df_data, list):
                    df = pd.DataFrame(df_data)
                elif isinstance(df_data, pd.DataFrame):
                    df = df_data
                else:
                    continue

                # Check if this looks like a technical indicator table
                if 'Period' in df.columns or 'Simple' in df.columns or 'Signal' in df.columns:
                    for i, row in df.iterrows():
                        if len(df.columns) >= 2:  # Ensure there are at least two columns
                            indicator = row[df.columns[0]]
                            value = row[df.columns[1]]
                            unified_data['technical_indicators'][f"dhan_{indicator}"] = value
                            # Add signal if available
                            if len(df.columns) >= 3:
                                signal = row[df.columns[2]]
                                unified_data['technical_indicators'][f"dhan_{indicator}_signal"] = signal

        # Extract news sentiment from dhan.co
        if 'dhan_news' in all_data and isinstance(all_data['dhan_news'], list):
            news_data = all_data['dhan_news']
            if news_data:
                # Get the most recent news articles (up to 5)
                recent_news = news_data[:min(5, len(news_data))]
                unified_data['news_sentiment']['recent_articles'] = recent_news

                # Count news by source
                news_by_source = {}
                for article in news_data:
                    source = article.get('source', 'Unknown')
                    if source in news_by_source:
                        news_by_source[source] += 1
                    else:
                        news_by_source[source] = 1
                unified_data['news_sentiment']['sources_count'] = news_by_source

        # Extract performance metrics from trendlyne.com
        if 'trendlyne' in all_data:
            trendlyne_data = all_data['trendlyne']

            # Extract key metrics
            if 'key_metrics' in trendlyne_data:
                key_metrics = trendlyne_data['key_metrics']
                if isinstance(key_metrics, dict):
                    for metric, value in key_metrics.items():
                        unified_data['performance_metrics'][metric] = value

            # Extract performance tables
            for table_name, df_data in trendlyne_data.items():
                # Convert to DataFrame if it's a list of dictionaries
                if isinstance(df_data, list):
                    df = pd.DataFrame(df_data)
                elif isinstance(df_data, pd.DataFrame):
                    df = df_data
                else:
                    continue

                # Check if this is a performance table
                if 'Historical Returns' in table_name or 'Performance' in table_name:
                    # For tables with time periods and returns
                    if 'Time' in df.columns or 'Period' in df.columns:
                        time_col = 'Time' if 'Time' in df.columns else 'Period'
                        for i, row in df.iterrows():
                            period = row[time_col]
                            # Extract each return metric
                            for col in df.columns:
                                if col != time_col:
                                    value = row[col]
                                    metric_name = f"{col}_{period}".replace(' ', '_')
                                    unified_data['performance_metrics'][metric_name] = value

        # Extract valuation ratios from investing.com
        if 'investing' in all_data:
            investing_data = all_data['investing']

            for table_name, df_data in investing_data.items():
                # Convert to DataFrame if it's a list of dictionaries
                if isinstance(df_data, list):
                    df = pd.DataFrame(df_data)
                elif isinstance(df_data, pd.DataFrame):
                    df = df_data
                else:
                    continue

                # Categorize tables based on their names
                if any(keyword in table_name.lower() for keyword in ['valuation', 'price']):
                    category = 'valuation_ratios'
                elif any(keyword in table_name.lower() for keyword in ['profit', 'margin', 'income']):
                    category = 'profitability_ratios'
                elif any(keyword in table_name.lower() for keyword in ['growth']):
                    category = 'growth_metrics'
                elif any(keyword in table_name.lower() for keyword in ['balance', 'debt', 'asset']):
                    category = 'balance_sheet_metrics'
                else:
                    category = 'fundamental_metrics'

                # Extract data from the table
                if len(df.columns) >= 2:  # Ensure there are at least two columns (metric and value)
                    for i, row in df.iterrows():
                        metric = row[df.columns[0]]
                        value = row[df.columns[1]]
                        unified_data[category][f"investing_{metric}"] = value

        # Extract detailed ratios from stockanalysis.com
        if 'stockanalysis' in all_data:
            stockanalysis_data = all_data['stockanalysis']

            for table_name, df_data in stockanalysis_data.items():
                # Convert to DataFrame if it's a list of dictionaries
                if isinstance(df_data, list):
                    df = pd.DataFrame(df_data)
                elif isinstance(df_data, pd.DataFrame):
                    df = df_data
                else:
                    continue

                # Categorize tables based on their names
                if any(keyword in table_name.lower() for keyword in ['valuation', 'price']):
                    category = 'valuation_ratios'
                elif any(keyword in table_name.lower() for keyword in ['profit', 'margin', 'income']):
                    category = 'profitability_ratios'
                elif any(keyword in table_name.lower() for keyword in ['growth']):
                    category = 'growth_metrics'
                elif any(keyword in table_name.lower() for keyword in ['balance', 'debt', 'asset']):
                    category = 'balance_sheet_metrics'
                else:
                    category = 'fundamental_metrics'

                # Extract data from the table
                if len(df.columns) >= 2:  # Ensure there are at least two columns
                    # For stockanalysis.com, the first column is usually the metric name
                    # and subsequent columns are years
                    for i, row in df.iterrows():
                        metric = row[df.columns[0]]
                        # Get the most recent value (usually the second column)
                        if len(df.columns) > 1:
                            value = row[df.columns[1]]
                            unified_data[category][f"stockanalysis_{metric}"] = value

        # Extract ratios from goodreturns.in
        if 'goodreturns' in all_data:
            goodreturns_data = all_data['goodreturns']

            for table_name, df_data in goodreturns_data.items():
                # Convert to DataFrame if it's a list of dictionaries
                if isinstance(df_data, list):
                    df = pd.DataFrame(df_data)
                elif isinstance(df_data, pd.DataFrame):
                    df = df_data
                else:
                    continue

                # Categorize tables based on their names
                if any(keyword in table_name.lower() for keyword in ['valuation', 'price']):
                    category = 'valuation_ratios'
                elif any(keyword in table_name.lower() for keyword in ['profit', 'margin', 'income']):
                    category = 'profitability_ratios'
                elif any(keyword in table_name.lower() for keyword in ['growth']):
                    category = 'growth_metrics'
                elif any(keyword in table_name.lower() for keyword in ['balance', 'debt', 'asset']):
                    category = 'balance_sheet_metrics'
                else:
                    category = 'fundamental_metrics'

                # Extract data from the table
                if len(df.columns) >= 2:  # Ensure there are at least two columns
                    for i, row in df.iterrows():
                        metric = row[df.columns[0]]
                        # Get the most recent value
                        if len(df.columns) > 1:
                            value = row[df.columns[1]]
                            unified_data[category][f"goodreturns_{metric}"] = value

    except Exception as e:
        print(f"{Fore.RED}Error creating unified dataset: {e}{Style.RESET_ALL}")

    # Save unified data
    unified_data_path = os.path.join(output_dir, "unified_dataset.json")
    with open(unified_data_path, 'w', encoding='utf-8') as f:
        json.dump(unified_data, f, indent=2)

    print(f"{Fore.GREEN}Unified dataset created and saved to {unified_data_path}{Style.RESET_ALL}")

    # Also save as CSV for easy viewing
    try:
        # Convert the nested dictionary to a flat DataFrame
        rows = []
        for category, metrics in unified_data.items():
            if isinstance(metrics, dict):
                for metric, value in metrics.items():
                    if not isinstance(value, (list, dict)):  # Skip complex nested structures
                        rows.append({
                            'Category': category,
                            'Metric': metric,
                            'Value': value
                        })

        if rows:
            unified_df = pd.DataFrame(rows)
            unified_csv_path = os.path.join(output_dir, "unified_dataset.csv")
            unified_df.to_csv(unified_csv_path, index=False)
            print(f"{Fore.GREEN}Unified dataset also saved as CSV to {unified_csv_path}{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}Error saving unified dataset as CSV: {e}{Style.RESET_ALL}")

    return unified_data

# Main function
async def main():
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Scrape financial data from multiple sources')
    parser.add_argument('--symbol', type=str, default='RELIANCE',
                        help='Stock symbol of the company (default: RELIANCE)')
    parser.add_argument('--output-dir', type=str, default=None,
                        help='Directory to save data files')

    args = parser.parse_args()

    # Scrape data from all sources
    await scrape_all_sources(args.symbol, args.output_dir)

if __name__ == "__main__":
    asyncio.run(main())
