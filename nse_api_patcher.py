#!/usr/bin/env python3
"""
NSE API Patcher
This module patches the AI hedge fund API to use NSE data when imported.
Import this before importing any hedge fund modules.
"""

import sys
import os
from colorama import Fore, Style

def patch_nse_api():
    """Patch the API functions to use NSE data."""
    try:
        # Add paths if not already added
        hedge_fund_path = os.path.join(os.path.dirname(__file__), 'ai-hedge-fund')
        hedge_fund_src_path = os.path.join(hedge_fund_path, 'src')
        
        if hedge_fund_path not in sys.path:
            sys.path.append(hedge_fund_path)
        if hedge_fund_src_path not in sys.path:
            sys.path.append(hedge_fund_src_path)
        
        # Import our NSE mock API functions
        from nse_mock_api import (
            get_prices,
            get_financial_metrics,
            search_line_items,
            get_insider_trades,
            get_company_news,
            get_market_cap,
            prices_to_df,
            get_price_data
        )
        
        # Import and patch the API module
        import tools.api as api
        
        # Patch all functions
        api.get_prices = get_prices
        api.get_financial_metrics = get_financial_metrics
        api.search_line_items = search_line_items
        api.get_insider_trades = get_insider_trades
        api.get_company_news = get_company_news
        api.get_market_cap = get_market_cap
        api.prices_to_df = prices_to_df
        api.get_price_data = get_price_data
        
        print(f"{Fore.GREEN}✅ NSE API patching successful{Style.RESET_ALL}")
        return True
        
    except Exception as e:
        print(f"{Fore.RED}❌ NSE API patching failed: {e}{Style.RESET_ALL}")
        return False

# Auto-patch when this module is imported
if __name__ != "__main__":
    patch_nse_api()
