#!/usr/bin/env python3
"""
NSE-Enhanced AI Hedge Fund Main
This is a modified version of the original main.py that automatically detects and handles NSE tickers.
Provides the exact same CLI experience as the original hedge fund.
"""

import sys
import os
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Add the AI hedge fund path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Patch API functions BEFORE any imports that might use them
def patch_api_early():
    """Patch API functions before any agent imports."""
    try:
        # Import our NSE mock API functions
        from nse_mock_api import (
            get_prices,
            get_financial_metrics,
            search_line_items,
            get_insider_trades,
            get_company_news,
            get_market_cap,
            prices_to_df,
            get_price_data
        )

        # Import the API module and patch it at the module level
        import tools.api as api

        # Store original functions for potential restoration
        api._original_get_prices = getattr(api, 'get_prices', None)
        api._original_get_financial_metrics = getattr(api, 'get_financial_metrics', None)
        api._original_search_line_items = getattr(api, 'search_line_items', None)
        api._original_get_insider_trades = getattr(api, 'get_insider_trades', None)
        api._original_get_company_news = getattr(api, 'get_company_news', None)
        api._original_get_market_cap = getattr(api, 'get_market_cap', None)
        api._original_prices_to_df = getattr(api, 'prices_to_df', None)
        api._original_get_price_data = getattr(api, 'get_price_data', None)

        # Patch all functions
        api.get_prices = get_prices
        api.get_financial_metrics = get_financial_metrics
        api.search_line_items = search_line_items
        api.get_insider_trades = get_insider_trades
        api.get_company_news = get_company_news
        api.get_market_cap = get_market_cap
        api.prices_to_df = prices_to_df
        api.get_price_data = get_price_data

        # Also patch the module's __dict__ to ensure imports work
        import sys
        if 'tools.api' in sys.modules:
            module = sys.modules['tools.api']
            module.get_prices = get_prices
            module.get_financial_metrics = get_financial_metrics
            module.search_line_items = search_line_items
            module.get_insider_trades = get_insider_trades
            module.get_company_news = get_company_news
            module.get_market_cap = get_market_cap
            module.prices_to_df = prices_to_df
            module.get_price_data = get_price_data

        return True

    except Exception as e:
        print(f"{Fore.RED}❌ Failed to patch API early: {e}{Style.RESET_ALL}")
        return False

def detect_and_patch_nse_api(tickers):
    """Detect NSE tickers and patch API if needed."""
    nse_tickers = []

    for ticker in tickers:
        if ticker.endswith('.NS') or ticker in ['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK', 'HINDUNILVR', 'ITC', 'SBIN', 'BHARTIARTL', 'KOTAKBANK']:
            # Convert to .NS format if not already
            if not ticker.endswith('.NS'):
                ticker = f"{ticker}.NS"
            nse_tickers.append(ticker)

    if nse_tickers:
        print(f"{Fore.YELLOW}🇮🇳 NSE tickers detected: {', '.join(nse_tickers)}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}🔧 Activating NSE data integration...{Style.RESET_ALL}")

        # Validate NSE data availability
        available_data = {
            "RELIANCE.NS": "RELIANCE_NS_complete_data",
            # Add more as we extract them
        }

        missing_data = []
        for ticker in nse_tickers:
            if ticker not in available_data:
                missing_data.append(ticker)
            else:
                data_dir = available_data[ticker]
                if not os.path.exists(data_dir):
                    missing_data.append(ticker)

        if missing_data:
            print(f"\n{Fore.RED}❌ Missing NSE data for: {', '.join(missing_data)}{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}Available NSE stocks: {', '.join(available_data.keys())}{Style.RESET_ALL}")
            print(f"\n{Fore.CYAN}To add more NSE stocks:{Style.RESET_ALL}")
            print(f"1. Run: python screener_mcp_final.py  # Enter stock symbol")
            print(f"2. Run: python complete_nse_data_extractor.py  # Modify symbol")
            print(f"3. Update nse_mock_api.py NSE_DATA_DIRS")
            sys.exit(1)

        # Patch the API early
        if not patch_api_early():
            sys.exit(1)

        print(f"{Fore.GREEN}✅ NSE data integration activated{Style.RESET_ALL}")

    return nse_tickers

# Now import everything from the original main.py
from dotenv import load_dotenv
from langchain_core.messages import HumanMessage
from langgraph.graph import END, StateGraph
import questionary
from agents.ben_graham import ben_graham_agent
from agents.bill_ackman import bill_ackman_agent
from agents.fundamentals import fundamentals_agent
from agents.portfolio_manager import portfolio_management_agent
from agents.mock_portfolio_manager import mock_portfolio_management_agent
from agents.technicals import technical_analyst_agent
from agents.risk_manager import risk_management_agent
from agents.mock_risk_manager import mock_risk_management_agent
from agents.sentiment import sentiment_agent
from agents.warren_buffett import warren_buffett_agent
from graph.state import AgentState
from agents.valuation import valuation_agent
from utils.display import print_trading_output
from utils.analysts import ANALYST_ORDER, get_analyst_nodes
from utils.progress import progress
from llm.models import LLM_ORDER, get_model_info

import argparse
from datetime import datetime
from dateutil.relativedelta import relativedelta
from tabulate import tabulate
from utils.visualize import save_graph_as_png
import json

# Load environment variables from .env file
load_dotenv()

def parse_hedge_fund_response(response):
    """Parses a JSON string and returns a dictionary."""
    try:
        return json.loads(response)
    except json.JSONDecodeError as e:
        print(f"JSON decoding error: {e}\nResponse: {repr(response)}")
        return None
    except TypeError as e:
        print(f"Invalid response type (expected string, got {type(response).__name__}): {e}")
        return None
    except Exception as e:
        print(f"Unexpected error while parsing response: {e}\nResponse: {repr(response)}")
        return None

def run_hedge_fund(
    tickers: list[str],
    start_date: str,
    end_date: str,
    portfolio: dict,
    show_reasoning: bool = False,
    selected_analysts: list[str] = [],
    model_name: str = "gpt-4o",
    model_provider: str = "OpenAI",
):
    # Start progress tracking
    progress.start()

    try:
        # Create a new workflow if analysts are customized
        if selected_analysts:
            workflow = create_workflow(selected_analysts)
            agent = workflow.compile()
        else:
            agent = app

        final_state = agent.invoke(
            {
                "messages": [
                    HumanMessage(
                        content="Make trading decisions based on the provided data.",
                    )
                ],
                "data": {
                    "tickers": tickers,
                    "portfolio": portfolio,
                    "start_date": start_date,
                    "end_date": end_date,
                    "analyst_signals": {},
                },
                "metadata": {
                    "show_reasoning": show_reasoning,
                    "model_name": model_name,
                    "model_provider": model_provider,
                },
            },
        )

        return {
            "decisions": parse_hedge_fund_response(final_state["messages"][-1].content),
            "analyst_signals": final_state["data"]["analyst_signals"],
        }
    finally:
        # Stop progress tracking
        progress.stop()

def start(state: AgentState):
    """Initialize the workflow with the input message."""
    return state

def create_workflow(selected_analysts=None):
    """Create the workflow with selected analysts."""
    workflow = StateGraph(AgentState)
    workflow.add_node("start_node", start)

    # Get analyst nodes from the configuration
    analyst_nodes = get_analyst_nodes()

    # Default to all analysts if none selected
    if selected_analysts is None:
        selected_analysts = list(analyst_nodes.keys())

    # Check if any mock analysts are selected
    use_mock_risk_manager = any("mock" in analyst_key.lower() for analyst_key in selected_analysts)

    # Add selected analyst nodes
    for analyst_key in selected_analysts:
        node_name, node_func = analyst_nodes[analyst_key]
        workflow.add_node(node_name, node_func)
        workflow.add_edge("start_node", node_name)

    # Add risk management agent (mock or real)
    if use_mock_risk_manager:
        print(f"{Fore.YELLOW}Using mock risk management agent because mock analysts are selected{Style.RESET_ALL}")
        workflow.add_node("risk_management_agent", mock_risk_management_agent)
    else:
        workflow.add_node("risk_management_agent", risk_management_agent)

    # Add portfolio management agent (mock or real)
    if use_mock_risk_manager:
        print(f"{Fore.YELLOW}Using mock portfolio management agent because mock analysts are selected{Style.RESET_ALL}")
        workflow.add_node("portfolio_management_agent", mock_portfolio_management_agent)
    else:
        workflow.add_node("portfolio_management_agent", portfolio_management_agent)

    # Connect selected analysts to risk management
    for analyst_key in selected_analysts:
        node_name = analyst_nodes[analyst_key][0]
        workflow.add_edge(node_name, "risk_management_agent")

    workflow.add_edge("risk_management_agent", "portfolio_management_agent")
    workflow.add_edge("portfolio_management_agent", END)

    workflow.set_entry_point("start_node")
    return workflow

if __name__ == "__main__":
    print(f"{Fore.CYAN}🚀 NSE-Enhanced AI Hedge Fund{Style.RESET_ALL}")
    print(f"{Fore.CYAN}=" * 50)
    print(f"{Fore.YELLOW}Supports both US stocks (via API) and NSE stocks (via extracted data){Style.RESET_ALL}")
    print(f"{Fore.YELLOW}NSE tickers: Use .NS suffix (e.g., RELIANCE.NS, TCS.NS){Style.RESET_ALL}\n")

    parser = argparse.ArgumentParser(description="Run the NSE-enhanced hedge fund trading system")
    parser.add_argument(
        "--initial-cash",
        type=float,
        default=100000.0,
        help="Initial cash position. Defaults to 100000.0)"
    )
    parser.add_argument(
        "--margin-requirement",
        type=float,
        default=0.0,
        help="Initial margin requirement. Defaults to 0.0"
    )
    parser.add_argument("--tickers", type=str, required=True, help="Comma-separated list of stock ticker symbols")
    parser.add_argument(
        "--start-date",
        type=str,
        help="Start date (YYYY-MM-DD). Defaults to 3 months before end date",
    )
    parser.add_argument("--end-date", type=str, help="End date (YYYY-MM-DD). Defaults to today")
    parser.add_argument("--show-reasoning", action="store_true", help="Show reasoning from each agent")
    parser.add_argument(
        "--show-agent-graph", action="store_true", help="Show the agent graph"
    )

    args = parser.parse_args()

    # Parse tickers from comma-separated string
    tickers = [ticker.strip() for ticker in args.tickers.split(",")]

    # Detect NSE tickers and patch API if needed
    nse_tickers = detect_and_patch_nse_api(tickers)

    if nse_tickers:
        print(f"{Fore.GREEN}✅ Ready to analyze NSE stocks with extracted data{Style.RESET_ALL}")

    us_tickers = [t for t in tickers if t not in nse_tickers]
    if us_tickers:
        print(f"{Fore.BLUE}📡 US stocks will use normal API: {', '.join(us_tickers)}{Style.RESET_ALL}")

    print(f"\n{Fore.CYAN}🎯 Starting AI hedge fund workflow...{Style.RESET_ALL}")
    print(f"{Fore.CYAN}=" * 50)

    # Select analysts (same as original)
    selected_analysts = None
    choices = questionary.checkbox(
        "Select your AI analysts.",
        choices=[questionary.Choice(display, value=value) for display, value in ANALYST_ORDER],
        instruction="\n\nInstructions: \n1. Press Space to select/unselect analysts.\n2. Press 'a' to select/unselect all.\n3. Press Enter when done to run the hedge fund.\n",
        validate=lambda x: len(x) > 0 or "You must select at least one analyst.",
        style=questionary.Style(
            [
                ("checkbox-selected", "fg:green"),
                ("selected", "fg:green noinherit"),
                ("highlighted", "noinherit"),
                ("pointer", "noinherit"),
            ]
        ),
    ).ask()

    if not choices:
        print("\n\nInterrupt received. Exiting...")
        sys.exit(0)
    else:
        selected_analysts = choices
        print(f"\nSelected analysts: {', '.join(Fore.GREEN + choice.title().replace('_', ' ') + Style.RESET_ALL for choice in choices)}\n")

    # Select LLM model (same as original)
    model_choice = questionary.select(
        "Select your LLM model:",
        choices=[questionary.Choice(display, value=value) for display, value, _ in LLM_ORDER],
        style=questionary.Style([
            ("selected", "fg:green bold"),
            ("pointer", "fg:green bold"),
            ("highlighted", "fg:green"),
            ("answer", "fg:green bold"),
        ])
    ).ask()

    if not model_choice:
        print("\n\nInterrupt received. Exiting...")
        sys.exit(0)
    else:
        # Get model info using the helper function
        model_info = get_model_info(model_choice)
        if model_info:
            model_provider = model_info.provider.value
            print(f"\nSelected {Fore.CYAN}{model_provider}{Style.RESET_ALL} model: {Fore.GREEN + Style.BRIGHT}{model_choice}{Style.RESET_ALL}\n")
        else:
            model_provider = "Unknown"
            print(f"\nSelected model: {Fore.GREEN + Style.BRIGHT}{model_choice}{Style.RESET_ALL}\n")

    # Create the workflow with selected analysts
    workflow = create_workflow(selected_analysts)
    app = workflow.compile()

    if args.show_agent_graph:
        file_path = ""
        if selected_analysts is not None:
            for selected_analyst in selected_analysts:
                file_path += selected_analyst + "_"
            file_path += "graph.png"
        save_graph_as_png(app, file_path)

    # Validate dates if provided (same as original)
    if args.start_date:
        try:
            datetime.strptime(args.start_date, "%Y-%m-%d")
        except ValueError:
            raise ValueError("Start date must be in YYYY-MM-DD format")

    if args.end_date:
        try:
            datetime.strptime(args.end_date, "%Y-%m-%d")
        except ValueError:
            raise ValueError("End date must be in YYYY-MM-DD format")

    # Set the start and end dates (same as original)
    end_date = args.end_date or datetime.now().strftime("%Y-%m-%d")
    if not args.start_date:
        # Calculate 3 months before end_date
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
        start_date = (end_date_obj - relativedelta(months=3)).strftime("%Y-%m-%d")
    else:
        start_date = args.start_date

    # Initialize portfolio with cash amount and stock positions (same as original)
    portfolio = {
        "cash": args.initial_cash,  # Initial cash amount
        "margin_requirement": args.margin_requirement,  # Initial margin requirement
        "margin_used": 0.0,  # total margin usage across all short positions
        "positions": {
            ticker: {
                "long": 0,  # Number of shares held long
                "short": 0,  # Number of shares held short
                "long_cost_basis": 0.0,  # Average cost basis for long positions
                "short_cost_basis": 0.0,  # Average price at which shares were sold short
                "short_margin_used": 0.0,  # Dollars of margin used for this ticker's short
            } for ticker in tickers
        },
        "realized_gains": {
            ticker: {
                "long": 0.0,  # Realized gains from long positions
                "short": 0.0,  # Realized gains from short positions
            } for ticker in tickers
        }
    }

    # Run the hedge fund (same as original)
    result = run_hedge_fund(
        tickers=tickers,
        start_date=start_date,
        end_date=end_date,
        portfolio=portfolio,
        show_reasoning=args.show_reasoning,
        selected_analysts=selected_analysts,
        model_name=model_choice,
        model_provider=model_provider,
    )
    print_trading_output(result)
