"""
NSE Mock API module that loads data from our extracted NSE CSV files.
This replaces the original mock API to use RELIANCE and TCS data instead of AMD.
"""

import os
import pandas as pd
import random
from typing import List, Dict, Optional, Any, Union
from datetime import datetime, timedelta

# Add the AI hedge fund path
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

from data.models import (
    CompanyNews,
    CompanyNewsResponse,
    FinancialMetrics,
    FinancialMetricsResponse,
    Price,
    PriceResponse,
    LineItem,
    LineItemResponse,
    InsiderTrade,
    InsiderTradeResponse,
)

# Path to our NSE data directories
NSE_DATA_DIRS = {
    "RELIANCE.NS": "RELIANCE_NS_complete_data",
    "TCS.NS": "TCS_NS_complete_data",  # Will be created when we extract TCS
}

def get_nse_data_dir(ticker: str) -> str:
    """Get the data directory for a ticker."""
    # Map ticker to data directory
    if ticker in NSE_DATA_DIRS:
        return NSE_DATA_DIRS[ticker]
    elif ticker == "RELIANCE":
        return NSE_DATA_DIRS["RELIANCE.NS"]
    elif ticker == "TCS":
        return NSE_DATA_DIRS.get("TCS.NS", NSE_DATA_DIRS["RELIANCE.NS"])  # Fallback to RELIANCE
    else:
        # Default to RELIANCE for any unknown ticker
        return NSE_DATA_DIRS["RELIANCE.NS"]

def get_prices(ticker: str, start_date: str, end_date: str) -> List[Price]:
    """Load NSE price data from our extracted CSV files."""
    print(f"Loading NSE price data for {ticker}...")

    try:
        data_dir = get_nse_data_dir(ticker)

        # Determine the symbol for file naming
        symbol = ticker.replace(".NS", "") if ".NS" in ticker else ticker

        csv_path = os.path.join(data_dir, f"prices_{symbol}.csv")

        if not os.path.exists(csv_path):
            print(f"Warning: Price data not found for {ticker} at {csv_path}")
            # Generate mock data as fallback
            return generate_mock_prices(ticker, start_date, end_date)

        df = pd.read_csv(csv_path)

        # Filter by date range
        df['time'] = pd.to_datetime(df['time'], utc=True)
        start_dt = pd.to_datetime(start_date, utc=True)
        end_dt = pd.to_datetime(end_date, utc=True)
        df = df[(df['time'] >= start_dt) & (df['time'] <= end_dt)]

        # Convert to Price objects
        prices = []
        for _, row in df.iterrows():
            prices.append(Price(
                ticker=ticker,
                open=float(row['open']),
                close=float(row['close']),
                high=float(row['high']),
                low=float(row['low']),
                volume=int(row['volume']),
                time=row['time'].strftime('%Y-%m-%dT%H:%M:%SZ'),
                time_milliseconds=int(row['time_milliseconds'])
            ))

        print(f"Loaded {len(prices)} price records for {ticker}")
        return prices

    except Exception as e:
        print(f"Error loading NSE price data for {ticker}: {e}")
        return generate_mock_prices(ticker, start_date, end_date)

def generate_mock_prices(ticker: str, start_date: str, end_date: str) -> List[Price]:
    """Generate mock price data as fallback."""
    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")

    # Base prices for different stocks
    base_prices = {
        "RELIANCE": 2800.0,
        "RELIANCE.NS": 2800.0,
        "TCS": 4200.0,
        "TCS.NS": 4200.0,
    }

    base_price = base_prices.get(ticker, 2800.0)

    dates = []
    current = start_dt
    while current <= end_dt:
        if current.weekday() < 5:  # Monday to Friday
            dates.append(current)
        current += timedelta(days=1)

    prices = []
    for i, date in enumerate(dates):
        daily_change = random.uniform(-0.02, 0.025)
        if i > 0:
            base_price = base_price * (1 + daily_change)

        high = base_price * random.uniform(1.0, 1.02)
        low = base_price * random.uniform(0.98, 1.0)
        open_price = base_price * random.uniform(0.99, 1.01)

        iso_date = date.strftime("%Y-%m-%dT%H:%M:%SZ")
        ms_timestamp = int(date.timestamp() * 1000)

        prices.append(Price(
            ticker=ticker,
            open=round(open_price, 2),
            close=round(base_price, 2),
            high=round(high, 2),
            low=round(low, 2),
            volume=int(random.uniform(1000000, 5000000)),
            time=iso_date,
            time_milliseconds=ms_timestamp
        ))

    return prices

def get_financial_metrics(
    ticker: str,
    end_date: str,
    period: str = "ttm",
    limit: int = 10,
) -> List[FinancialMetrics]:
    """Load NSE financial metrics from our extracted CSV files."""
    print(f"Loading NSE financial metrics for {ticker}...")

    try:
        data_dir = get_nse_data_dir(ticker)

        # Determine the symbol for file naming
        symbol = ticker.replace(".NS", "") if ".NS" in ticker else ticker

        csv_path = os.path.join(data_dir, f"financial_metrics_{symbol}.csv")

        if not os.path.exists(csv_path):
            print(f"Warning: Financial metrics not found for {ticker} at {csv_path}")
            return []

        df = pd.read_csv(csv_path)

        # Filter by date and period
        # Convert report_period format from "Mar 2025" to datetime
        try:
            df['report_period_dt'] = pd.to_datetime(df['report_period'], format='%b %Y')
            end_date_dt = pd.to_datetime(end_date)
            df = df[df['report_period_dt'] <= end_date_dt]
        except Exception as e:
            # Fallback to original format if conversion fails
            df = df[pd.to_datetime(df['report_period']) <= pd.to_datetime(end_date)]

        if period != "all" and 'period' in df.columns:
            # Map ttm to annual for NSE data
            search_period = "annual" if period == "ttm" else period
            df = df[df['period'] == search_period]

        # Sort by report period (most recent first)
        df = df.sort_values('report_period', ascending=False)

        # Limit the number of results
        df = df.head(limit)

        # Convert to FinancialMetrics objects
        metrics = []
        for _, row in df.iterrows():
            # Convert row to dict and handle NaN values
            row_dict = row.to_dict()
            for key, value in row_dict.items():
                if pd.isna(value):
                    row_dict[key] = 0.0 if key not in ['ticker', 'report_period', 'fiscal_period', 'period', 'currency'] else str(value)

            metrics.append(FinancialMetrics(**row_dict))

        print(f"Loaded {len(metrics)} financial metric records for {ticker}")
        return metrics

    except Exception as e:
        print(f"Error loading NSE financial metrics for {ticker}: {e}")
        return []

def search_line_items(
    ticker: str,
    line_items: List[str],
    end_date: str,
    period: str = "ttm",
    limit: int = 10,
) -> List[LineItem]:
    """Load NSE line items from our extracted CSV files."""
    print(f"Loading NSE line items for {ticker}...")

    try:
        data_dir = get_nse_data_dir(ticker)

        # Determine the symbol for file naming
        symbol = ticker.replace(".NS", "") if ".NS" in ticker else ticker

        csv_path = os.path.join(data_dir, f"line_items_{symbol}.csv")

        if not os.path.exists(csv_path):
            print(f"Warning: Line items not found for {ticker} at {csv_path}")
            return []

        df = pd.read_csv(csv_path)

        # Filter by date and period
        # Convert report_period format from "Mar 2025" to datetime
        try:
            df['report_period_dt'] = pd.to_datetime(df['report_period'], format='%b %Y')
            df = df[df['report_period_dt'] <= pd.to_datetime(end_date)]
        except:
            # Fallback to original format if conversion fails
            df = df[pd.to_datetime(df['report_period']) <= pd.to_datetime(end_date)]

        if period != "all" and 'period' in df.columns:
            df = df[df['period'] == period]

        # Sort by report period (most recent first)
        df = df.sort_values('report_period', ascending=False)

        # Limit the number of results
        df = df.head(limit)

        # Convert to LineItem objects
        items = []
        for _, row in df.iterrows():
            # Convert row to dict and handle NaN values
            row_dict = row.to_dict()
            for key, value in row_dict.items():
                if pd.isna(value):
                    row_dict[key] = 0.0 if key not in ['ticker', 'report_period', 'period', 'currency'] else str(value)

            items.append(LineItem(**row_dict))

        print(f"Loaded {len(items)} line item records for {ticker}")
        return items

    except Exception as e:
        print(f"Error loading NSE line items for {ticker}: {e}")
        return []

def get_company_news(
    ticker: str,
    end_date: str,
    start_date: Optional[str] = None,
    limit: int = 1000,
) -> List[CompanyNews]:
    """Load NSE company news from our extracted CSV files."""
    print(f"Loading NSE company news for {ticker}...")

    try:
        data_dir = get_nse_data_dir(ticker)

        # Determine the symbol for file naming
        symbol = ticker.replace(".NS", "") if ".NS" in ticker else ticker

        csv_path = os.path.join(data_dir, f"company_news_{symbol}.csv")

        if not os.path.exists(csv_path):
            print(f"Warning: Company news not found for {ticker} at {csv_path}")
            return []

        df = pd.read_csv(csv_path)

        # Filter by date range
        df = df[df['date'] <= end_date]
        if start_date:
            df = df[df['date'] >= start_date]

        # Sort by date (most recent first)
        df = df.sort_values('date', ascending=False)

        # Limit the number of results
        df = df.head(limit)

        # Convert to CompanyNews objects
        news = []
        for _, row in df.iterrows():
            # Convert row to dict and handle NaN values
            row_dict = row.to_dict()
            for key, value in row_dict.items():
                if pd.isna(value):
                    row_dict[key] = "" if key in ['title', 'author', 'source', 'url', 'sentiment'] else str(value)

            news.append(CompanyNews(**row_dict))

        print(f"Loaded {len(news)} news records for {ticker}")
        return news

    except Exception as e:
        print(f"Error loading NSE company news for {ticker}: {e}")
        return []

def get_insider_trades(
    ticker: str,
    end_date: str,
    start_date: Optional[str] = None,
    limit: int = 1000,
) -> List[InsiderTrade]:
    """Load NSE insider trades (placeholder - not available for NSE stocks)."""
    print(f"Loading NSE insider trades for {ticker}...")

    # NSE insider trading data is not readily available
    # Return empty list for now
    print(f"Warning: Insider trading data not available for NSE stocks")
    return []

def get_market_cap(ticker: str, end_date: str = "2025-12-31") -> float:
    """Get market cap from financial metrics."""
    try:
        metrics = get_financial_metrics(ticker, end_date, limit=1)
        if metrics:
            return float(metrics[0].market_cap)
        return 0.0
    except:
        return 0.0

def prices_to_df(prices: List[Price]) -> pd.DataFrame:
    """Convert prices to DataFrame."""
    if not prices:
        return pd.DataFrame()

    data = []
    for price in prices:
        # Price model doesn't have ticker field, so we'll add it from the context
        price_dict = {
            'open': price.open,
            'close': price.close,
            'high': price.high,
            'low': price.low,
            'volume': price.volume,
            'time': price.time,
        }

        # Add time_milliseconds if available
        if hasattr(price, 'time_milliseconds'):
            price_dict['time_milliseconds'] = price.time_milliseconds

        data.append(price_dict)

    df = pd.DataFrame(data)

    # Convert time to datetime and set as index (matching original API behavior)
    if 'time' in df.columns:
        df['Date'] = pd.to_datetime(df['time'])
        df.set_index('Date', inplace=True)

    # Ensure numeric columns are properly typed
    numeric_cols = ['open', 'close', 'high', 'low', 'volume']
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')

    df.sort_index(inplace=True)
    return df

def get_price_data(ticker: str, start_date: str, end_date: str) -> pd.DataFrame:
    """Get price data as DataFrame."""
    prices = get_prices(ticker, start_date, end_date)
    return prices_to_df(prices)
