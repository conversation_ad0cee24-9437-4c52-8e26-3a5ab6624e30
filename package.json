{"name": "screener-ratio-analyzer-mcp", "version": "1.0.0", "description": "MCP Server for extracting financial ratios from screener.in using existing Microsoft Edge browser", "main": "screener_ratio_analyzer_mcp.py", "scripts": {"install-deps": "pip install -r requirements_ratio_analyzer.txt", "install-playwright": "playwright install chromium", "setup": "npm run install-deps && npm run install-playwright", "setup-edge": "powershell -ExecutionPolicy Bypass -File setup_edge_debug.ps1", "test": "python test_screener_mcp_new.py", "start": "python screener_ratio_analyzer_mcp.py", "quick-run": "python quick_run.py", "dev": "npm run quick-run"}, "keywords": ["mcp", "screener", "financial-ratios", "stock-analysis", "playwright", "automation"], "author": "Your Name", "license": "MIT", "dependencies": {}, "devDependencies": {}}