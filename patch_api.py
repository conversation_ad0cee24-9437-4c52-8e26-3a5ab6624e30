"""
Patch the API module to use mock data for AMD.
Run this script before running the main.py script.
"""

import sys
import os
import importlib.util

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Import the mock API functions
from src.tools.mock_api import (
    get_prices,
    get_financial_metrics,
    search_line_items,
    get_insider_trades,
    get_company_news,
    get_market_cap,
    prices_to_df,
    get_price_data
)

# Get the path to the API module
api_module_path = os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src', 'tools', 'api.py')

# Load the API module
spec = importlib.util.spec_from_file_location("api", api_module_path)
api = importlib.util.module_from_spec(spec)
spec.loader.exec_module(api)

# Patch the API functions
api.get_prices = get_prices
api.get_financial_metrics = get_financial_metrics
api.search_line_items = search_line_items
api.get_insider_trades = get_insider_trades
api.get_company_news = get_company_news
api.get_market_cap = get_market_cap
api.prices_to_df = prices_to_df
api.get_price_data = get_price_data

# Save the patched module
with open(api_module_path, 'r') as f:
    api_code = f.read()

# Add our patch code at the top of the file
patch_code = """
# PATCHED: Use mock data for AMD
import os
import sys
from pathlib import Path

# Get the path to the mock_api.py file
mock_api_path = Path(__file__).parent / "mock_api.py"

    # Override the functions to use mock data for AMD
    def get_prices(ticker, start_date, end_date):
        if ticker == "AMD":
            print(f"Using mock data for {ticker}")
            return mock_get_prices(ticker, start_date, end_date)
        return original_get_prices(ticker, start_date, end_date)

    def get_financial_metrics(ticker, end_date, period="ttm", limit=10):
        if ticker == "AMD":
            print(f"Using mock data for {ticker}")
            return mock_get_financial_metrics(ticker, end_date, period, limit)
        return original_get_financial_metrics(ticker, end_date, period, limit)

    def search_line_items(ticker, line_items, end_date, period="ttm", limit=10):
        if ticker == "AMD":
            print(f"Using mock data for {ticker}")
            return mock_search_line_items(ticker, line_items, end_date, period, limit)
        return original_search_line_items(ticker, line_items, end_date, period, limit)

    def get_insider_trades(ticker, end_date, start_date=None, limit=1000):
        if ticker == "AMD":
            print(f"Using mock data for {ticker}")
            return mock_get_insider_trades(ticker, end_date, start_date, limit)
        return original_get_insider_trades(ticker, end_date, start_date, limit)

    def get_company_news(ticker, end_date, start_date=None, limit=1000):
        if ticker == "AMD":
            print(f"Using mock data for {ticker}")
            return mock_get_company_news(ticker, end_date, start_date, limit)
        return original_get_company_news(ticker, end_date, start_date, limit)

    def get_market_cap(ticker, end_date):
        if ticker == "AMD":
            print(f"Using mock data for {ticker}")
            return mock_get_market_cap(ticker, end_date)
        return original_get_market_cap(ticker, end_date)

    print("API functions patched to use mock data for AMD")
"""

# Check if the patch has already been applied
if "# PATCHED: Use mock data for AMD" not in api_code:
    # Add the patch code at the top of the file
    patched_api_code = patch_code + api_code

    # Save the patched module
    with open(api_module_path, 'w') as f:
        f.write(patched_api_code)

    print(f"API module patched successfully: {api_module_path}")
else:
    print("API module already patched")
