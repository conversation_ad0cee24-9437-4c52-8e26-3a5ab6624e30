"""
Patch the API module to use mock data for AMD.
Run this script before running the main.py script.
"""

import os
import sys
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Get the path to the API module
api_module_path = os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src', 'tools', 'api.py')
mock_api_module_path = os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src', 'tools', 'mock_api.py')

# Read the original API module
with open(api_module_path, 'r') as f:
    api_code = f.read()

# Check if the patch has already been applied
if "# PATCHED FOR AMD" in api_code:
    print(f"{Fore.YELLOW}API module already patched{Style.RESET_ALL}")
    sys.exit(0)

# Create a backup of the original API module
backup_path = api_module_path + '.bak'
with open(backup_path, 'w') as f:
    f.write(api_code)
print(f"{Fore.GREEN}Created backup of API module: {backup_path}{Style.RESET_ALL}")

# Modify the get_financial_metrics function to use mock data for AMD
api_code = api_code.replace(
    "def get_financial_metrics(ticker: str, end_date: str, period: str = \"ttm\", limit: int = 10) -> List[FinancialMetrics]:",
    """def get_financial_metrics(ticker: str, end_date: str, period: str = "ttm", limit: int = 10) -> List[FinancialMetrics]:
    # PATCHED FOR AMD
    if ticker == "AMD":
        print(f"{Fore.CYAN}Using mock data for {ticker}{Style.RESET_ALL}")
        # Import the mock API function
        from src.tools.mock_api import get_financial_metrics as mock_get_financial_metrics
        return mock_get_financial_metrics(ticker, end_date, period, limit)"""
)

# Modify the search_line_items function to use mock data for AMD
api_code = api_code.replace(
    "def search_line_items(ticker: str, line_items: List[str], end_date: str, period: str = \"ttm\", limit: int = 10) -> List[LineItem]:",
    """def search_line_items(ticker: str, line_items: List[str], end_date: str, period: str = "ttm", limit: int = 10) -> List[LineItem]:
    # PATCHED FOR AMD
    if ticker == "AMD":
        print(f"{Fore.CYAN}Using mock data for {ticker}{Style.RESET_ALL}")
        # Import the mock API function
        from src.tools.mock_api import search_line_items as mock_search_line_items
        return mock_search_line_items(ticker, line_items, end_date, period, limit)"""
)

# Modify the get_insider_trades function to use mock data for AMD
api_code = api_code.replace(
    "def get_insider_trades(ticker: str, end_date: str, start_date: Optional[str] = None, limit: int = 1000) -> List[InsiderTrade]:",
    """def get_insider_trades(ticker: str, end_date: str, start_date: Optional[str] = None, limit: int = 1000) -> List[InsiderTrade]:
    # PATCHED FOR AMD
    if ticker == "AMD":
        print(f"{Fore.CYAN}Using mock data for {ticker}{Style.RESET_ALL}")
        # Import the mock API function
        from src.tools.mock_api import get_insider_trades as mock_get_insider_trades
        return mock_get_insider_trades(ticker, end_date, start_date, limit)"""
)

# Modify the get_company_news function to use mock data for AMD
api_code = api_code.replace(
    "def get_company_news(ticker: str, end_date: str, start_date: Optional[str] = None, limit: int = 1000) -> List[CompanyNews]:",
    """def get_company_news(ticker: str, end_date: str, start_date: Optional[str] = None, limit: int = 1000) -> List[CompanyNews]:
    # PATCHED FOR AMD
    if ticker == "AMD":
        print(f"{Fore.CYAN}Using mock data for {ticker}{Style.RESET_ALL}")
        # Import the mock API function
        from src.tools.mock_api import get_company_news as mock_get_company_news
        return mock_get_company_news(ticker, end_date, start_date, limit)"""
)

# Modify the get_prices function to use mock data for AMD
api_code = api_code.replace(
    "def get_prices(ticker: str, start_date: str, end_date: str) -> List[Price]:",
    """def get_prices(ticker: str, start_date: str, end_date: str) -> List[Price]:
    # PATCHED FOR AMD
    if ticker == "AMD":
        print(f"{Fore.CYAN}Using mock data for {ticker}{Style.RESET_ALL}")
        # Import the mock API function
        from src.tools.mock_api import get_prices as mock_get_prices
        return mock_get_prices(ticker, start_date, end_date)"""
)

# Modify the get_market_cap function to use mock data for AMD
api_code = api_code.replace(
    "def get_market_cap(ticker: str, end_date: str) -> Optional[float]:",
    """def get_market_cap(ticker: str, end_date: str) -> Optional[float]:
    # PATCHED FOR AMD
    if ticker == "AMD":
        print(f"{Fore.CYAN}Using mock data for {ticker}{Style.RESET_ALL}")
        # Import the mock API function
        from src.tools.mock_api import get_market_cap as mock_get_market_cap
        return mock_get_market_cap(ticker, end_date)"""
)

# Add colorama import at the top of the file
api_code = api_code.replace(
    "import os",
    """import os
from colorama import Fore, Style"""
)

# Save the patched API module
with open(api_module_path, 'w') as f:
    f.write(api_code)

print(f"{Fore.GREEN}API module patched successfully: {api_module_path}{Style.RESET_ALL}")
print(f"{Fore.GREEN}You can now run the main.py script with AMD as the ticker{Style.RESET_ALL}")
