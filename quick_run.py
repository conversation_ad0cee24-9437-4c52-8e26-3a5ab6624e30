#!/usr/bin/env python3
"""
Quick run script for Screener Ratio Analyzer MCP
This script handles the complete setup and execution
"""

import asyncio
import subprocess
import sys
import os
import time
from pathlib import Path

def run_powershell_command(command):
    """Run a PowerShell command and return the result."""
    try:
        result = subprocess.run(
            ["powershell", "-Command", command],
            capture_output=True,
            text=True,
            timeout=30
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def check_edge_debug_port():
    """Check if Edge debug port is available."""
    success, stdout, stderr = run_powershell_command(
        "Test-NetConnection -ComputerName localhost -Port 9222 -WarningAction SilentlyContinue | Select-Object TcpTestSucceeded"
    )
    return "True" in stdout if success else False

def setup_edge_debug():
    """Set up Edge for debugging."""
    print("🔧 Setting up Microsoft Edge for debugging...")
    
    # Check if Edge is already running with debug port
    if check_edge_debug_port():
        print("✅ Edge is already running with debug port enabled")
        return True
    
    print("🚀 Running Edge setup script...")
    
    # Run the PowerShell setup script
    setup_script = Path(__file__).parent / "setup_edge_debug.ps1"
    
    if setup_script.exists():
        try:
            subprocess.run(
                ["powershell", "-ExecutionPolicy", "Bypass", "-File", str(setup_script)],
                check=False  # Don't raise exception on non-zero exit
            )
            
            # Wait a bit and check if port is now available
            print("⏳ Waiting for Edge to start...")
            time.sleep(5)
            
            return check_edge_debug_port()
            
        except Exception as e:
            print(f"❌ Failed to run setup script: {e}")
            return False
    else:
        print("❌ Setup script not found")
        return False

def install_dependencies():
    """Install required Python dependencies."""
    print("📦 Installing dependencies...")
    
    try:
        # Install Python requirements
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements_ratio_analyzer.txt"], 
                      check=True, capture_output=True)
        
        # Install Playwright browsers
        subprocess.run([sys.executable, "-m", "playwright", "install", "chromium"], 
                      check=True, capture_output=True)
        
        print("✅ Dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

async def run_mcp_server():
    """Run the MCP server."""
    print("🚀 Starting MCP server...")
    
    try:
        from screener_ratio_analyzer_mcp import main
        await main()
    except KeyboardInterrupt:
        print("\n👋 MCP server stopped by user")
    except Exception as e:
        print(f"❌ MCP server error: {e}")

async def test_functionality():
    """Test the functionality before starting the server."""
    print("🧪 Testing functionality...")
    
    try:
        from test_screener_mcp_new import test_full_workflow
        success = await test_full_workflow()
        
        if success:
            print("✅ Test passed! MCP is ready to use")
            return True
        else:
            print("❌ Test failed! Please check the setup")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Main execution function."""
    print("🎯 Screener Ratio Analyzer MCP - Quick Setup & Run")
    print("=" * 55)
    
    # Check if we're in the right directory
    if not Path("screener_ratio_analyzer_mcp.py").exists():
        print("❌ Please run this script from the project directory")
        return
    
    # Step 1: Install dependencies
    if not install_dependencies():
        print("❌ Setup failed at dependency installation")
        return
    
    # Step 2: Setup Edge
    if not setup_edge_debug():
        print("❌ Setup failed at Edge configuration")
        print("💡 Please manually setup Edge with debug port 9222")
        return
    
    # Step 3: Test functionality
    print("\n🧪 Running functionality test...")
    
    async def run_test():
        return await test_functionality()
    
    test_passed = asyncio.run(run_test())
    
    if not test_passed:
        print("❌ Functionality test failed")
        print("💡 Please check the logs and try again")
        return
    
    # Step 4: Ask user what to do next
    print("\n" + "=" * 55)
    print("✅ Setup completed successfully!")
    print("🎯 What would you like to do next?")
    print("1. Start MCP server")
    print("2. Run another test")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == "1":
        print("\n🚀 Starting MCP server...")
        print("💡 Use Ctrl+C to stop the server")
        asyncio.run(run_mcp_server())
    elif choice == "2":
        print("\n🧪 Running test again...")
        asyncio.run(run_test())
    else:
        print("👋 Goodbye!")

if __name__ == "__main__":
    main()
