"""
Direct data fetcher for RELIANCE.NS using yfinance and other sources.
This script fetches comprehensive financial data for comparison with AAPL.
"""

import yfinance as yf
import pandas as pd
import json
import os
import requests
from datetime import datetime, timedelta
import warnings
import numpy as np
warnings.filterwarnings('ignore')

class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle pandas and numpy objects."""
    def default(self, obj):
        if isinstance(obj, pd.Timestamp):
            return obj.isoformat()
        elif isinstance(obj, (pd.Series, pd.DataFrame)):
            return obj.to_dict()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj):
            return None
        elif hasattr(obj, 'isoformat'):  # datetime.date, datetime.datetime
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):  # Custom objects
            return obj.__dict__
        try:
            return str(obj)
        except:
            return None

def get_comprehensive_stock_data(symbol):
    """
    Get comprehensive stock data for a given symbol.

    Args:
        symbol (str): Stock symbol (e.g., 'RELIANCE.NS')

    Returns:
        dict: Comprehensive stock data
    """
    print(f"Fetching comprehensive data for {symbol}...")

    # Create yfinance ticker object
    ticker = yf.Ticker(symbol)

    # Initialize data dictionary
    data = {
        'symbol': symbol,
        'fetch_date': datetime.now().isoformat(),
        'basic_info': {},
        'financial_data': {},
        'price_data': {},
        'news': [],
        'recommendations': {},
        'calendar': {},
        'options': {},
        'institutional_holders': {},
        'major_holders': {},
        'sustainability': {},
        'analyst_price_targets': {}
    }

    # Get basic stock info
    try:
        info = ticker.info
        data['basic_info'] = info
        print(f"✓ Basic info: {len(info)} fields")
    except Exception as e:
        print(f"✗ Error fetching basic info: {e}")
        data['basic_info'] = {}

    # Get financial statements
    try:
        # Income statement
        income_stmt = ticker.financials
        if not income_stmt.empty:
            # Convert index to string to avoid JSON serialization issues
            income_stmt_copy = income_stmt.copy()
            income_stmt_copy.columns = income_stmt_copy.columns.astype(str)
            data['financial_data']['income_statement'] = income_stmt_copy.to_dict()
            print(f"✓ Income statement: {income_stmt.shape}")

        # Balance sheet
        balance_sheet = ticker.balance_sheet
        if not balance_sheet.empty:
            balance_sheet_copy = balance_sheet.copy()
            balance_sheet_copy.columns = balance_sheet_copy.columns.astype(str)
            data['financial_data']['balance_sheet'] = balance_sheet_copy.to_dict()
            print(f"✓ Balance sheet: {balance_sheet.shape}")

        # Cash flow
        cash_flow = ticker.cashflow
        if not cash_flow.empty:
            cash_flow_copy = cash_flow.copy()
            cash_flow_copy.columns = cash_flow_copy.columns.astype(str)
            data['financial_data']['cash_flow'] = cash_flow_copy.to_dict()
            print(f"✓ Cash flow: {cash_flow.shape}")

        # Quarterly financials
        quarterly_financials = ticker.quarterly_financials
        if not quarterly_financials.empty:
            quarterly_financials_copy = quarterly_financials.copy()
            quarterly_financials_copy.columns = quarterly_financials_copy.columns.astype(str)
            data['financial_data']['quarterly_financials'] = quarterly_financials_copy.to_dict()
            print(f"✓ Quarterly financials: {quarterly_financials.shape}")

        # Quarterly balance sheet
        quarterly_balance_sheet = ticker.quarterly_balance_sheet
        if not quarterly_balance_sheet.empty:
            quarterly_balance_sheet_copy = quarterly_balance_sheet.copy()
            quarterly_balance_sheet_copy.columns = quarterly_balance_sheet_copy.columns.astype(str)
            data['financial_data']['quarterly_balance_sheet'] = quarterly_balance_sheet_copy.to_dict()
            print(f"✓ Quarterly balance sheet: {quarterly_balance_sheet.shape}")

        # Quarterly cash flow
        quarterly_cashflow = ticker.quarterly_cashflow
        if not quarterly_cashflow.empty:
            quarterly_cashflow_copy = quarterly_cashflow.copy()
            quarterly_cashflow_copy.columns = quarterly_cashflow_copy.columns.astype(str)
            data['financial_data']['quarterly_cashflow'] = quarterly_cashflow_copy.to_dict()
            print(f"✓ Quarterly cash flow: {quarterly_cashflow.shape}")

    except Exception as e:
        print(f"✗ Error fetching financial data: {e}")

    # Get price data
    try:
        # Historical data (1 year)
        hist_1y = ticker.history(period="1y")
        if not hist_1y.empty:
            hist_1y_copy = hist_1y.copy()
            hist_1y_copy.index = hist_1y_copy.index.astype(str)
            data['price_data']['history_1y'] = hist_1y_copy.to_dict()
            print(f"✓ 1Y price history: {hist_1y.shape}")

        # Historical data (5 years)
        hist_5y = ticker.history(period="5y")
        if not hist_5y.empty:
            hist_5y_copy = hist_5y.copy()
            hist_5y_copy.index = hist_5y_copy.index.astype(str)
            data['price_data']['history_5y'] = hist_5y_copy.to_dict()
            print(f"✓ 5Y price history: {hist_5y.shape}")

        # Dividends
        dividends = ticker.dividends
        if not dividends.empty:
            dividends_copy = dividends.copy()
            dividends_copy.index = dividends_copy.index.astype(str)
            data['price_data']['dividends'] = dividends_copy.to_dict()
            print(f"✓ Dividends: {len(dividends)} records")

        # Splits
        splits = ticker.splits
        if not splits.empty:
            splits_copy = splits.copy()
            splits_copy.index = splits_copy.index.astype(str)
            data['price_data']['splits'] = splits_copy.to_dict()
            print(f"✓ Splits: {len(splits)} records")

    except Exception as e:
        print(f"✗ Error fetching price data: {e}")

    # Get news
    try:
        news = ticker.news
        if news:
            data['news'] = news
            print(f"✓ News: {len(news)} articles")
    except Exception as e:
        print(f"✗ Error fetching news: {e}")

    # Get recommendations
    try:
        recommendations = ticker.recommendations
        if recommendations is not None and not recommendations.empty:
            data['recommendations'] = recommendations.to_dict()
            print(f"✓ Recommendations: {recommendations.shape}")
    except Exception as e:
        print(f"✗ Error fetching recommendations: {e}")

    # Get calendar
    try:
        calendar = ticker.calendar
        if calendar is not None:
            if hasattr(calendar, 'empty') and not calendar.empty:
                data['calendar'] = calendar.to_dict()
                print(f"✓ Calendar: {calendar.shape}")
            elif isinstance(calendar, dict):
                data['calendar'] = calendar
                print(f"✓ Calendar: {len(calendar)} fields")
    except Exception as e:
        print(f"✗ Error fetching calendar: {e}")

    # Get institutional holders
    try:
        institutional_holders = ticker.institutional_holders
        if institutional_holders is not None and not institutional_holders.empty:
            data['institutional_holders'] = institutional_holders.to_dict()
            print(f"✓ Institutional holders: {institutional_holders.shape}")
    except Exception as e:
        print(f"✗ Error fetching institutional holders: {e}")

    # Get major holders
    try:
        major_holders = ticker.major_holders
        if major_holders is not None and not major_holders.empty:
            data['major_holders'] = major_holders.to_dict()
            print(f"✓ Major holders: {major_holders.shape}")
    except Exception as e:
        print(f"✗ Error fetching major holders: {e}")

    # Get sustainability data
    try:
        sustainability = ticker.sustainability
        if sustainability is not None and not sustainability.empty:
            data['sustainability'] = sustainability.to_dict()
            print(f"✓ Sustainability: {sustainability.shape}")
    except Exception as e:
        print(f"✗ Error fetching sustainability: {e}")

    # Get analyst price targets
    try:
        analyst_price_targets = ticker.analyst_price_targets
        if analyst_price_targets:
            data['analyst_price_targets'] = analyst_price_targets
            print(f"✓ Analyst price targets: {len(analyst_price_targets)} fields")
    except Exception as e:
        print(f"✗ Error fetching analyst price targets: {e}")

    return data

def save_data_to_files(data, output_dir):
    """
    Save data to multiple files for easy analysis.

    Args:
        data (dict): Comprehensive stock data
        output_dir (str): Output directory
    """
    os.makedirs(output_dir, exist_ok=True)

    # Save complete data as JSON
    with open(os.path.join(output_dir, "complete_data.json"), 'w') as f:
        json.dump(data, f, indent=2, cls=CustomJSONEncoder)
    print(f"✓ Saved complete data to {output_dir}/complete_data.json")

    # Save individual sections
    sections = ['basic_info', 'financial_data', 'price_data', 'news',
                'recommendations', 'calendar', 'institutional_holders',
                'major_holders', 'sustainability', 'analyst_price_targets']

    for section in sections:
        if data.get(section):
            filename = f"{section}.json"
            with open(os.path.join(output_dir, filename), 'w') as f:
                json.dump(data[section], f, indent=2, cls=CustomJSONEncoder)
            print(f"✓ Saved {section} to {output_dir}/{filename}")

    # Create field summary
    all_fields = set()

    def extract_fields(obj, prefix=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                field_name = f"{prefix}.{key}" if prefix else key
                all_fields.add(field_name)
                if isinstance(value, dict):
                    extract_fields(value, field_name)
                elif isinstance(value, list) and value and isinstance(value[0], dict):
                    extract_fields(value[0], f"{field_name}[0]")

    extract_fields(data)

    field_summary = {
        "total_fields": len(all_fields),
        "fields": sorted(list(all_fields)),
        "sections": {
            section: len([f for f in all_fields if f.startswith(section)])
            for section in sections
        }
    }

    with open(os.path.join(output_dir, "field_summary.json"), 'w') as f:
        json.dump(field_summary, f, indent=2)
    print(f"✓ Saved field summary to {output_dir}/field_summary.json")
    print(f"Total fields available: {len(all_fields)}")

    return field_summary

def main():
    """Main function to fetch and save RELIANCE.NS data."""
    symbol = "RELIANCE.NS"
    output_dir = f"{symbol.replace('.', '_')}_comprehensive_data"

    print(f"Starting comprehensive data collection for {symbol}")
    print("=" * 60)

    # Fetch comprehensive data
    data = get_comprehensive_stock_data(symbol)

    print("\n" + "=" * 60)
    print("Saving data to files...")

    # Save data to files
    field_summary = save_data_to_files(data, output_dir)

    print("\n" + "=" * 60)
    print("Data collection completed!")
    print(f"Output directory: {output_dir}")
    print(f"Total data fields: {field_summary['total_fields']}")

    # Print section summary
    print("\nSection breakdown:")
    for section, count in field_summary['sections'].items():
        print(f"  {section}: {count} fields")

if __name__ == "__main__":
    main()
