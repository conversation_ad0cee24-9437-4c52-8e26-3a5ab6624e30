"""
<PERSON><PERSON><PERSON> to run the AI Hedge Fund with the Mock Sentiment Analyst.
"""

import sys
import os
import json
from datetime import datetime
from dateutil.relativedelta import relativedelta
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Import necessary functions from the project
from src.main import run_hedge_fund
from src.utils.display import print_trading_output
from src.llm.models import get_model_info

print(f"{Fore.GREEN}Successfully imported AI Hedge Fund functions{Style.RESET_ALL}")

def run_hedge_fund_with_mock(ticker, end_date=None, model_name="gpt-4o"):
    """
    Run the AI Hedge Fund with the Mock Sentiment Analyst.
    """
    # If end_date is not provided, use today's date
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")
    
    # Set start_date to 3 months before end_date
    end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")
    start_date_dt = end_date_dt - relativedelta(months=3)
    start_date = start_date_dt.strftime("%Y-%m-%d")
    
    print(f"\nRunning AI Hedge Fund for {Fore.CYAN}{ticker}{Style.RESET_ALL} from {start_date} to {end_date}...")
    
    # Get model info
    model_info = get_model_info(model_name)
    if model_info:
        model_provider = model_info.provider.value
    else:
        model_provider = "OpenAI"
    
    # Initialize portfolio
    portfolio = {
        "cash": 100000.0,
        "positions": {ticker: {"shares": 0, "cost_basis": 0.0}},
        "margin_requirement": 0.0,
        "realized_gains": {
            ticker: {
                "long": 0.0,
                "short": 0.0,
            }
        }
    }
    
    # Run the hedge fund with the Mock Sentiment Analyst
    result = run_hedge_fund(
        tickers=[ticker],
        start_date=start_date,
        end_date=end_date,
        portfolio=portfolio,
        show_reasoning=True,
        selected_analysts=["mock_sentiment_analyst"],
        model_name=model_name,
        model_provider=model_provider,
    )
    
    # Print the results
    print_trading_output(result)
    
    return result

if __name__ == "__main__":
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run AI Hedge Fund with Mock Sentiment Analyst.')
    parser.add_argument('ticker', type=str, help='Stock ticker symbol (e.g., AMD, AAPL)')
    parser.add_argument('--date', type=str, help='End date for analysis (YYYY-MM-DD format)', default=None)
    parser.add_argument('--model', type=str, help='LLM model to use', default="gpt-4o")
    args = parser.parse_args()
    
    # Run the analysis
    run_hedge_fund_with_mock(args.ticker, args.date, args.model)
