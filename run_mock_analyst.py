"""
<PERSON><PERSON><PERSON> to run the Mock Sentiment Analyst directly.
This script doesn't modify any existing files.
"""

import sys
import os
import json
from datetime import datetime, timedelta
import random
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Import necessary modules
from src.graph.state import AgentState, show_agent_reasoning
from src.utils.progress import progress

print(f"{Fore.GREEN}Setting up mock environment...{Style.RESET_ALL}")

# Define mock data generation functions
def generate_mock_insider_trades(ticker, end_date, start_date=None, limit=10):
    """Generate mock insider trades."""
    print(f"Generating mock insider trades for {ticker}...")

    # Check if we should use the transformed Reliance data
    mock_data_dir = os.environ.get("MOCK_DATA_DIR")
    if mock_data_dir and os.path.exists(mock_data_dir) and ticker == "RELIANCE":
        insider_trades_file = os.path.join(mock_data_dir, "insider_trades.csv")
        if os.path.exists(insider_trades_file):
            print(f"Using transformed Reliance insider trades data from {insider_trades_file}")
            import pandas as pd
            try:
                df = pd.read_csv(insider_trades_file)
                insider_trades = df.to_dict('records')
                return insider_trades
            except Exception as e:
                print(f"Error reading insider trades file: {e}")
                print("Falling back to generated mock data")

    # Get company name from ticker (simple approximation)
    company_name = f"{ticker.title()} Corporation"

    # Define executives
    executives = [
        {"name": "John Smith", "title": "CEO", "is_board_director": True},
        {"name": "Jane Doe", "title": "CFO", "is_board_director": False},
        {"name": "Robert Johnson", "title": "CTO", "is_board_director": True}
    ]

    # Parse end_date
    end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")

    # Generate dates for transactions (within last 3 months of end_date)
    transaction_dates = []
    for i in range(limit):
        days_back = random.randint(1, 90)
        transaction_date = end_date_dt - timedelta(days=days_back)
        transaction_dates.append(transaction_date)

    # Sort dates in descending order (most recent first)
    transaction_dates.sort(reverse=True)

    # Generate mock trades
    mock_trades = []
    for i in range(min(limit, len(transaction_dates))):
        # Randomly select an executive
        executive = random.choice(executives)

        # Randomly decide if buying or selling (70% chance of selling)
        is_selling = random.random() < 0.7

        # Generate transaction details
        transaction_shares = random.randint(5000, 30000) * (-1 if is_selling else 1)
        transaction_price = random.uniform(80.0, 150.0)
        transaction_value = transaction_shares * transaction_price
        shares_before = random.randint(100000, 500000)
        shares_after = shares_before + transaction_shares

        # Format dates
        transaction_date_str = transaction_dates[i].strftime("%Y-%m-%d")
        filing_date = transaction_dates[i] + timedelta(days=random.randint(1, 5))
        filing_date_str = filing_date.strftime("%Y-%m-%d")

        # Create the trade record
        trade = {
            "ticker": ticker,
            "issuer": company_name,
            "name": executive["name"],
            "title": executive["title"],
            "is_board_director": executive["is_board_director"],
            "transaction_date": transaction_date_str,
            "transaction_price_per_share": transaction_price,
            "transaction_shares": transaction_shares,
            "transaction_value": transaction_value,
            "shares_owned_before_transaction": shares_before,
            "shares_owned_after_transaction": shares_after,
            "security_title": "Common Stock",
            "filing_date": filing_date_str
        }

        mock_trades.append(trade)

    return mock_trades

def generate_mock_company_news(ticker, end_date, start_date=None, limit=20):
    """Generate mock company news."""
    print(f"Generating mock company news for {ticker}...")

    # Check if we should use the transformed Reliance data
    mock_data_dir = os.environ.get("MOCK_DATA_DIR")
    if mock_data_dir and os.path.exists(mock_data_dir) and ticker == "RELIANCE":
        news_file = os.path.join(mock_data_dir, "news_data.csv")
        if os.path.exists(news_file):
            print(f"Using transformed Reliance news data from {news_file}")
            import pandas as pd
            try:
                df = pd.read_csv(news_file)
                company_news = df.to_dict('records')
                return company_news
            except Exception as e:
                print(f"Error reading news file: {e}")
                print("Falling back to generated mock data")

    # Define news templates that can be used for any company
    news_templates = [
        {
            "title": "{ticker} Announces New Product Line, Expanding Market Reach",
            "sentiment": "positive"
        },
        {
            "title": "{ticker} Reports Strong Q1 Earnings, Raises Full-Year Guidance",
            "sentiment": "positive"
        },
        {
            "title": "{ticker} Gains Market Share Against Competitors",
            "sentiment": "positive"
        },
        {
            "title": "Analysts Concerned About {ticker}'s Increasing Expenses",
            "sentiment": "negative"
        },
        {
            "title": "{ticker} Partners with Major Tech Companies for New Solutions",
            "sentiment": "positive"
        },
        {
            "title": "{ticker} Stock Drops After Disappointing Quarterly Results",
            "sentiment": "negative"
        },
        {
            "title": "Investors Bullish on {ticker} Following Product Announcement",
            "sentiment": "positive"
        },
        {
            "title": "{ticker} Faces Regulatory Scrutiny Over Business Practices",
            "sentiment": "negative"
        },
        {
            "title": "{ticker} Expands International Operations with New Facility",
            "sentiment": "positive"
        },
        {
            "title": "Analyst Upgrades {ticker} Stock, Citing Growth Potential",
            "sentiment": "positive"
        }
    ]

    # Define possible authors and sources
    authors = ["John Smith", "Jane Doe", "Michael Johnson", "Sarah Williams", "David Brown"]
    sources = ["Bloomberg", "CNBC", "Reuters", "Wall Street Journal", "TechCrunch"]

    # Parse end_date
    end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")

    # Generate dates for news (within last 3 months of end_date)
    news_dates = []
    for i in range(min(limit, 20)):  # Generate up to 20 news items or limit, whichever is smaller
        days_back = random.randint(1, 90)
        news_date = end_date_dt - timedelta(days=days_back)
        news_dates.append(news_date)

    # Sort dates in descending order (most recent first)
    news_dates.sort(reverse=True)

    # Generate mock news
    mock_news = []
    for i in range(len(news_dates)):
        # Randomly select a news template
        template = random.choice(news_templates)

        # Format the title with the ticker
        title = template["title"].format(ticker=ticker)

        # Generate other news details
        author = random.choice(authors)
        source = random.choice(sources)

        # Format date as string (not Timestamp)
        news_date = news_dates[i]
        date_str = news_date.strftime("%Y-%m-%dT%H:%M:%SZ")

        # Create the news item
        news = {
            "ticker": ticker,
            "title": title,
            "author": author,
            "source": source,
            "date": date_str,  # Ensure this is a string
            "url": f"https://example.com/{ticker.lower()}-{i}",
            "sentiment": template["sentiment"]
        }

        mock_news.append(news)

    return mock_news

# Define the mock sentiment agent function
def mock_sentiment_analysis(ticker, end_date=None):
    """
    Run a mock sentiment analysis for a ticker.
    """
    # If end_date is not provided, use today's date
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")

    print(f"\nRunning mock sentiment analysis for {Fore.CYAN}{ticker}{Style.RESET_ALL} as of {end_date}...")

    # Generate mock insider trades
    insider_trades = generate_mock_insider_trades(ticker, end_date)

    # Generate mock company news
    company_news = generate_mock_company_news(ticker, end_date)

    # For AAPL, force a strong bullish signal
    if ticker == "AAPL":
        # Make 90% of insider trades bullish
        for i in range(len(insider_trades)):
            if i < int(len(insider_trades) * 0.9):
                insider_trades[i]["transaction_shares"] = abs(insider_trades[i]["transaction_shares"]) * 2  # Double the size for emphasis

        # Make 90% of news positive
        for i in range(len(company_news)):
            if i < int(len(company_news) * 0.9):
                company_news[i]["sentiment"] = "positive"

    # For TSLA, force a strong bearish signal
    if ticker == "TSLA":
        # Make 80% of insider trades bearish
        for i in range(len(insider_trades)):
            if i < int(len(insider_trades) * 0.8):
                insider_trades[i]["transaction_shares"] = -abs(insider_trades[i]["transaction_shares"])

        # Make 80% of news negative
        for i in range(len(company_news)):
            if i < int(len(company_news) * 0.8):
                company_news[i]["sentiment"] = "negative"

    # For RELIANCE, use the data as is (already prepared in the transformed data)
    # No need to modify the data as it's coming from our prepared CSV files

    # Calculate sentiment based on insider trades
    insider_signals = []
    for trade in insider_trades:
        if trade["transaction_shares"] < 0:
            insider_signals.append("bearish")
        else:
            insider_signals.append("bullish")

    # Calculate sentiment based on news
    news_signals = []
    for news in company_news:
        if news["sentiment"] == "positive":
            news_signals.append("bullish")
        elif news["sentiment"] == "negative":
            news_signals.append("bearish")
        else:
            news_signals.append("neutral")

    # Combine signals from both sources with weights
    insider_weight = 0.3
    news_weight = 0.7

    # Calculate weighted signal counts
    bullish_signals = (
        insider_signals.count("bullish") * insider_weight +
        news_signals.count("bullish") * news_weight
    )
    bearish_signals = (
        insider_signals.count("bearish") * insider_weight +
        news_signals.count("bearish") * news_weight
    )

    if bullish_signals > bearish_signals:
        overall_signal = "bullish"
    elif bearish_signals > bullish_signals:
        overall_signal = "bearish"
    else:
        overall_signal = "neutral"

    # Calculate confidence level based on the weighted proportion
    total_weighted_signals = len(insider_signals) * insider_weight + len(news_signals) * news_weight
    confidence = 0  # Default confidence when there are no signals
    if total_weighted_signals > 0:
        confidence = round(max(bullish_signals, bearish_signals) / total_weighted_signals, 2) * 100
    reasoning = f"Weighted Bullish signals: {bullish_signals:.1f}, Weighted Bearish signals: {bearish_signals:.1f}"

    # Create the sentiment analysis result
    sentiment_analysis = {
        ticker: {
            "signal": overall_signal,
            "confidence": confidence,
            "reasoning": reasoning,
        }
    }

    # Print the results
    print(f"\n{Fore.GREEN}Mock Sentiment Analysis for {ticker}:{Style.RESET_ALL}")
    print(f"Signal: {Fore.CYAN if overall_signal == 'bullish' else Fore.RED if overall_signal == 'bearish' else Fore.YELLOW}{overall_signal.upper()}{Style.RESET_ALL}")
    print(f"Confidence: {confidence:.1f}%")
    print(f"Reasoning: {reasoning}")

    # Print sample insider trades
    print(f"\n{Fore.GREEN}Sample Insider Trades:{Style.RESET_ALL}")
    for i, trade in enumerate(insider_trades[:3]):
        trade_type = "SELLING" if trade["transaction_shares"] < 0 else "BUYING"
        trade_color = Fore.RED if trade["transaction_shares"] < 0 else Fore.CYAN
        print(f"{i+1}. {trade['name']} ({trade['title']}): {trade_color}{trade_type} {abs(trade['transaction_shares'])} shares{Style.RESET_ALL} at ${trade['transaction_price_per_share']:.2f} ({trade['transaction_date']})")

    # Print sample news
    print(f"\n{Fore.GREEN}Sample News:{Style.RESET_ALL}")
    for i, news in enumerate(company_news[:3]):
        sentiment_color = Fore.CYAN if news["sentiment"] == "positive" else Fore.RED if news["sentiment"] == "negative" else Fore.YELLOW
        print(f"{i+1}. {news['title']} - {sentiment_color}{news['sentiment'].upper()}{Style.RESET_ALL} ({news['date']})")

    # Save the analysis to a file
    with open(f"{ticker}_mock_sentiment_analysis.json", "w") as f:
        json.dump(sentiment_analysis, f, indent=2)

    print(f"\nAnalysis saved to {Fore.GREEN}{ticker}_mock_sentiment_analysis.json{Style.RESET_ALL}")

    return sentiment_analysis

if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run mock sentiment analysis on a stock.')
    parser.add_argument('ticker', type=str, help='Stock ticker symbol (e.g., AMD, AAPL)')
    parser.add_argument('--date', type=str, help='End date for analysis (YYYY-MM-DD format)', default=None)
    args = parser.parse_args()

    # Run the analysis
    mock_sentiment_analysis(args.ticker, args.date)
