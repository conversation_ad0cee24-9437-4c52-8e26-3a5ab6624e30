"""
<PERSON><PERSON><PERSON> to run only the mock sentiment agent without the full workflow.
"""

import sys
import os
import json
from datetime import datetime
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Import the mock sentiment agent
from src.agents.mock_sentiment import mock_sentiment_agent
from src.utils.progress import progress
from src.graph.state import AgentState, show_agent_reasoning

print(f"{Fore.GREEN}Successfully imported mock sentiment agent{Style.RESET_ALL}")

def run_mock_sentiment(ticker, end_date=None):
    """
    Run the mock sentiment agent with a specific ticker.
    """
    # If end_date is not provided, use today's date
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")
    
    print(f"\nRunning mock sentiment analysis for {Fore.CYAN}{ticker}{Style.RESET_ALL} as of {end_date}...")
    
    # Create a minimal state object for the sentiment agent
    state = AgentState({
        "data": {
            "tickers": [ticker],
            "end_date": end_date,
            "analyst_signals": {},
        },
        "metadata": {
            "show_reasoning": True,
            "model_name": "gpt-4o",
            "model_provider": "OpenAI",
        },
    })
    
    # Run the mock sentiment agent
    progress.start()
    try:
        result = mock_sentiment_agent(state)
        sentiment_analysis = result["data"]["analyst_signals"]["mock_sentiment_agent"]
    finally:
        progress.stop()
    
    # Print the results
    ticker_result = sentiment_analysis[ticker]
    print(f"\n{Fore.GREEN}Mock Sentiment Analysis for {ticker}:{Style.RESET_ALL}")
    print(f"Signal: {Fore.CYAN if ticker_result['signal'] == 'bullish' else Fore.RED if ticker_result['signal'] == 'bearish' else Fore.YELLOW}{ticker_result['signal'].upper()}{Style.RESET_ALL}")
    print(f"Confidence: {ticker_result['confidence']:.1f}%")
    print(f"Reasoning: {ticker_result['reasoning']}")
    
    # Save the analysis to a file
    with open(f"{ticker}_mock_sentiment_analysis.json", "w") as f:
        json.dump(sentiment_analysis, f, indent=2)
    
    print(f"\nAnalysis saved to {Fore.GREEN}{ticker}_mock_sentiment_analysis.json{Style.RESET_ALL}")
    
    return sentiment_analysis

if __name__ == "__main__":
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run mock sentiment analysis on a stock.')
    parser.add_argument('ticker', type=str, help='Stock ticker symbol (e.g., AMD, AAPL)')
    parser.add_argument('--date', type=str, help='End date for analysis (YYYY-MM-DD format)', default=None)
    args = parser.parse_args()
    
    # Run the analysis
    run_mock_sentiment(args.ticker, args.date)
