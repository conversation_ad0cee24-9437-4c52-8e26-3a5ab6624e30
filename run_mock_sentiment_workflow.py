"""
<PERSON><PERSON><PERSON> to run the AI Hedge Fund with the Mock Sentiment Analyst.
This script properly patches the API functions to use mock data.
"""

import sys
import os
import importlib
import importlib.util
from datetime import datetime
from dateutil.relativedelta import relativedelta
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# First, we need to patch the API module before any other imports
# Get the path to our fixed mock_api module
mock_api_path = os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src', 'tools', 'mock_api_fixed.py')

# Load the mock_api module
spec = importlib.util.spec_from_file_location("mock_api_fixed", mock_api_path)
mock_api = importlib.util.module_from_spec(spec)
spec.loader.exec_module(mock_api)

# Get the path to the original API module
api_module_path = os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src', 'tools', 'api.py')

# Create a backup of the original API module if it doesn't exist
api_backup_path = api_module_path + '.bak'
if not os.path.exists(api_backup_path):
    with open(api_module_path, 'r') as f:
        original_api_code = f.read()
    with open(api_backup_path, 'w') as f:
        f.write(original_api_code)
    print(f"{Fore.GREEN}Created backup of original API module at {api_backup_path}{Style.RESET_ALL}")

# Now, let's patch the API module by replacing the functions with our mock implementations
# First, read the original API module
with open(api_module_path, 'r') as f:
    api_code = f.read()

# Define the functions we want to patch
functions_to_patch = [
    'get_prices',
    'get_financial_metrics',
    'search_line_items',
    'get_insider_trades',
    'get_company_news',
    'get_market_cap',
    'prices_to_df',
    'get_price_data'
]

# Create a temporary patched version of the API module
temp_api_path = api_module_path + '.temp'
with open(temp_api_path, 'w') as f:
    # Add a comment at the top of the file
    f.write('"""\nPATCHED API MODULE - Using mock data\n"""\n\n')

    # Add imports
    f.write('import os\n')
    f.write('import pandas as pd\n')
    f.write('import requests\n')
    f.write('from typing import List, Dict, Optional, Any, Union, cast\n\n')

    # Add imports from data models
    f.write('from data.cache import get_cache\n')
    f.write('from data.models import (\n')
    f.write('    CompanyNews,\n')
    f.write('    CompanyNewsResponse,\n')
    f.write('    FinancialMetrics,\n')
    f.write('    FinancialMetricsResponse,\n')
    f.write('    Price,\n')
    f.write('    PriceResponse,\n')
    f.write('    LineItem,\n')
    f.write('    LineItemResponse,\n')
    f.write('    InsiderTrade,\n')
    f.write('    InsiderTradeResponse,\n')
    f.write(')\n\n')

    # Add cache instance
    f.write('# Global cache instance\n')
    f.write('_cache = get_cache()\n\n')

    # Add the patched functions from our mock_api module
    import inspect
    for func_name in functions_to_patch:
        f.write(f'# Patched {func_name} function\n')
        # Get the function source code
        func = getattr(mock_api, func_name)
        func_source = inspect.getsource(func)
        f.write(func_source)
        f.write('\n\n')

# Replace the original API module with our patched version
os.replace(temp_api_path, api_module_path)
print(f"{Fore.GREEN}Successfully patched API module to use mock data{Style.RESET_ALL}")

# Now we can import the main module and other dependencies
from src.main import run_hedge_fund
from src.utils.display import print_trading_output
from src.llm.models import get_model_info

def run_hedge_fund_with_mock(ticker, end_date=None, model_name="gpt-4o"):
    """
    Run the AI Hedge Fund with the Mock Sentiment Analyst.
    """
    # If end_date is not provided, use today's date
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")

    # Set start_date to 3 months before end_date
    end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")
    start_date_dt = end_date_dt - relativedelta(months=3)
    start_date = start_date_dt.strftime("%Y-%m-%d")

    print(f"\nRunning AI Hedge Fund for {Fore.CYAN}{ticker}{Style.RESET_ALL} from {start_date} to {end_date}...")

    # Get model info
    model_info = get_model_info(model_name)
    if model_info:
        model_provider = model_info.provider.value
    else:
        model_provider = "OpenAI"

    # Initialize portfolio
    portfolio = {
        "cash": 100000.0,
        "positions": {ticker: {"shares": 0, "cost_basis": 0.0}},
        "margin_requirement": 0.0,
        "realized_gains": {
            ticker: {
                "long": 0.0,
                "short": 0.0,
            }
        }
    }

    # Run the hedge fund with the Mock Sentiment Analyst
    result = run_hedge_fund(
        tickers=[ticker],
        start_date=start_date,
        end_date=end_date,
        portfolio=portfolio,
        show_reasoning=True,
        selected_analysts=["mock_sentiment_analyst"],
        model_name=model_name,
        model_provider=model_provider,
    )

    # Print the results
    print_trading_output(result)

    return result

if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run AI Hedge Fund with Mock Sentiment Analyst.')
    parser.add_argument('ticker', type=str, help='Stock ticker symbol (e.g., AMD, AAPL)')
    parser.add_argument('--date', type=str, help='End date for analysis (YYYY-MM-DD format)', default=None)
    parser.add_argument('--model', type=str, help='LLM model to use', default="gpt-4o")
    args = parser.parse_args()

    try:
        # Run the analysis
        run_hedge_fund_with_mock(args.ticker, args.date, args.model)
    finally:
        # Restore the original API module
        if os.path.exists(api_backup_path):
            os.replace(api_backup_path, api_module_path)
            print(f"{Fore.GREEN}Restored original API module{Style.RESET_ALL}")
