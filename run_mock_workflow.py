"""
<PERSON><PERSON><PERSON> to run the AI Hedge Fund with the Mock Sentiment Analyst.
This script properly patches the API functions to use mock data.
"""

import sys
import os
import importlib
import importlib.util
from datetime import datetime
from dateutil.relativedelta import relativedelta
from colorama import Fore, Style, init
import json
import inspect

# Initialize colorama
init(autoreset=True)

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

print(f"{Fore.GREEN}Setting up mock environment...{Style.RESET_ALL}")

# Create a mock data directory if it doesn't exist
MOCK_DATA_DIR = os.path.join(os.path.dirname(__file__), 'mock_financial_data')
os.makedirs(MOCK_DATA_DIR, exist_ok=True)

# Define mock API functions that don't rely on external dependencies
def mock_get_prices(ticker, start_date, end_date):
    """Generate mock price data."""
    print(f"Generating mock price data for {ticker}...")
    
    from datetime import datetime, timedelta
    import random
    
    # Parse dates
    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")
    
    # Generate dates (excluding weekends)
    dates = []
    current = start_dt
    while current <= end_dt:
        if current.weekday() < 5:  # Monday to Friday
            dates.append(current)
        current += timedelta(days=1)
    
    # Generate price data
    base_price = 120.0  # Starting price
    prices = []
    for i, date in enumerate(dates):
        # Generate price with some randomness and a slight upward trend
        daily_change = random.uniform(-0.03, 0.035)  # -3% to +3.5%
        if i > 0:
            base_price = base_price * (1 + daily_change)
        
        # Generate high, low, open prices based on close
        high = base_price * random.uniform(1.0, 1.03)
        low = base_price * random.uniform(0.97, 1.0)
        open_price = base_price * random.uniform(0.99, 1.01)
        
        # Format date
        iso_date = date.strftime("%Y-%m-%dT%H:%M:%SZ")
        ms_timestamp = int(date.timestamp() * 1000)
        
        # Create a price object
        from src.data.models import Price
        prices.append(Price(
            ticker=ticker,
            open=round(open_price, 2),
            close=round(base_price, 2),
            high=round(high, 2),
            low=round(low, 2),
            volume=int(random.uniform(5000000, 15000000)),
            time=iso_date,
            time_milliseconds=ms_timestamp
        ))
    
    return prices

def mock_get_financial_metrics(ticker, end_date, period="ttm", limit=10):
    """Generate mock financial metrics."""
    print(f"Generating mock financial metrics for {ticker}...")
    
    # Create base metrics for the most recent period
    base_metrics = {
        "ticker": ticker,
        "report_period": "2025-03-31",
        "fiscal_period": "2025-Q1",
        "period": "ttm",
        "currency": "USD",
        "market_cap": 180000000000.0,
        "enterprise_value": 185000000000.0,
        "price_to_earnings_ratio": 35.2,
        "price_to_book_ratio": 4.8,
        "price_to_sales_ratio": 8.5,
        "enterprise_value_to_ebitda_ratio": 25.3,
        "enterprise_value_to_revenue_ratio": 8.7,
        "free_cash_flow_yield": 0.03,
        "peg_ratio": 1.2,
        "gross_margin": 0.45,
        "operating_margin": 0.18,
        "net_margin": 0.15,
        "return_on_equity": 0.12,
        "return_on_assets": 0.08,
        "return_on_invested_capital": 0.14,
        "asset_turnover": 0.6,
        "inventory_turnover": 4.5,
        "receivables_turnover": 8.2,
        "days_sales_outstanding": 44.5,
        "operating_cycle": 125.6,
        "working_capital_turnover": 4.8,
        "current_ratio": 2.1,
        "quick_ratio": 1.7,
        "cash_ratio": 0.9,
        "operating_cash_flow_ratio": 1.2,
        "debt_to_equity": 0.15,
        "debt_to_assets": 0.08,
        "interest_coverage": 25.0,
        "revenue_growth": 0.25,
        "earnings_growth": 0.32,
        "book_value_growth": 0.18,
        "earnings_per_share_growth": 0.30,
        "free_cash_flow_growth": 0.22,
        "operating_income_growth": 0.28,
        "ebitda_growth": 0.26,
        "payout_ratio": 0.0,  # No dividends
        "earnings_per_share": 3.2,
        "book_value_per_share": 12.5,
        "free_cash_flow_per_share": 2.8
    }
    
    # Create a FinancialMetrics object
    from src.data.models import FinancialMetrics
    return [FinancialMetrics(**base_metrics)]

def mock_search_line_items(ticker, line_items, end_date, period="ttm", limit=10):
    """Generate mock line items."""
    print(f"Generating mock line items for {ticker}...")
    
    # Create a record for the most recent period
    record = {
        "ticker": ticker,
        "report_period": "2025-03-31",
        "fiscal_period": "2025-Q1",
        "period": "ttm",
        "currency": "USD"
    }
    
    # Add requested line items
    if "revenue" in line_items:
        record["revenue"] = 25000000000.0  # $25 billion
    if "net_income" in line_items:
        record["net_income"] = 3750000000.0  # $3.75 billion
    if "total_assets" in line_items:
        record["total_assets"] = 65000000000.0  # $65 billion
    if "total_liabilities" in line_items:
        record["total_liabilities"] = 25000000000.0  # $25 billion
    if "total_shareholders_equity" in line_items:
        record["total_shareholders_equity"] = 40000000000.0  # $40 billion
    if "operating_income" in line_items:
        record["operating_income"] = 4500000000.0  # $4.5 billion
    if "ebitda" in line_items:
        record["ebitda"] = 5500000000.0  # $5.5 billion
    if "free_cash_flow" in line_items:
        record["free_cash_flow"] = 3000000000.0  # $3 billion
    if "capital_expenditure" in line_items:
        record["capital_expenditure"] = -2500000000.0  # $2.5 billion (negative for cash outflow)
    if "depreciation_and_amortization" in line_items:
        record["depreciation_and_amortization"] = 1200000000.0  # $1.2 billion
    if "outstanding_shares" in line_items:
        record["outstanding_shares"] = 1620000000.0  # 1.62 billion shares
    
    # Create a LineItem object
    from src.data.models import LineItem
    return [LineItem(**record)]

def mock_get_insider_trades(ticker, end_date, start_date=None, limit=1000):
    """Generate mock insider trades."""
    print(f"Generating mock insider trades for {ticker}...")
    
    from datetime import datetime, timedelta
    import random
    
    # Get company name from ticker (simple approximation)
    company_name = f"{ticker.title()} Corporation"
    
    # Define executives
    executives = [
        {"name": "John Smith", "title": "CEO", "is_board_director": True},
        {"name": "Jane Doe", "title": "CFO", "is_board_director": False},
        {"name": "Robert Johnson", "title": "CTO", "is_board_director": True}
    ]
    
    # Parse end_date
    end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")
    
    # Generate dates for transactions (within last 3 months of end_date)
    transaction_dates = []
    for i in range(10):
        days_back = random.randint(1, 90)
        transaction_date = end_date_dt - timedelta(days=days_back)
        transaction_dates.append(transaction_date)
    
    # Sort dates in descending order (most recent first)
    transaction_dates.sort(reverse=True)
    
    # Generate mock trades
    mock_trades = []
    for i in range(min(10, len(transaction_dates))):
        # Randomly select an executive
        executive = random.choice(executives)
        
        # Randomly decide if buying or selling (70% chance of selling)
        is_selling = random.random() < 0.7
        
        # Generate transaction details
        transaction_shares = random.randint(5000, 30000) * (-1 if is_selling else 1)
        transaction_price = random.uniform(80.0, 150.0)
        transaction_value = transaction_shares * transaction_price
        shares_before = random.randint(100000, 500000)
        shares_after = shares_before + transaction_shares
        
        # Format dates
        transaction_date_str = transaction_dates[i].strftime("%Y-%m-%d")
        filing_date = transaction_dates[i] + timedelta(days=random.randint(1, 5))
        filing_date_str = filing_date.strftime("%Y-%m-%d")
        
        # Create the trade record
        trade = {
            "ticker": ticker,
            "issuer": company_name,
            "name": executive["name"],
            "title": executive["title"],
            "is_board_director": executive["is_board_director"],
            "transaction_date": transaction_date_str,
            "transaction_shares": transaction_shares,
            "transaction_price_per_share": transaction_price,
            "transaction_value": transaction_value,
            "shares_owned_before_transaction": shares_before,
            "shares_owned_after_transaction": shares_after,
            "security_title": "Common Stock",
            "filing_date": filing_date_str
        }
        
        mock_trades.append(trade)
    
    # Create InsiderTrade objects
    from src.data.models import InsiderTrade
    return [InsiderTrade(**trade) for trade in mock_trades]

def mock_get_company_news(ticker, end_date, start_date=None, limit=1000):
    """Generate mock company news."""
    print(f"Generating mock company news for {ticker}...")
    
    from datetime import datetime, timedelta
    import random
    
    # Define news templates that can be used for any company
    news_templates = [
        {
            "title": "{ticker} Announces New Product Line, Expanding Market Reach",
            "sentiment": "positive"
        },
        {
            "title": "{ticker} Reports Strong Q1 Earnings, Raises Full-Year Guidance",
            "sentiment": "positive"
        },
        {
            "title": "{ticker} Gains Market Share Against Competitors",
            "sentiment": "positive"
        },
        {
            "title": "Analysts Concerned About {ticker}'s Increasing Expenses",
            "sentiment": "negative"
        },
        {
            "title": "{ticker} Partners with Major Tech Companies for New Solutions",
            "sentiment": "positive"
        },
        {
            "title": "{ticker} Stock Drops After Disappointing Quarterly Results",
            "sentiment": "negative"
        },
        {
            "title": "Investors Bullish on {ticker} Following Product Announcement",
            "sentiment": "positive"
        },
        {
            "title": "{ticker} Faces Regulatory Scrutiny Over Business Practices",
            "sentiment": "negative"
        },
        {
            "title": "{ticker} Expands International Operations with New Facility",
            "sentiment": "positive"
        },
        {
            "title": "Analyst Upgrades {ticker} Stock, Citing Growth Potential",
            "sentiment": "positive"
        }
    ]
    
    # Define possible authors and sources
    authors = ["John Smith", "Jane Doe", "Michael Johnson", "Sarah Williams", "David Brown"]
    sources = ["Bloomberg", "CNBC", "Reuters", "Wall Street Journal", "TechCrunch"]
    
    # Parse end_date
    end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")
    
    # Generate dates for news (within last 3 months of end_date)
    news_dates = []
    for i in range(min(limit, 20)):  # Generate up to 20 news items or limit, whichever is smaller
        days_back = random.randint(1, 90)
        news_date = end_date_dt - timedelta(days=days_back)
        news_dates.append(news_date)
    
    # Sort dates in descending order (most recent first)
    news_dates.sort(reverse=True)
    
    # Generate mock news
    mock_news = []
    for i in range(len(news_dates)):
        # Randomly select a news template
        template = random.choice(news_templates)
        
        # Format the title with the ticker
        title = template["title"].format(ticker=ticker)
        
        # Generate other news details
        author = random.choice(authors)
        source = random.choice(sources)
        
        # Format date as string (not Timestamp)
        news_date = news_dates[i]
        date_str = news_date.strftime("%Y-%m-%dT%H:%M:%SZ")
        
        # Create the news item
        news = {
            "ticker": ticker,
            "title": title,
            "author": author,
            "source": source,
            "date": date_str,  # Ensure this is a string
            "url": f"https://example.com/{ticker.lower()}-{i}",
            "sentiment": template["sentiment"]
        }
        
        mock_news.append(news)
    
    # Create CompanyNews objects
    from src.data.models import CompanyNews
    return [CompanyNews(**news) for news in mock_news]

def mock_get_market_cap(ticker, end_date):
    """Get market cap from mock financial metrics."""
    metrics = mock_get_financial_metrics(ticker, end_date)
    if not metrics:
        return None
    
    return metrics[0].market_cap

def mock_prices_to_df(prices):
    """Convert prices to a DataFrame."""
    import pandas as pd
    
    df = pd.DataFrame([p.model_dump() for p in prices])
    df["Date"] = pd.to_datetime(df["time"])
    df.set_index("Date", inplace=True)
    numeric_cols = ["open", "close", "high", "low", "volume"]
    for col in numeric_cols:
        df[col] = pd.to_numeric(df[col], errors="coerce")
    df.sort_index(inplace=True)
    return df

def mock_get_price_data(ticker, start_date, end_date):
    """Get price data as DataFrame."""
    prices = mock_get_prices(ticker, start_date, end_date)
    return mock_prices_to_df(prices)

# Patch the API module
def patch_api_module():
    """Patch the API module to use our mock functions."""
    # Import the API module
    import src.tools.api as api
    
    # Patch the API functions
    api.get_prices = mock_get_prices
    api.get_financial_metrics = mock_get_financial_metrics
    api.search_line_items = mock_search_line_items
    api.get_insider_trades = mock_get_insider_trades
    api.get_company_news = mock_get_company_news
    api.get_market_cap = mock_get_market_cap
    api.prices_to_df = mock_prices_to_df
    api.get_price_data = mock_get_price_data
    
    print(f"{Fore.GREEN}Successfully patched API module to use mock data{Style.RESET_ALL}")

# Patch the LLM module to use a mock LLM
def patch_llm_module():
    """Patch the LLM module to use a mock LLM."""
    # Import the LLM module
    import src.utils.llm as llm
    
    # Define a mock LLM function
    def mock_call_llm(prompt, model_name=None, model_provider=None, temperature=0.0, max_tokens=None):
        """Mock LLM function that returns a predefined response."""
        print(f"Mock LLM call with model: {model_name}, provider: {model_provider}")
        
        # Return a generic response based on the prompt
        if "portfolio management" in prompt.lower():
            return json.dumps({
                "AMD": {
                    "action": "hold",
                    "quantity": 0,
                    "confidence": 50.0,
                    "reasoning": "Based on the mixed signals from analysts, I recommend holding the current position."
                }
            })
        elif "risk management" in prompt.lower():
            return json.dumps({
                "AMD": {
                    "remaining_position_limit": 20000.0,
                    "current_price": 120.0,
                    "reasoning": {
                        "portfolio_value": 100000.0,
                        "current_position": 0.0,
                        "position_limit": 20000.0,
                        "remaining_limit": 20000.0,
                        "available_cash": 100000.0
                    }
                }
            })
        else:
            return json.dumps({
                "response": "This is a mock LLM response."
            })
    
    # Patch the LLM function
    llm.call_llm = mock_call_llm
    
    print(f"{Fore.GREEN}Successfully patched LLM module to use mock LLM{Style.RESET_ALL}")

# Apply the patches
patch_api_module()
patch_llm_module()

# Now we can import the main module and other dependencies
from src.main import run_hedge_fund
from src.utils.display import print_trading_output
from src.llm.models import get_model_info

def run_hedge_fund_with_mock(ticker, end_date=None, model_name="gpt-4o"):
    """
    Run the AI Hedge Fund with the Mock Sentiment Analyst.
    """
    # If end_date is not provided, use today's date
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")
    
    # Set start_date to 3 months before end_date
    end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")
    start_date_dt = end_date_dt - relativedelta(months=3)
    start_date = start_date_dt.strftime("%Y-%m-%d")
    
    print(f"\nRunning AI Hedge Fund for {Fore.CYAN}{ticker}{Style.RESET_ALL} from {start_date} to {end_date}...")
    
    # Get model info
    model_info = get_model_info(model_name)
    if model_info:
        model_provider = model_info.provider.value
    else:
        model_provider = "OpenAI"
    
    # Initialize portfolio
    portfolio = {
        "cash": 100000.0,
        "positions": {ticker: {"shares": 0, "cost_basis": 0.0}},
        "margin_requirement": 0.0,
        "realized_gains": {
            ticker: {
                "long": 0.0,
                "short": 0.0,
            }
        }
    }
    
    # Run the hedge fund with the Mock Sentiment Analyst
    result = run_hedge_fund(
        tickers=[ticker],
        start_date=start_date,
        end_date=end_date,
        portfolio=portfolio,
        show_reasoning=True,
        selected_analysts=["mock_sentiment_analyst"],
        model_name=model_name,
        model_provider=model_provider,
    )
    
    # Print the results
    print_trading_output(result)
    
    return result

if __name__ == "__main__":
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run AI Hedge Fund with Mock Sentiment Analyst.')
    parser.add_argument('ticker', type=str, help='Stock ticker symbol (e.g., AMD, AAPL)')
    parser.add_argument('--date', type=str, help='End date for analysis (YYYY-MM-DD format)', default=None)
    parser.add_argument('--model', type=str, help='LLM model to use', default="gpt-4o")
    args = parser.parse_args()
    
    # Run the analysis
    run_hedge_fund_with_mock(args.ticker, args.date, args.model)
