"""
Run the AI Hedge Fund with NSE data (RELIANCE and TCS).
This script uses our extracted NSE data instead of the original mock AMD data.
"""

import sys
import os
import json
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Add the AI hedge fund path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Import our NSE mock API functions
from nse_mock_api import (
    get_prices,
    get_financial_metrics,
    search_line_items,
    get_insider_trades,
    get_company_news,
    get_market_cap,
    prices_to_df,
    get_price_data
)

# Monkey patch the API functions to use our NSE data
import src.tools.api as api
api.get_prices = get_prices
api.get_financial_metrics = get_financial_metrics
api.search_line_items = search_line_items
api.get_insider_trades = get_insider_trades
api.get_company_news = get_company_news
api.get_market_cap = get_market_cap
api.prices_to_df = prices_to_df
api.get_price_data = get_price_data

print(f"{Fore.GREEN}Successfully patched API functions to use NSE data{Style.RESET_ALL}")

# Now import the main hedge fund module
from src.main import run_hedge_fund
from src.utils.display import print_trading_output
from src.llm.models import get_model_info

def run_nse_hedge_fund(ticker="RELIANCE.NS", end_date=None, model_name="gpt-4o", model_provider="openai"):
    """Run the AI hedge fund with NSE data."""
    
    print(f"\n{Fore.CYAN}=" * 80)
    print(f"{Fore.CYAN}AI HEDGE FUND - NSE STOCK ANALYSIS")
    print(f"{Fore.CYAN}=" * 80)
    
    # Set default end date to today
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")
    
    # Calculate start date (1 month before end date)
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")
    start_dt = end_dt - relativedelta(months=1)
    start_date = start_dt.strftime("%Y-%m-%d")
    
    print(f"{Fore.YELLOW}Ticker: {ticker}")
    print(f"{Fore.YELLOW}Analysis Period: {start_date} to {end_date}")
    print(f"{Fore.YELLOW}Model: {model_name} ({model_provider})")
    
    # Validate that we have data for this ticker
    data_available = validate_nse_data(ticker)
    if not data_available:
        print(f"{Fore.RED}Error: No data available for {ticker}")
        print(f"{Fore.RED}Available tickers: RELIANCE.NS")
        return None
    
    # Initialize portfolio
    portfolio = {
        "cash": 1000000.0,  # 10 lakh INR
        "positions": {ticker: {"shares": 0, "cost_basis": 0.0}},
        "margin_requirement": 0.0,
        "realized_gains": {
            ticker: {
                "long": 0.0,
                "short": 0.0,
            }
        }
    }
    
    print(f"\n{Fore.GREEN}Starting AI Hedge Fund Analysis...{Style.RESET_ALL}")
    
    # Run the hedge fund with mock sentiment analyst (to avoid API costs)
    result = run_hedge_fund(
        tickers=[ticker],
        start_date=start_date,
        end_date=end_date,
        portfolio=portfolio,
        show_reasoning=True,
        selected_analysts=["mock_sentiment_analyst"],
        model_name=model_name,
        model_provider=model_provider,
    )
    
    # Print the results
    print_trading_output(result)
    
    return result

def validate_nse_data(ticker):
    """Validate that we have data for the given ticker."""
    try:
        # Check if we can load financial metrics
        metrics = get_financial_metrics(ticker, "2025-12-31", limit=1)
        if not metrics:
            return False
        
        # Check if we can load price data
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        prices = get_prices(ticker, start_date, end_date)
        if not prices:
            return False
        
        print(f"{Fore.GREEN}✅ Data validation successful for {ticker}")
        print(f"   📊 Financial metrics: {len(metrics)} periods")
        print(f"   📈 Price data: {len(prices)} days")
        
        return True
        
    except Exception as e:
        print(f"{Fore.RED}❌ Data validation failed for {ticker}: {e}")
        return False

def run_multi_stock_analysis():
    """Run analysis on multiple NSE stocks."""
    
    print(f"\n{Fore.MAGENTA}=" * 80)
    print(f"{Fore.MAGENTA}MULTI-STOCK NSE ANALYSIS")
    print(f"{Fore.MAGENTA}=" * 80)
    
    # List of NSE stocks to analyze
    nse_stocks = ["RELIANCE.NS"]  # Add "TCS.NS" when we extract TCS data
    
    results = {}
    
    for ticker in nse_stocks:
        print(f"\n{Fore.CYAN}Analyzing {ticker}...{Style.RESET_ALL}")
        
        try:
            result = run_nse_hedge_fund(ticker)
            results[ticker] = result
            
            print(f"{Fore.GREEN}✅ Analysis completed for {ticker}{Style.RESET_ALL}")
            
        except Exception as e:
            print(f"{Fore.RED}❌ Analysis failed for {ticker}: {e}{Style.RESET_ALL}")
            results[ticker] = None
    
    # Summary
    print(f"\n{Fore.MAGENTA}=" * 80)
    print(f"{Fore.MAGENTA}MULTI-STOCK ANALYSIS SUMMARY")
    print(f"{Fore.MAGENTA}=" * 80)
    
    successful = sum(1 for result in results.values() if result is not None)
    total = len(results)
    
    print(f"{Fore.YELLOW}Successful analyses: {successful}/{total}")
    
    for ticker, result in results.items():
        status = "✅" if result is not None else "❌"
        print(f"  {status} {ticker}")
    
    return results

def test_data_integration():
    """Test the integration between our NSE data and the AI hedge fund system."""
    
    print(f"\n{Fore.BLUE}=" * 80)
    print(f"{Fore.BLUE}NSE DATA INTEGRATION TEST")
    print(f"{Fore.BLUE}=" * 80)
    
    ticker = "RELIANCE.NS"
    
    print(f"{Fore.YELLOW}Testing data integration for {ticker}...")
    
    # Test 1: Financial Metrics
    print(f"\n📊 Test 1: Financial Metrics")
    try:
        metrics = get_financial_metrics(ticker, "2025-12-31", limit=3)
        print(f"   ✅ Loaded {len(metrics)} financial metric periods")
        if metrics:
            latest = metrics[0]
            print(f"   📈 Latest P/E Ratio: {latest.price_to_earnings_ratio}")
            print(f"   📈 Latest ROE: {latest.return_on_equity}")
            print(f"   📈 Latest Market Cap: {latest.market_cap:,.0f}")
    except Exception as e:
        print(f"   ❌ Financial metrics test failed: {e}")
    
    # Test 2: Price Data
    print(f"\n📈 Test 2: Price Data")
    try:
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        prices = get_prices(ticker, start_date, end_date)
        print(f"   ✅ Loaded {len(prices)} price records")
        if prices:
            latest_price = prices[0]
            print(f"   💰 Latest Close: ₹{latest_price.close}")
            print(f"   📊 Latest Volume: {latest_price.volume:,}")
    except Exception as e:
        print(f"   ❌ Price data test failed: {e}")
    
    # Test 3: Line Items
    print(f"\n📋 Test 3: Line Items")
    try:
        line_items = search_line_items(ticker, ["revenue", "net_income"], "2025-12-31", limit=2)
        print(f"   ✅ Loaded {len(line_items)} line item periods")
        if line_items:
            latest = line_items[0]
            print(f"   💰 Latest Revenue: ₹{getattr(latest, 'revenue', 0):,.0f}")
            print(f"   💰 Latest Net Income: ₹{getattr(latest, 'net_income', 0):,.0f}")
    except Exception as e:
        print(f"   ❌ Line items test failed: {e}")
    
    # Test 4: Company News
    print(f"\n📰 Test 4: Company News")
    try:
        news = get_company_news(ticker, "2025-12-31", limit=3)
        print(f"   ✅ Loaded {len(news)} news articles")
        if news:
            latest_news = news[0]
            print(f"   📰 Latest: {latest_news.title[:60]}...")
            print(f"   😊 Sentiment: {latest_news.sentiment}")
    except Exception as e:
        print(f"   ❌ Company news test failed: {e}")
    
    print(f"\n{Fore.GREEN}✅ Data integration test completed!{Style.RESET_ALL}")

if __name__ == "__main__":
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run AI Hedge Fund with NSE data.')
    parser.add_argument('--ticker', type=str, help='NSE stock ticker (e.g., RELIANCE.NS, TCS.NS)', default="RELIANCE.NS")
    parser.add_argument('--date', type=str, help='End date for analysis (YYYY-MM-DD format)', default=None)
    parser.add_argument('--model', type=str, help='LLM model to use', default="gpt-4o")
    parser.add_argument('--test', action='store_true', help='Run data integration test')
    parser.add_argument('--multi', action='store_true', help='Run multi-stock analysis')
    
    args = parser.parse_args()
    
    try:
        if args.test:
            # Run integration test
            test_data_integration()
        elif args.multi:
            # Run multi-stock analysis
            run_multi_stock_analysis()
        else:
            # Run single stock analysis
            run_nse_hedge_fund(args.ticker, args.date, args.model)
            
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Analysis interrupted by user{Style.RESET_ALL}")
    except Exception as e:
        print(f"\n{Fore.RED}Error: {e}{Style.RESET_ALL}")
        import traceback
        traceback.print_exc()
