#!/usr/bin/env python3
"""
NSE Hedge Fund CLI
Runs the normal AI hedge fund CLI but automatically patches API for NSE tickers.
This provides the exact same user experience as the normal hedge fund.
"""

import sys
import os
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

def detect_nse_tickers(args):
    """Detect if any tickers are NSE stocks from command line args."""
    nse_tickers = []
    
    # Find --tickers argument
    if '--tickers' in args:
        try:
            tickers_index = args.index('--tickers') + 1
            if tickers_index < len(args):
                tickers_str = args[tickers_index]
                tickers = [ticker.strip() for ticker in tickers_str.split(',')]
                
                for ticker in tickers:
                    if ticker.endswith('.NS') or ticker in ['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK', 'HINDUNILVR', 'ITC', 'SBIN', 'BHARTIARTL', 'KOTAKBANK']:
                        # Convert to .NS format if not already
                        if not ticker.endswith('.NS'):
                            ticker = f"{ticker}.NS"
                        nse_tickers.append(ticker)
        except (ValueError, IndexError):
            pass
    
    return nse_tickers

def patch_api_for_nse():
    """Patch the API functions to use NSE data."""
    try:
        # Add paths
        sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
        sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))
        
        # Import our NSE mock API functions
        from nse_mock_api import (
            get_prices,
            get_financial_metrics,
            search_line_items,
            get_insider_trades,
            get_company_news,
            get_market_cap,
            prices_to_df,
            get_price_data
        )
        
        # Import and patch the API module
        import tools.api as api
        
        # Patch all functions
        api.get_prices = get_prices
        api.get_financial_metrics = get_financial_metrics
        api.search_line_items = search_line_items
        api.get_insider_trades = get_insider_trades
        api.get_company_news = get_company_news
        api.get_market_cap = get_market_cap
        api.prices_to_df = prices_to_df
        api.get_price_data = get_price_data
        
        print(f"{Fore.GREEN}✅ NSE data integration activated{Style.RESET_ALL}")
        return True
        
    except Exception as e:
        print(f"{Fore.RED}❌ Failed to patch API for NSE data: {e}{Style.RESET_ALL}")
        return False

def validate_nse_data_availability(nse_tickers):
    """Validate that we have data for the NSE tickers."""
    available_data = {
        "RELIANCE.NS": "RELIANCE_NS_complete_data",
        # Add more as we extract them
        # "TCS.NS": "TCS_NS_complete_data",
        # "HDFCBANK.NS": "HDFCBANK_NS_complete_data",
    }
    
    missing_data = []
    for ticker in nse_tickers:
        if ticker not in available_data:
            missing_data.append(ticker)
        else:
            data_dir = available_data[ticker]
            if not os.path.exists(data_dir):
                missing_data.append(ticker)
    
    if missing_data:
        print(f"\n{Fore.YELLOW}⚠️ Missing NSE data for: {', '.join(missing_data)}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Available NSE stocks: {', '.join(available_data.keys())}{Style.RESET_ALL}")
        print(f"\n{Fore.CYAN}To add more NSE stocks:{Style.RESET_ALL}")
        print(f"1. Run: python screener_mcp_final.py  # Enter stock symbol")
        print(f"2. Run: python complete_nse_data_extractor.py  # Modify symbol")
        print(f"3. Update nse_mock_api.py NSE_DATA_DIRS")
        return False
    
    return True

def main():
    """Main function that runs the normal AI hedge fund with NSE support."""
    
    print(f"{Fore.CYAN}🚀 NSE-Enhanced AI Hedge Fund CLI{Style.RESET_ALL}")
    print(f"{Fore.CYAN}=" * 50)
    print(f"{Fore.YELLOW}Supports both US stocks (via API) and NSE stocks (via extracted data){Style.RESET_ALL}")
    print(f"{Fore.YELLOW}NSE tickers: Use .NS suffix (e.g., RELIANCE.NS, TCS.NS){Style.RESET_ALL}\n")
    
    # Check if tickers are provided
    if len(sys.argv) < 2 or '--tickers' not in sys.argv:
        print(f"{Fore.RED}❌ Please provide tickers using --tickers argument{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Example: python run_nse_hedge_fund_cli.py --tickers RELIANCE.NS,TCS.NS{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Example: python run_nse_hedge_fund_cli.py --tickers AAPL,GOOGL{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Example: python run_nse_hedge_fund_cli.py --tickers RELIANCE.NS,AAPL{Style.RESET_ALL}")
        sys.exit(1)
    
    # Detect NSE tickers
    nse_tickers = detect_nse_tickers(sys.argv)
    us_tickers = []
    
    if '--tickers' in sys.argv:
        try:
            tickers_index = sys.argv.index('--tickers') + 1
            if tickers_index < len(sys.argv):
                tickers_str = sys.argv[tickers_index]
                all_tickers = [ticker.strip() for ticker in tickers_str.split(',')]
                us_tickers = [t for t in all_tickers if t not in nse_tickers and not t.endswith('.NS')]
        except (ValueError, IndexError):
            pass
    
    print(f"{Fore.GREEN}📊 Ticker Analysis:{Style.RESET_ALL}")
    if nse_tickers:
        print(f"  🇮🇳 NSE stocks: {', '.join(nse_tickers)}")
    if us_tickers:
        print(f"  🇺🇸 US stocks: {', '.join(us_tickers)}")
    
    # If we have NSE tickers, patch the API and validate data
    if nse_tickers:
        print(f"\n{Fore.YELLOW}🔧 NSE tickers detected - activating NSE data integration...{Style.RESET_ALL}")
        
        # Validate NSE data availability
        if not validate_nse_data_availability(nse_tickers):
            sys.exit(1)
        
        # Patch the API
        if not patch_api_for_nse():
            sys.exit(1)
        
        print(f"{Fore.GREEN}✅ Ready to analyze NSE stocks with extracted data{Style.RESET_ALL}")
    
    if us_tickers:
        print(f"{Fore.BLUE}📡 US stocks will use normal API (requires API keys){Style.RESET_ALL}")
    
    print(f"\n{Fore.CYAN}🎯 Starting normal AI hedge fund workflow...{Style.RESET_ALL}")
    print(f"{Fore.CYAN}=" * 50)
    
    # Now run the original main.py with the same arguments
    try:
        # Change to the AI hedge fund directory
        original_cwd = os.getcwd()
        hedge_fund_src_dir = os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src')
        
        # Import and run the main module
        sys.path.insert(0, hedge_fund_src_dir)
        os.chdir(hedge_fund_src_dir)
        
        # Modify sys.argv to look like we're running main.py directly
        original_argv = sys.argv.copy()
        sys.argv[0] = 'main.py'
        
        # Import and execute the main module
        import main
        
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Analysis interrupted by user{Style.RESET_ALL}")
    except Exception as e:
        print(f"\n{Fore.RED}Error running hedge fund: {e}{Style.RESET_ALL}")
        import traceback
        traceback.print_exc()
    finally:
        # Restore original state
        sys.argv = original_argv
        os.chdir(original_cwd)

if __name__ == "__main__":
    main()
