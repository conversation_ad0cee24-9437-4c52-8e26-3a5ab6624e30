#!/usr/bin/env python3
"""
NSE-Enhanced AI Hedge Fund
Runs the normal AI hedge fund CLI but automatically uses NSE data when NSE tickers are detected.
This provides the exact same user experience as the normal hedge fund but with NSE data support.
"""

import sys
import os
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Add the AI hedge fund path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

def detect_nse_tickers(tickers):
    """Detect if any tickers are NSE stocks."""
    nse_tickers = []
    us_tickers = []

    for ticker in tickers:
        if ticker.endswith('.NS') or ticker in ['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK', 'HINDUNILVR', 'ITC', 'SBIN', 'BHARTIARTL', 'KOTAKBANK']:
            # Convert to .NS format if not already
            if not ticker.endswith('.NS'):
                ticker = f"{ticker}.NS"
            nse_tickers.append(ticker)
        else:
            us_tickers.append(ticker)

    return nse_tickers, us_tickers

def patch_api_for_nse():
    """Patch the API functions to use NSE data."""
    try:
        # Import our NSE mock API functions
        from nse_mock_api import (
            get_prices,
            get_financial_metrics,
            search_line_items,
            get_insider_trades,
            get_company_news,
            get_market_cap,
            prices_to_df,
            get_price_data
        )

        # Monkey patch the API functions
        import src.tools.api as api
        api.get_prices = get_prices
        api.get_financial_metrics = get_financial_metrics
        api.search_line_items = search_line_items
        api.get_insider_trades = get_insider_trades
        api.get_company_news = get_company_news
        api.get_market_cap = get_market_cap
        api.prices_to_df = prices_to_df
        api.get_price_data = get_price_data

        print(f"{Fore.GREEN}✅ NSE data integration activated{Style.RESET_ALL}")
        return True

    except Exception as e:
        print(f"{Fore.RED}❌ Failed to patch API for NSE data: {e}{Style.RESET_ALL}")
        return False

def validate_nse_data_availability(nse_tickers):
    """Validate that we have data for the NSE tickers."""
    available_data = {
        "RELIANCE.NS": "RELIANCE_NS_complete_data",
        # Add more as we extract them
        # "TCS.NS": "TCS_NS_complete_data",
        # "HDFCBANK.NS": "HDFCBANK_NS_complete_data",
    }

    missing_data = []
    for ticker in nse_tickers:
        if ticker not in available_data:
            missing_data.append(ticker)
        else:
            data_dir = available_data[ticker]
            if not os.path.exists(data_dir):
                missing_data.append(ticker)

    if missing_data:
        print(f"\n{Fore.YELLOW}⚠️ Missing NSE data for: {', '.join(missing_data)}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Available NSE stocks: {', '.join(available_data.keys())}{Style.RESET_ALL}")
        print(f"\n{Fore.CYAN}To add more NSE stocks:{Style.RESET_ALL}")
        print(f"1. Run: python screener_mcp_final.py  # Enter stock symbol")
        print(f"2. Run: python complete_nse_data_extractor.py  # Modify symbol")
        print(f"3. Update nse_mock_api.py NSE_DATA_DIRS")
        return False

    return True

def main():
    """Main function that runs the normal AI hedge fund with NSE support."""

    print(f"{Fore.CYAN}🚀 NSE-Enhanced AI Hedge Fund{Style.RESET_ALL}")
    print(f"{Fore.CYAN}=" * 50)
    print(f"{Fore.YELLOW}Supports both US stocks (via API) and NSE stocks (via extracted data){Style.RESET_ALL}")
    print(f"{Fore.YELLOW}NSE tickers: Use .NS suffix (e.g., RELIANCE.NS, TCS.NS){Style.RESET_ALL}\n")

    # Parse command line arguments to detect tickers early
    if len(sys.argv) < 2 or '--tickers' not in sys.argv:
        print(f"{Fore.RED}❌ Please provide tickers using --tickers argument{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Example: python run_nse_hedge_fund_normal.py --tickers RELIANCE.NS,TCS.NS{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Example: python run_nse_hedge_fund_normal.py --tickers AAPL,GOOGL{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Example: python run_nse_hedge_fund_normal.py --tickers RELIANCE.NS,AAPL{Style.RESET_ALL}")
        sys.exit(1)

    # Extract tickers from command line
    try:
        tickers_arg_index = sys.argv.index('--tickers') + 1
        tickers_str = sys.argv[tickers_arg_index]
        tickers = [ticker.strip() for ticker in tickers_str.split(',')]
    except (ValueError, IndexError):
        print(f"{Fore.RED}❌ Invalid --tickers argument{Style.RESET_ALL}")
        sys.exit(1)

    # Detect NSE vs US tickers
    nse_tickers, us_tickers = detect_nse_tickers(tickers)

    print(f"{Fore.GREEN}📊 Ticker Analysis:{Style.RESET_ALL}")
    if nse_tickers:
        print(f"  🇮🇳 NSE stocks: {', '.join(nse_tickers)}")
    if us_tickers:
        print(f"  🇺🇸 US stocks: {', '.join(us_tickers)}")

    # If we have NSE tickers, patch the API
    if nse_tickers:
        print(f"\n{Fore.YELLOW}🔧 NSE tickers detected - activating NSE data integration...{Style.RESET_ALL}")

        # Validate NSE data availability
        if not validate_nse_data_availability(nse_tickers):
            sys.exit(1)

        # Patch the API
        if not patch_api_for_nse():
            sys.exit(1)

        print(f"{Fore.GREEN}✅ Ready to analyze NSE stocks with extracted data{Style.RESET_ALL}")

    if us_tickers:
        print(f"{Fore.BLUE}📡 US stocks will use normal API (requires API keys){Style.RESET_ALL}")

    print(f"\n{Fore.CYAN}🎯 Starting normal AI hedge fund workflow...{Style.RESET_ALL}")
    print(f"{Fore.CYAN}=" * 50)

    # Now execute the normal main module using subprocess
    try:
        import subprocess

        # Prepare the command
        hedge_fund_dir = os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src')
        main_py_path = os.path.join(hedge_fund_dir, 'main.py')

        # Build the command arguments (skip our script name)
        cmd_args = ['python', main_py_path] + sys.argv[1:]

        print(f"{Fore.BLUE}Executing: {' '.join(cmd_args)}{Style.RESET_ALL}\n")

        # Run the hedge fund with the same arguments
        result = subprocess.run(cmd_args, cwd=hedge_fund_dir, env=os.environ.copy())

        if result.returncode != 0:
            print(f"\n{Fore.RED}Hedge fund exited with code {result.returncode}{Style.RESET_ALL}")

    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Analysis interrupted by user{Style.RESET_ALL}")
    except Exception as e:
        print(f"\n{Fore.RED}Error running hedge fund: {e}{Style.RESET_ALL}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
