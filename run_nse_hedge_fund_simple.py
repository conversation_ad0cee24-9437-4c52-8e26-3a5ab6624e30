#!/usr/bin/env python3
"""
Simple NSE Hedge Fund Runner
Patches API and runs the normal hedge fund main.py exactly as it would run normally.
"""

import sys
import os
import subprocess
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

def detect_nse_tickers(args):
    """Detect if any tickers are NSE stocks from command line args."""
    nse_tickers = []

    # Find --tickers argument
    if '--tickers' in args:
        try:
            tickers_index = args.index('--tickers') + 1
            if tickers_index < len(args):
                tickers_str = args[tickers_index]
                tickers = [ticker.strip() for ticker in tickers_str.split(',')]

                for ticker in tickers:
                    if ticker.endswith('.NS') or ticker in ['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK', 'HINDUNILVR', 'ITC', 'SBIN', 'BHARTIARTL', 'KOTAKBANK']:
                        # Convert to .NS format if not already
                        if not ticker.endswith('.NS'):
                            ticker = f"{ticker}.NS"
                        nse_tickers.append(ticker)
        except (ValueError, IndexError):
            pass

    return nse_tickers

def validate_nse_data_availability(nse_tickers):
    """Validate that we have data for the NSE tickers."""
    available_data = {
        "RELIANCE.NS": "RELIANCE_NS_complete_data",
        # Add more as we extract them
        # "TCS.NS": "TCS_NS_complete_data",
        # "HDFCBANK.NS": "HDFCBANK_NS_complete_data",
    }

    missing_data = []
    for ticker in nse_tickers:
        if ticker not in available_data:
            missing_data.append(ticker)
        else:
            data_dir = available_data[ticker]
            if not os.path.exists(data_dir):
                missing_data.append(ticker)

    if missing_data:
        print(f"\n{Fore.YELLOW}⚠️ Missing NSE data for: {', '.join(missing_data)}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Available NSE stocks: {', '.join(available_data.keys())}{Style.RESET_ALL}")
        print(f"\n{Fore.CYAN}To add more NSE stocks:{Style.RESET_ALL}")
        print(f"1. Run: python screener_mcp_final.py  # Enter stock symbol")
        print(f"2. Run: python complete_nse_data_extractor.py  # Modify symbol")
        print(f"3. Update nse_mock_api.py NSE_DATA_DIRS")
        return False

    return True

def create_patched_main():
    """Create a patched version of main.py that imports NSE API patcher."""

    # Read the original main.py
    hedge_fund_src_dir = os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src')
    hedge_fund_dir = os.path.join(os.path.dirname(__file__), 'ai-hedge-fund')
    main_py_path = os.path.join(hedge_fund_src_dir, 'main.py')

    with open(main_py_path, 'r') as f:
        original_content = f.read()

    # Create patched version with proper .env loading
    patched_content = f"""# NSE API Patcher - Auto-imported
import sys
import os
from dotenv import load_dotenv

# Load .env from the hedge fund directory
hedge_fund_dir = r'{hedge_fund_dir}'
env_path = os.path.join(hedge_fund_dir, '.env')
load_dotenv(env_path)
print(f"Loaded .env from: {{env_path}}")

# Add paths and patch API
sys.path.append(r'{os.path.dirname(__file__)}')
from nse_api_patcher import patch_nse_api
patch_nse_api()

# Original main.py content (but skip the original load_dotenv call)
{original_content.replace('load_dotenv()', '# load_dotenv() - already loaded above')}
"""

    # Write patched version
    patched_main_path = os.path.join(os.path.dirname(__file__), 'patched_main.py')
    with open(patched_main_path, 'w') as f:
        f.write(patched_content)

    return patched_main_path

def main():
    """Main function."""

    print(f"{Fore.CYAN}🚀 NSE-Enhanced AI Hedge Fund{Style.RESET_ALL}")
    print(f"{Fore.CYAN}=" * 50)
    print(f"{Fore.YELLOW}Supports both US stocks (via API) and NSE stocks (via extracted data){Style.RESET_ALL}")
    print(f"{Fore.YELLOW}NSE tickers: Use .NS suffix (e.g., RELIANCE.NS, TCS.NS){Style.RESET_ALL}\n")

    # Check if tickers are provided
    if len(sys.argv) < 2 or '--tickers' not in sys.argv:
        print(f"{Fore.RED}❌ Please provide tickers using --tickers argument{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Example: python run_nse_hedge_fund_simple.py --tickers RELIANCE.NS{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Example: python run_nse_hedge_fund_simple.py --tickers AAPL,GOOGL{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Example: python run_nse_hedge_fund_simple.py --tickers RELIANCE.NS,AAPL{Style.RESET_ALL}")
        sys.exit(1)

    # Detect NSE tickers
    nse_tickers = detect_nse_tickers(sys.argv)

    if nse_tickers:
        print(f"{Fore.GREEN}🇮🇳 NSE tickers detected: {', '.join(nse_tickers)}{Style.RESET_ALL}")

        # Validate NSE data availability
        if not validate_nse_data_availability(nse_tickers):
            sys.exit(1)

        print(f"{Fore.YELLOW}🔧 Creating patched hedge fund with NSE data integration...{Style.RESET_ALL}")

        # Create patched main.py
        patched_main_path = create_patched_main()

        print(f"{Fore.GREEN}✅ NSE integration ready{Style.RESET_ALL}")
        print(f"\n{Fore.CYAN}🎯 Starting AI hedge fund with NSE data...{Style.RESET_ALL}")
        print(f"{Fore.CYAN}=" * 50)

        # Run the patched main.py
        try:
            # Prepare command
            cmd_args = ['python', patched_main_path] + sys.argv[1:]

            # Run with same environment
            env = os.environ.copy()

            # Execute the patched main
            result = subprocess.run(cmd_args, env=env, cwd=os.path.dirname(__file__))

            # Clean up
            if os.path.exists(patched_main_path):
                os.remove(patched_main_path)

            sys.exit(result.returncode)

        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}Analysis interrupted by user{Style.RESET_ALL}")
            # Clean up
            if os.path.exists(patched_main_path):
                os.remove(patched_main_path)
            sys.exit(0)
        except Exception as e:
            print(f"\n{Fore.RED}Error running hedge fund: {e}{Style.RESET_ALL}")
            # Clean up
            if os.path.exists(patched_main_path):
                os.remove(patched_main_path)
            sys.exit(1)

    else:
        print(f"{Fore.BLUE}🇺🇸 No NSE tickers detected - running normal hedge fund{Style.RESET_ALL}")
        print(f"{Fore.CYAN}🎯 Starting normal AI hedge fund...{Style.RESET_ALL}")

        # Run normal hedge fund
        hedge_fund_src_dir = os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src')
        main_py_path = os.path.join(hedge_fund_src_dir, 'main.py')

        cmd_args = ['python', main_py_path] + sys.argv[1:]
        result = subprocess.run(cmd_args, cwd=hedge_fund_src_dir)
        sys.exit(result.returncode)

if __name__ == "__main__":
    main()
