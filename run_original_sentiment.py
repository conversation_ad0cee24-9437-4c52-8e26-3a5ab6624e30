"""
<PERSON><PERSON><PERSON> to run the original sentiment agent from the AI Hedge Fund project with MSFT.
"""

import sys
import os
import json
from datetime import datetime
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Import the original API functions
from src.tools.api import get_insider_trades, get_company_news
from src.agents.sentiment import sentiment_agent
from src.utils.progress import progress
from src.graph.state import AgentState, show_agent_reasoning

print(f"{Fore.GREEN}Successfully imported original API functions{Style.RESET_ALL}")

def run_original_sentiment(ticker, end_date=None):
    """
    Run the original sentiment agent from the AI Hedge Fund project with a specific ticker.
    """
    # If end_date is not provided, use today's date
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")
    
    print(f"\nRunning original sentiment analysis for {Fore.CYAN}{ticker}{Style.RESET_ALL} as of {end_date}...")
    
    # Create a minimal state object for the sentiment agent
    state = AgentState({
        "data": {
            "tickers": [ticker],
            "end_date": end_date,
            "analyst_signals": {},
        },
        "metadata": {
            "show_reasoning": True,
            "model_name": "gpt-4o",
            "model_provider": "OpenAI",
        },
    })
    
    # Run the sentiment agent
    progress.start()
    try:
        result = sentiment_agent(state)
        sentiment_analysis = result["data"]["analyst_signals"]["sentiment_agent"]
    finally:
        progress.stop()
    
    # Print the results
    ticker_result = sentiment_analysis[ticker]
    print(f"\n{Fore.GREEN}Original Sentiment Analysis for {ticker}:{Style.RESET_ALL}")
    print(f"Signal: {Fore.CYAN if ticker_result['signal'] == 'bullish' else Fore.RED if ticker_result['signal'] == 'bearish' else Fore.YELLOW}{ticker_result['signal'].upper()}{Style.RESET_ALL}")
    print(f"Confidence: {ticker_result['confidence']:.1f}%")
    print(f"Reasoning: {ticker_result['reasoning']}")
    
    # Save the analysis to a file
    with open(f"{ticker}_original_sentiment_analysis.json", "w") as f:
        json.dump(sentiment_analysis, f, indent=2)
    
    print(f"\nAnalysis saved to {Fore.GREEN}{ticker}_original_sentiment_analysis.json{Style.RESET_ALL}")
    
    return sentiment_analysis

if __name__ == "__main__":
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run original sentiment analysis on a stock.')
    parser.add_argument('ticker', type=str, help='Stock ticker symbol (e.g., MSFT, AAPL)')
    parser.add_argument('--date', type=str, help='End date for analysis (YYYY-MM-DD format)', default=None)
    args = parser.parse_args()
    
    # Run the analysis
    run_original_sentiment(args.ticker, args.date)
