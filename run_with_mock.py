"""
Run the AI Hedge Fund with mock data for AMD.
This script monkey patches the API functions before importing the main module.
"""

import sys
import os
import importlib
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Import the mock API functions
from src.tools.mock_api import (
    get_prices,
    get_financial_metrics,
    search_line_items,
    get_insider_trades,
    get_company_news,
    get_market_cap,
    prices_to_df,
    get_price_data
)

# Monkey patch the API functions
import src.tools.api as api
api.get_prices = get_prices
api.get_financial_metrics = get_financial_metrics
api.search_line_items = search_line_items
api.get_insider_trades = get_insider_trades
api.get_company_news = get_company_news
api.get_market_cap = get_market_cap
api.prices_to_df = prices_to_df
api.get_price_data = get_price_data

print(f"{Fore.GREEN}Successfully patched API functions to use mock data{Style.RESET_ALL}")

# Now import and run the main module
from src.main import run_hedge_fund

# Set up the parameters
tickers = ["AMD"]
end_date = "2025-04-09"
start_date = "2025-03-10"
portfolio = {
    "positions": {},
    "total_cash": 100000.0,
}
selected_analysts = ["warren_buffett", "peter_lynch"]
model_name = "gemini-2.5-pro-exp-03-25"

print(f"\nRunning AI Hedge Fund with {Fore.CYAN}{', '.join(tickers)}{Style.RESET_ALL}")
print(f"Date range: {Fore.YELLOW}{start_date}{Style.RESET_ALL} to {Fore.YELLOW}{end_date}{Style.RESET_ALL}")
print(f"Analysts: {Fore.MAGENTA}{', '.join(selected_analysts)}{Style.RESET_ALL}")
print(f"Model: {Fore.GREEN}{model_name}{Style.RESET_ALL}")

# Run the hedge fund
run_hedge_fund(
    tickers=tickers,
    start_date=start_date,
    end_date=end_date,
    portfolio=portfolio,
    show_reasoning=True,
    selected_analysts=selected_analysts,
    model_name=model_name,
)
