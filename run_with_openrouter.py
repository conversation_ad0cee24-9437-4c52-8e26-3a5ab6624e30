"""
<PERSON><PERSON><PERSON> to run the AI Hedge Fund with the Mock Sentiment Analyst using OpenRouter.
"""

import sys
import os
import json
from datetime import datetime
from dateutil.relativedelta import relativedelta
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Check if the OpenRouter API key is set
if not os.environ.get("OPENROUTER_API_KEY"):
    print(f"{Fore.YELLOW}Warning: OPENROUTER_API_KEY environment variable is not set.{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}Please provide your OpenRouter API key using the --api-key parameter.{Style.RESET_ALL}")

# Import necessary functions from the project
from src.main import run_hedge_fund
from src.utils.display import print_trading_output
from src.llm.models import get_model_info

print(f"{Fore.GREEN}Successfully imported AI Hedge Fund functions{Style.RESET_ALL}")

def run_hedge_fund_with_openrouter(ticker, end_date=None, model_name="google/gemini-2.5-pro-exp-03-25:free"):
    """
    Run the AI Hedge Fund with the Mock Sentiment Analyst using OpenRouter.
    """
    # If end_date is not provided, use today's date
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")

    # Set start_date to 3 months before end_date
    end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")
    start_date_dt = end_date_dt - relativedelta(months=3)
    start_date = start_date_dt.strftime("%Y-%m-%d")

    print(f"\nRunning AI Hedge Fund for {Fore.CYAN}{ticker}{Style.RESET_ALL} from {start_date} to {end_date}...")

    # Get model info
    model_info = get_model_info(model_name)
    if model_info:
        model_provider = model_info.provider.value
        print(f"\nSelected {Fore.CYAN}{model_provider}{Style.RESET_ALL} model: {Fore.GREEN + Style.BRIGHT}{model_name}{Style.RESET_ALL}\n")
    else:
        model_provider = "OpenRouter"
        print(f"\nSelected model: {Fore.GREEN + Style.BRIGHT}{model_name}{Style.RESET_ALL}\n")

    # Initialize portfolio
    portfolio = {
        "cash": 100000.0,
        "positions": {ticker: {"shares": 0, "cost_basis": 0.0}},
        "margin_requirement": 0.0,
        "realized_gains": {
            ticker: {
                "long": 0.0,
                "short": 0.0,
            }
        }
    }

    # Run the hedge fund with the Mock Sentiment Analyst
    result = run_hedge_fund(
        tickers=[ticker],
        start_date=start_date,
        end_date=end_date,
        portfolio=portfolio,
        show_reasoning=True,
        selected_analysts=["mock_sentiment_analyst"],
        model_name=model_name,
        model_provider=model_provider,
    )

    # Print the results
    print_trading_output(result)

    return result

if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run AI Hedge Fund with Mock Sentiment Analyst using OpenRouter.')
    parser.add_argument('ticker', type=str, help='Stock ticker symbol (e.g., AMD, AAPL)')
    parser.add_argument('--date', type=str, help='End date for analysis (YYYY-MM-DD format)', default=None)
    parser.add_argument('--model', type=str, help='OpenRouter model to use', default="google/gemini-2.5-pro-exp-03-25:free")
    parser.add_argument('--api-key', type=str, help='OpenRouter API key', default=None)
    args = parser.parse_args()

    # Set the API key if provided
    if args.api_key:
        os.environ["OPENROUTER_API_KEY"] = args.api_key

    # Run the analysis
    run_hedge_fund_with_openrouter(args.ticker, args.date, args.model)
