"""
Script to scrape financial data from screener.in for a given company and save it to CSV.
Uses requests-html to handle JavaScript-rendered content.
"""

from requests_html import HTMLSession
import pandas as pd
import csv
import os
import time
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

def scrape_screener_data(company_url, output_file=None):
    """
    Scrape financial data from screener.in for a given company and save it to CSV.
    
    Args:
        company_url (str): URL of the company page on screener.in
        output_file (str, optional): Path to save the CSV file. If None, uses company name.
    
    Returns:
        dict: Dictionary containing all scraped tables
    """
    print(f"{Fore.CYAN}Scraping data from {company_url}...{Style.RESET_ALL}")
    
    # Create an HTML Session
    session = HTMLSession()
    
    try:
        # Get the page
        response = session.get(company_url)
        
        # Render the page with JavaScript
        response.html.render(sleep=3, timeout=20)
        
        # Extract company name
        company_name = response.html.find('h1.margin-0', first=True).text.strip()
        print(f"{Fore.GREEN}Scraping data for {company_name}{Style.RESET_ALL}")
        
        # If output file is not specified, use company name
        if output_file is None:
            output_file = f"{company_name.replace(' ', '_')}_screener_data.csv"
        
        # Find all tables on the page
        tables = response.html.find('table')
        
        # Dictionary to store all tables
        all_tables = {}
        
        # Process each table
        for i, table in enumerate(tables):
            # Get table title if available
            table_title = None
            
            # Try to find the heading for this table
            for heading in response.html.find(['h2', 'h3', 'h4']):
                # Check if this heading is before the table in the DOM
                if heading.element.sourceline < table.element.sourceline:
                    potential_title = heading.text.strip()
                    # Check if there's no other table between this heading and our table
                    if all(t.element.sourceline < heading.element.sourceline or t.element.sourceline >= table.element.sourceline for t in tables if t != table):
                        table_title = potential_title
            
            if not table_title:
                table_title = f"Table_{i+1}"
            
            # Extract headers
            headers = []
            header_row = table.find('tr', first=True)
            if header_row:
                headers = [th.text.strip() for th in header_row.find(['th', 'td'])]
            
            # Extract rows
            rows = []
            for row in table.find('tr')[1:]:  # Skip header row
                row_data = [td.text.strip() for td in row.find(['td', 'th'])]
                if row_data:  # Only add non-empty rows
                    rows.append(row_data)
            
            # Create DataFrame
            if headers and rows:
                # Make sure all rows have the same length as headers
                rows = [row + [''] * (len(headers) - len(row)) for row in rows if len(row) <= len(headers)]
                rows = [row[:len(headers)] for row in rows if len(row) > len(headers)]
                
                df = pd.DataFrame(rows, columns=headers)
                
                # Check if this table title already exists
                if table_title in all_tables:
                    # Append a number to make it unique
                    j = 1
                    while f"{table_title}_{j}" in all_tables:
                        j += 1
                    table_title = f"{table_title}_{j}"
                
                all_tables[table_title] = df
                
                print(f"{Fore.GREEN}Extracted table: {table_title} with {len(df)} rows and {len(headers)} columns{Style.RESET_ALL}")
        
        try:
            # Save all tables to a single CSV file
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # Write a header row for the CSV
                writer.writerow(['Table', 'Row', 'Column', 'Value'])
                
                # Write each table
                for table_name, df in all_tables.items():
                    # Write table name as a header
                    writer.writerow([table_name, '', '', ''])
                    
                    # Write column headers
                    writer.writerow(['', 'Header'] + list(df.columns))
                    
                    # Write data rows
                    for i, row in df.iterrows():
                        writer.writerow(['', i+1] + list(row))
                    
                    # Add a blank row between tables
                    writer.writerow([])
            
            print(f"{Fore.GREEN}Data saved to {output_file}{Style.RESET_ALL}")
        except PermissionError:
            print(f"{Fore.RED}Permission denied when trying to write to {output_file}. Skipping this file.{Style.RESET_ALL}")
        
        return all_tables
    
    finally:
        # Close the session
        session.close()

def save_tables_to_separate_csvs(all_tables, output_dir=None):
    """
    Save each table to a separate CSV file.
    
    Args:
        all_tables (dict): Dictionary of table name to DataFrame
        output_dir (str, optional): Directory to save CSV files. If None, uses current directory.
    """
    if output_dir is None:
        output_dir = '.'
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Save each table to a separate CSV file
    for table_name, df in all_tables.items():
        # Clean up table name for filename
        filename = f"{table_name.replace(' ', '_').replace('/', '_').replace('\\', '_')}.csv"
        filepath = os.path.join(output_dir, filename)
        
        try:
            # Save to CSV
            df.to_csv(filepath, index=False)
            print(f"{Fore.GREEN}Saved table '{table_name}' to {filepath}{Style.RESET_ALL}")
        except PermissionError:
            print(f"{Fore.RED}Permission denied when trying to write to {filepath}. Skipping this file.{Style.RESET_ALL}")

if __name__ == "__main__":
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Scrape financial data from screener.in')
    parser.add_argument('--url', type=str, default='https://screener.in/company/RELIANCE/consolidated/', 
                        help='URL of the company page on screener.in')
    parser.add_argument('--output', type=str, default=None, 
                        help='Path to save the CSV file')
    parser.add_argument('--separate', action='store_true', 
                        help='Save each table to a separate CSV file')
    parser.add_argument('--output-dir', type=str, default=None, 
                        help='Directory to save separate CSV files')
    
    args = parser.parse_args()
    
    # Scrape data
    all_tables = scrape_screener_data(args.url, args.output)
    
    # Save tables to separate CSV files if requested
    if args.separate and all_tables:
        save_tables_to_separate_csvs(all_tables, args.output_dir)
