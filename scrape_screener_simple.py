"""
Simple script to scrape financial data from screener.in for Reliance Industries.
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import os
import re
import json
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

def scrape_screener_data(company_url, output_dir=None):
    """
    Scrape financial data from screener.in for a given company and save it to CSV.
    
    Args:
        company_url (str): URL of the company page on screener.in
        output_dir (str, optional): Directory to save CSV files. If None, uses current directory.
    """
    print(f"{Fore.CYAN}Scraping data from {company_url}...{Style.RESET_ALL}")
    
    # Send a request to the URL
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.get(company_url, headers=headers)
        response.raise_for_status()  # Raise an exception for HTTP errors
    except requests.exceptions.RequestException as e:
        print(f"{Fore.RED}Error fetching the URL: {e}{Style.RESET_ALL}")
        return None
    
    # Parse the HTML content
    soup = BeautifulSoup(response.text, 'html.parser')
    
    # Extract company name
    company_name = soup.select_one('h1.margin-0').text.strip()
    print(f"{Fore.GREEN}Scraping data for {company_name}{Style.RESET_ALL}")
    
    # Create output directory if it doesn't exist
    if output_dir is None:
        output_dir = f"{company_name.replace(' ', '_')}_data"
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Extract data from script tags
    script_tags = soup.find_all('script')
    
    # Dictionary to store all tables
    all_tables = {}
    
    # Look for JSON data in script tags
    for script in script_tags:
        if script.string and 'var data' in script.string:
            # Extract JSON data
            json_match = re.search(r'var data = ({.*?});', script.string, re.DOTALL)
            if json_match:
                try:
                    data = json.loads(json_match.group(1))
                    print(f"{Fore.GREEN}Found JSON data in script tag{Style.RESET_ALL}")
                    
                    # Process each section in the data
                    for section_name, section_data in data.items():
                        if isinstance(section_data, dict) and 'data' in section_data:
                            # Extract table data
                            table_data = section_data['data']
                            if table_data:
                                # Create DataFrame
                                df = pd.DataFrame(table_data)
                                all_tables[section_name] = df
                                print(f"{Fore.GREEN}Extracted table: {section_name} with {len(df)} rows and {len(df.columns)} columns{Style.RESET_ALL}")
                except json.JSONDecodeError as e:
                    print(f"{Fore.RED}Error decoding JSON: {e}{Style.RESET_ALL}")
    
    # Extract tables from HTML
    tables = soup.find_all('table')
    
    # Process each table
    for i, table in enumerate(tables):
        # Get table title if available
        table_title = None
        prev_element = table.find_previous(['h2', 'h3', 'h4'])
        if prev_element:
            table_title = prev_element.text.strip()
        else:
            table_title = f"Table_{i+1}"
        
        # Extract headers
        headers = []
        header_row = table.find('tr')
        if header_row:
            headers = [th.text.strip() for th in header_row.find_all(['th', 'td'])]
        
        # Extract rows
        rows = []
        for row in table.find_all('tr')[1:]:  # Skip header row
            row_data = [td.text.strip() for td in row.find_all(['td', 'th'])]
            if row_data:  # Only add non-empty rows
                rows.append(row_data)
        
        # Create DataFrame
        if headers and rows:
            # Make sure all rows have the same length as headers
            rows = [row + [''] * (len(headers) - len(row)) for row in rows if len(row) <= len(headers)]
            rows = [row[:len(headers)] for row in rows if len(row) > len(headers)]
            
            df = pd.DataFrame(rows, columns=headers)
            
            # Check if this table title already exists
            if table_title in all_tables:
                # Append a number to make it unique
                j = 1
                while f"{table_title}_{j}" in all_tables:
                    j += 1
                table_title = f"{table_title}_{j}"
            
            all_tables[table_title] = df
            
            print(f"{Fore.GREEN}Extracted table: {table_title} with {len(df)} rows and {len(df.columns)} columns{Style.RESET_ALL}")
    
    # Save each table to a separate CSV file
    for table_name, df in all_tables.items():
        # Clean up table name for filename
        filename = f"{table_name.replace(' ', '_').replace('/', '_').replace('\\', '_')}.csv"
        filepath = os.path.join(output_dir, filename)
        
        try:
            # Save to CSV
            df.to_csv(filepath, index=False)
            print(f"{Fore.GREEN}Saved table '{table_name}' to {filepath}{Style.RESET_ALL}")
        except PermissionError:
            print(f"{Fore.RED}Permission denied when trying to write to {filepath}. Skipping this file.{Style.RESET_ALL}")
    
    # Also try to extract data from the page source directly
    # Look for specific sections like Quarterly Results, Profit & Loss, etc.
    sections = [
        "Quarterly Results",
        "Profit & Loss",
        "Balance Sheet",
        "Cash Flows",
        "Ratios",
        "Shareholding Pattern"
    ]
    
    for section in sections:
        # Find the section heading
        section_heading = soup.find(['h2', 'h3', 'h4'], string=re.compile(section))
        if section_heading:
            print(f"{Fore.GREEN}Found section: {section}{Style.RESET_ALL}")
            
            # Find the table after this heading
            table = section_heading.find_next('table')
            if table:
                # Extract headers
                headers = []
                header_row = table.find('tr')
                if header_row:
                    headers = [th.text.strip() for th in header_row.find_all(['th', 'td'])]
                
                # Extract rows
                rows = []
                for row in table.find_all('tr')[1:]:  # Skip header row
                    row_data = [td.text.strip() for td in row.find_all(['td', 'th'])]
                    if row_data:  # Only add non-empty rows
                        rows.append(row_data)
                
                # Create DataFrame
                if headers and rows:
                    # Make sure all rows have the same length as headers
                    rows = [row + [''] * (len(headers) - len(row)) for row in rows if len(row) <= len(headers)]
                    rows = [row[:len(headers)] for row in rows if len(row) > len(headers)]
                    
                    df = pd.DataFrame(rows, columns=headers)
                    
                    # Save to CSV
                    filename = f"{section.replace(' ', '_').replace('/', '_').replace('\\', '_')}_direct.csv"
                    filepath = os.path.join(output_dir, filename)
                    
                    try:
                        df.to_csv(filepath, index=False)
                        print(f"{Fore.GREEN}Saved section '{section}' to {filepath}{Style.RESET_ALL}")
                    except PermissionError:
                        print(f"{Fore.RED}Permission denied when trying to write to {filepath}. Skipping this file.{Style.RESET_ALL}")
    
    print(f"{Fore.GREEN}Scraping completed. Data saved to {output_dir}{Style.RESET_ALL}")

if __name__ == "__main__":
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Scrape financial data from screener.in')
    parser.add_argument('--url', type=str, default='https://screener.in/company/RELIANCE/consolidated/', 
                        help='URL of the company page on screener.in')
    parser.add_argument('--output-dir', type=str, default=None, 
                        help='Directory to save CSV files')
    
    args = parser.parse_args()
    
    # Scrape data
    scrape_screener_data(args.url, args.output_dir)
