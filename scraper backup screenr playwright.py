"""
Script to scrape financial data from screener.in using <PERSON><PERSON>.
"""

import asyncio
from playwright.async_api import async_playwright
import pandas as pd
import os
import json
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

async def scrape_screener_data(company_url, output_dir=None):
    """
    Scrape financial data from screener.in for a given company and save it to CSV.

    Args:
        company_url (str): URL of the company page on screener.in
        output_dir (str, optional): Directory to save CSV files. If None, uses current directory.
    """
    print(f"{Fore.CYAN}Scraping data from {company_url}...{Style.RESET_ALL}")

    async with async_playwright() as p:
        # Launch the browser
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()

        # Go to the URL
        await page.goto(company_url, wait_until="networkidle")

        # Wait for the content to load
        await page.wait_for_selector('table')

        # Extract company name
        company_name = await page.locator('h1.margin-0').text_content()
        company_name = company_name.strip()
        print(f"{Fore.GREEN}Scraping data for {company_name}{Style.RESET_ALL}")

        # Create output directory if it doesn't exist
        if output_dir is None:
            output_dir = f"{company_name.replace(' ', '_')}_data"

        os.makedirs(output_dir, exist_ok=True)

        # Extract all tables
        tables = await page.locator('table').all()

        # Dictionary to store all tables
        all_tables = {}

        # Process each table
        for i, table in enumerate(tables):
            # Get table title if available
            table_title = None

            # Try to find the heading for this table
            headings = await page.locator('h2, h3, h4').all()
            for heading in headings:
                heading_text = await heading.text_content()
                heading_text = heading_text.strip()

                # Check if this heading is before the table
                heading_box = await heading.bounding_box()
                table_box = await table.bounding_box()

                if heading_box and table_box and heading_box['y'] < table_box['y']:
                    # Check if there's no other table between this heading and our table
                    is_closest = True
                    for other_table in tables:
                        if other_table != table:
                            other_box = await other_table.bounding_box()
                            if other_box and heading_box['y'] < other_box['y'] < table_box['y']:
                                is_closest = False
                                break

                    if is_closest:
                        table_title = heading_text
                        break

            if not table_title:
                table_title = f"Table_{i+1}"

            # Extract table data
            table_data = await extract_table_data(table)

            if table_data and table_data['headers'] and (table_data['rows'] or table_data.get('is_financial_table', False)):
                # Create DataFrame based on table type
                if table_data.get('is_financial_table', False) and 'row_labels' in table_data:
                    # For financial tables, create a properly structured DataFrame
                    # First column becomes the index with row labels
                    # Headers (excluding the first empty/label cell) become columns
                    columns = table_data['headers'][1:] if len(table_data['headers']) > 1 else []

                    # Create DataFrame with row labels as the first column
                    df = pd.DataFrame(table_data['rows'], columns=columns)

                    # Add row labels as the first column
                    df.insert(0, 'Metric', table_data['row_labels'])

                    # Clean up column names (remove special characters)
                    df.columns = [col.replace('Â', '').replace('+', '').strip() for col in df.columns]

                    # Convert numeric columns to appropriate types
                    for col in df.columns[1:]:  # Skip the 'Metric' column
                        try:
                            # Try to convert to numeric, coercing errors to NaN
                            df[col] = pd.to_numeric(df[col].str.replace(',', ''), errors='coerce')
                        except:
                            pass  # Keep as string if conversion fails
                else:
                    # For regular tables, create a standard DataFrame
                    df = pd.DataFrame(table_data['rows'], columns=table_data['headers'])

                # Check if this table title already exists
                if table_title in all_tables:
                    # Append a number to make it unique
                    j = 1
                    while f"{table_title}_{j}" in all_tables:
                        j += 1
                    table_title = f"{table_title}_{j}"

                all_tables[table_title] = df

                print(f"{Fore.GREEN}Extracted table: {table_title} with {len(df)} rows and {len(df.columns)} columns{Style.RESET_ALL}")

                # Save to CSV
                filename = f"{table_title.replace(' ', '_').replace('/', '_').replace('\\', '_')}.csv"
                filepath = os.path.join(output_dir, filename)

                try:
                    df.to_csv(filepath, index=False)
                    print(f"{Fore.GREEN}Saved table '{table_title}' to {filepath}{Style.RESET_ALL}")
                except PermissionError:
                    print(f"{Fore.RED}Permission denied when trying to write to {filepath}. Skipping this file.{Style.RESET_ALL}")

        # Try to extract data from JavaScript
        js_data = await page.evaluate('''() => {
            // Try to find data in the window object
            if (window.data) {
                return window.data;
            }

            // Try to find data in script tags
            const scripts = document.querySelectorAll('script');
            for (const script of scripts) {
                const content = script.textContent;
                if (content && content.includes('var data =')) {
                    const match = content.match(/var data = (\\{.*?\\});/s);
                    if (match && match[1]) {
                        try {
                            return JSON.parse(match[1]);
                        } catch (e) {
                            console.error('Error parsing JSON:', e);
                        }
                    }
                }
            }

            return null;
        }''')

        if js_data:
            print(f"{Fore.GREEN}Found JavaScript data{Style.RESET_ALL}")

            # Save the raw JavaScript data
            js_filepath = os.path.join(output_dir, "js_data.json")
            try:
                with open(js_filepath, 'w', encoding='utf-8') as f:
                    json.dump(js_data, f, indent=2)
                print(f"{Fore.GREEN}Saved JavaScript data to {js_filepath}{Style.RESET_ALL}")
            except PermissionError:
                print(f"{Fore.RED}Permission denied when trying to write to {js_filepath}. Skipping this file.{Style.RESET_ALL}")

            # Process each section in the JavaScript data
            for section_name, section_data in js_data.items():
                if isinstance(section_data, dict) and 'data' in section_data:
                    # Extract table data
                    table_data = section_data['data']
                    if table_data:
                        # Create DataFrame
                        df = pd.DataFrame(table_data)

                        # Save to CSV
                        filename = f"{section_name.replace(' ', '_').replace('/', '_').replace('\\', '_')}_js.csv"
                        filepath = os.path.join(output_dir, filename)

                        try:
                            df.to_csv(filepath, index=False)
                            print(f"{Fore.GREEN}Saved JavaScript section '{section_name}' to {filepath}{Style.RESET_ALL}")
                        except PermissionError:
                            print(f"{Fore.RED}Permission denied when trying to write to {filepath}. Skipping this file.{Style.RESET_ALL}")

        # Close the browser
        await browser.close()

        print(f"{Fore.GREEN}Scraping completed. Data saved to {output_dir}{Style.RESET_ALL}")

async def extract_table_data(table):
    """
    Extract data from a table element.

    Args:
        table: Playwright locator for the table

    Returns:
        dict: Dictionary with headers and rows
    """
    # Get all rows first
    all_rows = await table.locator('tr').all()

    if len(all_rows) < 2:
        return {'headers': [], 'rows': []}

    # Extract headers from the first row
    headers = []
    header_cells = await all_rows[0].locator('th, td').all()
    for cell in header_cells:
        header_text = await cell.text_content()
        headers.append(header_text.strip())

    # Check if headers are actually data (common in financial tables)
    # If the first column is empty or contains a label like 'Sales', 'Revenue', etc.
    # and other columns are dates or years, then the first row might be headers
    # Otherwise, the first row might be data and we need to extract real headers

    first_cell = headers[0] if headers else ''
    date_pattern = any(month in ''.join(headers[1:]) for month in ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'])
    year_pattern = any(str(year) in ''.join(headers[1:]) for year in range(2010, 2025))

    is_financial_table = (first_cell == '' or any(keyword in first_cell for keyword in ['Sales', 'Revenue', 'Profit', 'Assets', 'Liabilities'])) and (date_pattern or year_pattern)

    # If this looks like a financial table with dates/years as columns
    if is_financial_table:
        # The first row contains the real headers (dates/years)
        # The first column of each subsequent row contains the row labels
        row_labels = []
        data_rows = []

        # Process each row after the header row
        for row_idx in range(1, len(all_rows)):
            row_element = all_rows[row_idx]
            cells = await row_element.locator('td, th').all()

            if not cells:
                continue

            # First cell is the row label
            label_cell = await cells[0].text_content()
            row_labels.append(label_cell.strip())

            # Rest of the cells are data
            row_data = []
            for cell_idx in range(1, len(cells)):
                if cell_idx < len(cells):
                    cell_text = await cells[cell_idx].text_content()
                    row_data.append(cell_text.strip())
                else:
                    row_data.append('')  # Padding for missing cells

            # Make sure all rows have the same length
            if len(row_data) < len(headers) - 1:  # -1 because we exclude the empty first header
                row_data.extend([''] * (len(headers) - 1 - len(row_data)))
            elif len(row_data) > len(headers) - 1:
                row_data = row_data[:len(headers) - 1]

            data_rows.append(row_data)

        # Create a proper DataFrame structure
        # First column is row labels, other columns are from the header row
        return {
            'headers': headers,
            'rows': data_rows,
            'row_labels': row_labels,
            'is_financial_table': True
        }

    # For regular tables, extract rows normally
    rows = []
    for row_idx in range(1, len(all_rows)):
        row_element = all_rows[row_idx]
        row_data = []
        cells = await row_element.locator('td, th').all()

        for cell in cells:
            cell_text = await cell.text_content()
            row_data.append(cell_text.strip())

        # Only add non-empty rows
        if row_data:
            # Make sure all rows have the same length as headers
            if len(row_data) < len(headers):
                row_data.extend([''] * (len(headers) - len(row_data)))
            elif len(row_data) > len(headers):
                row_data = row_data[:len(headers)]

            rows.append(row_data)

    return {
        'headers': headers,
        'rows': rows,
        'is_financial_table': False
    }

async def main():
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Scrape financial data from screener.in')
    parser.add_argument('--url', type=str, default='https://screener.in/company/RELIANCE/consolidated/',
                        help='URL of the company page on screener.in')
    parser.add_argument('--output-dir', type=str, default=None,
                        help='Directory to save CSV files')

    args = parser.parse_args()

    # Scrape data
    await scrape_screener_data(args.url, args.output_dir)

if __name__ == "__main__":
    asyncio.run(main())
