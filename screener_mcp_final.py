#!/usr/bin/env python3
"""
Screener.in Financial Ratios MCP Server - Final Version
Extracts comprehensive financial ratios from screener.in in the exact format needed.
"""

import asyncio
import json
import logging
import re
import os
from datetime import datetime
from typing import Any, Dict, List, Optional
import pandas as pd

# Playwright imports
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("screener-mcp-final")

class ScreenerMCPFinal:
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        
    async def connect_to_existing_edge(self, debug_port: int = 9222):
        """Connect to existing Edge browser with debug mode enabled."""
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.connect_over_cdp(f"http://localhost:{debug_port}")
            
            contexts = self.browser.contexts
            if contexts:
                self.context = contexts[0]
            else:
                self.context = await self.browser.new_context()
            
            pages = self.context.pages
            if pages:
                self.page = pages[0]
            else:
                self.page = await self.context.new_page()
            
            logger.info("✅ Successfully connected to existing Edge browser")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to Edge browser: {e}")
            return False
    
    async def navigate_and_extract_ratios(self, symbol: str = "RELIANCE"):
        """Navigate to screener.in and extract all financial ratios."""
        try:
            # Navigate to screener.in
            url = f"https://www.screener.in/company/{symbol}/"
            logger.info(f"🌐 Navigating to {url}")
            await self.page.goto(url, wait_until="networkidle", timeout=30000)
            await asyncio.sleep(2)
            
            # Click Smart Analyze
            logger.info("🔍 Clicking Smart Analyze button...")
            smart_analyze_selectors = [
                "text='Smart Analyze'",
                "button:has-text('Smart Analyze')",
                "a:has-text('Smart Analyze')"
            ]
            
            for selector in smart_analyze_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=5000)
                    if element:
                        await element.scroll_into_view_if_needed()
                        await asyncio.sleep(1)
                        await element.click()
                        logger.info("✅ Successfully clicked Smart Analyze button")
                        await asyncio.sleep(4)  # Wait 4 seconds as specified
                        break
                except:
                    continue
            
            # Scroll down to ratios section
            logger.info("📊 Scrolling to ratios section...")
            await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight / 2)")
            await asyncio.sleep(2)
            
            # Click Ratio Analysis
            logger.info("🔍 Clicking Ratio Analysis button...")
            ratio_analysis_selectors = [
                "text='Ratio Analysis'",
                "button:has-text('Ratio Analysis')",
                "a:has-text('Ratio Analysis')"
            ]
            
            for selector in ratio_analysis_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=5000)
                    if element:
                        await element.scroll_into_view_if_needed()
                        await asyncio.sleep(1)
                        await element.click()
                        logger.info("✅ Successfully clicked Ratio Analysis button")
                        await asyncio.sleep(3)  # Wait for new data to load
                        break
                except:
                    continue
            
            # Extract all tables
            logger.info("📈 Extracting ratio tables...")
            await asyncio.sleep(3)
            
            tables = await self.page.query_selector_all("table")
            extracted_data = {
                "tables": [],
                "metadata": {
                    "url": self.page.url,
                    "title": await self.page.title(),
                    "extraction_time": datetime.now().isoformat(),
                    "total_tables": len(tables)
                }
            }
            
            logger.info(f"🔍 Found {len(tables)} tables on the page")
            
            for i, table in enumerate(tables):
                try:
                    table_data = await self.extract_table_data(table)
                    if table_data and len(table_data) > 1:
                        extracted_data["tables"].append({
                            "table_index": i,
                            "data": table_data,
                            "headers": table_data[0] if table_data else [],
                            "row_count": len(table_data) - 1
                        })
                except Exception as e:
                    logger.warning(f"  ⚠️ Table {i}: Failed to extract - {e}")
            
            logger.info(f"✅ Successfully extracted {len(extracted_data['tables'])} tables")
            return extracted_data
            
        except Exception as e:
            logger.error(f"❌ Failed to navigate and extract ratios: {e}")
            return None
    
    async def extract_table_data(self, table):
        """Extract data from a single table element."""
        try:
            rows = await table.query_selector_all("tr")
            if not rows:
                return None
            
            table_data = []
            for row in rows:
                cells = await row.query_selector_all("th, td")
                row_data = []
                for cell in cells:
                    cell_text = await cell.inner_text()
                    row_data.append(cell_text.strip())
                
                if row_data and any(row_data):
                    table_data.append(row_data)
            
            return table_data if len(table_data) > 1 else None
            
        except Exception as e:
            return None
    
    def process_ratio_tables(self, extracted_data: Dict, symbol: str = "RELIANCE") -> Dict[str, Any]:
        """Process extracted tables and convert to AAPL-compatible format."""
        
        # Find the specific ratio tables
        ratio_tables = {
            "leverage_ratios": None,
            "efficiency_ratios": None,
            "profitability_ratios": None,
            "capital_allocation_ratios": None,
            "valuation_ratios": None
        }
        
        for table_info in extracted_data.get("tables", []):
            headers = table_info.get("headers", [])
            if not headers:
                continue
            
            first_header = str(headers[0]).lower()
            
            if "leverage" in first_header:
                ratio_tables["leverage_ratios"] = table_info
            elif "efficiency" in first_header:
                ratio_tables["efficiency_ratios"] = table_info
            elif "profitability" in first_header:
                ratio_tables["profitability_ratios"] = table_info
            elif "capital allocation" in first_header:
                ratio_tables["capital_allocation_ratios"] = table_info
            elif "valuation" in first_header:
                ratio_tables["valuation_ratios"] = table_info
        
        # Convert to AAPL format
        converted_data = []
        
        # Comprehensive ratio mapping
        ratio_mapping = {
            # Leverage Ratios
            "debt/equity": "debt_to_equity",
            "debt/assets": "debt_to_assets",
            "debt/ebitda": "debt_to_ebitda",
            "debt/capital ratio": "debt_to_capital_ratio",
            "cash flow/debt": "cash_flow_to_debt",
            "interest coverage ratio": "interest_coverage",
            "financial leverage": "financial_leverage",
            "debt burden ratio": "debt_burden_ratio",
            
            # Efficiency Ratios
            "receivable days": "days_sales_outstanding",
            "receivable turnover": "receivables_turnover",
            "inventory days": "inventory_days",
            "inventory turnover": "inventory_turnover",
            "net fixed assets turnover": "fixed_asset_turnover",
            "sales/capital employed": "sales_to_capital_employed",
            "total asset turnover": "asset_turnover",
            
            # Profitability Ratios
            "ebitda": "ebitda",
            "ebitda margin": "ebitda_margin",
            "gross profit": "gross_profit",
            "gross profit margin": "gross_margin",
            "ebit": "ebit",
            "ebit margin": "ebit_margin",
            "net profit margin": "net_margin",
            "eps": "earnings_per_share",
            "eps growth": "earnings_per_share_growth",
            "roe": "return_on_equity",
            "net profit margin": "net_margin",
            "asset turnover": "asset_turnover",
            "financial leverage": "financial_leverage",
            "roa": "return_on_assets",
            
            # Capital Allocation Ratios
            "roce": "return_on_invested_capital",
            "ebit margin": "ebit_margin",
            "sales/cap employed": "sales_to_capital_employed",
            "nopat": "nopat",
            "capital employed": "capital_employed",
            "roic": "return_on_invested_capital",
            "roiic": "roiic",
            
            # Valuation Ratios
            "price/earnings": "price_to_earnings_ratio",
            "price/book": "price_to_book_ratio",
            "price/cashflow": "price_to_cash_flow_ratio",
            "price/sales": "price_to_sales_ratio",
            "enterprise value": "enterprise_value",
            "ev/ebitda": "enterprise_value_to_ebitda_ratio",
        }
        
        # Process each ratio table
        for category, table_info in ratio_tables.items():
            if not table_info:
                continue
            
            table_data = table_info.get("data", [])
            if len(table_data) < 2:
                continue
            
            headers = table_data[0]
            
            # Find year columns
            year_columns = []
            for i, header in enumerate(headers):
                if re.search(r'Mar 20\d{2}', str(header)):
                    year_columns.append((i, header))
            
            if not year_columns:
                continue
            
            logger.info(f"  📊 Processing {category}: {len(year_columns)} years, {len(table_data)-1} ratios")
            
            # Process each ratio row
            for row in table_data[1:]:
                if not row or len(row) == 0:
                    continue
                
                ratio_name = row[0].lower().strip()
                
                # Find matching AAPL format name
                aapl_name = None
                for screener_key, aapl_key in ratio_mapping.items():
                    if screener_key in ratio_name:
                        aapl_name = aapl_key
                        break
                
                if not aapl_name:
                    # Use original name if no mapping found
                    aapl_name = ratio_name.replace("/", "_to_").replace(" ", "_").lower()
                
                # Create records for each year
                for col_index, year_header in year_columns:
                    if col_index < len(row):
                        value = self.clean_numeric_value(row[col_index])
                        
                        # Find or create record for this year
                        year_record = None
                        for record in converted_data:
                            if record.get("report_period") == year_header:
                                year_record = record
                                break
                        
                        if not year_record:
                            year_record = {
                                "ticker": f"{symbol}.NS",
                                "report_period": year_header,
                                "fiscal_period": year_header,
                                "period": "annual",
                                "currency": "INR"
                            }
                            converted_data.append(year_record)
                        
                        year_record[aapl_name] = value
        
        # Sort by year (most recent first)
        converted_data.sort(key=lambda x: self.extract_year_from_period(x.get("report_period", "")), reverse=True)
        
        return {
            "status": "success",
            "symbol": symbol,
            "total_periods": len(converted_data),
            "ratio_categories": list(ratio_tables.keys()),
            "converted_data": converted_data,
            "raw_ratio_tables": ratio_tables
        }
    
    def extract_year_from_period(self, period_str: str) -> int:
        """Extract year from period string for sorting."""
        try:
            match = re.search(r'20\d{2}', str(period_str))
            return int(match.group()) if match else 0
        except:
            return 0
    
    def clean_numeric_value(self, value_str: str) -> float:
        """Clean and convert string values to numeric."""
        if not value_str or value_str in ['-', 'N/A', 'NA', '', 'nil', 'Nil', '--']:
            return 0.0
        
        try:
            cleaned = str(value_str).strip()
            is_percentage = '%' in cleaned
            
            # Remove common characters
            cleaned = re.sub(r'[,%₹$\s]', '', cleaned)
            
            # Handle negative values in parentheses
            if '(' in cleaned and ')' in cleaned:
                cleaned = '-' + cleaned.replace('(', '').replace(')', '')
            
            # Handle multipliers
            multiplier = 1
            if 'cr' in cleaned.lower() or 'crore' in cleaned.lower():
                cleaned = re.sub(r'(cr|crore)', '', cleaned, flags=re.IGNORECASE)
                multiplier = 10000000
            elif 'l' in cleaned.lower() or 'lakh' in cleaned.lower():
                cleaned = re.sub(r'(l|lakh)', '', cleaned, flags=re.IGNORECASE)
                multiplier = 100000
            
            # Remove any remaining non-numeric characters
            cleaned = re.sub(r'[^0-9.-]', '', cleaned)
            
            if not cleaned or cleaned in ['-', '.']:
                return 0.0
            
            numeric_value = float(cleaned) * multiplier
            
            # Convert percentages to decimals
            if is_percentage:
                numeric_value = numeric_value / 100
            
            return numeric_value
            
        except:
            return 0.0
    
    def save_data(self, data: Any, filename: str) -> Optional[str]:
        """Save data to file."""
        try:
            output_dir = "RELIANCE_NS_screener_data"
            os.makedirs(output_dir, exist_ok=True)
            
            filepath = os.path.join(output_dir, filename)
            
            if filename.endswith('.json'):
                with open(filepath, 'w') as f:
                    json.dump(data, f, indent=2, default=str)
            elif filename.endswith('.csv') and isinstance(data, list):
                df = pd.DataFrame(data)
                df.to_csv(filepath, index=False)
            
            logger.info(f"💾 Saved data to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"❌ Failed to save data: {e}")
            return None
    
    async def close(self):
        """Close browser connection."""
        if self.playwright:
            await self.playwright.stop()

# Main MCP function
async def extract_screener_financial_ratios(symbol: str = "RELIANCE", debug_port: int = 9222) -> Dict[str, Any]:
    """Main MCP function to extract comprehensive financial ratios from screener.in."""
    
    mcp = ScreenerMCPFinal()
    
    try:
        # Connect to existing Edge browser
        if not await mcp.connect_to_existing_edge(debug_port):
            return {"error": "Failed to connect to Edge browser. Make sure Edge is running with debug mode."}
        
        # Navigate and extract ratios
        extracted_data = await mcp.navigate_and_extract_ratios(symbol)
        if not extracted_data:
            return {"error": "Failed to extract ratio data from screener.in"}
        
        # Process ratio tables
        processed_data = mcp.process_ratio_tables(extracted_data, symbol)
        
        # Save data
        mcp.save_data(extracted_data, "raw_extracted_data.json")
        mcp.save_data(processed_data["converted_data"], f"financial_metrics_{symbol}_FINAL.csv")
        mcp.save_data(processed_data, f"processed_ratios_{symbol}_FINAL.json")
        
        return processed_data
        
    except Exception as e:
        logger.error(f"❌ Error in extract_screener_financial_ratios: {e}")
        return {"error": str(e)}
    
    finally:
        await mcp.close()

# CLI interface for testing
async def main():
    """Main CLI function."""
    print("=" * 80)
    print("SCREENER.IN FINANCIAL RATIOS MCP - FINAL VERSION")
    print("=" * 80)
    
    symbol = input("Enter stock symbol (default: RELIANCE): ").strip() or "RELIANCE"
    
    print(f"\n🎯 Extracting comprehensive financial ratios for {symbol}...")
    print("📋 Make sure Edge is running with debug mode")
    
    result = await extract_screener_financial_ratios(symbol)
    
    if "error" in result:
        print(f"❌ Error: {result['error']}")
    else:
        print(f"✅ Success!")
        print(f"📊 Categories: {', '.join(result['ratio_categories'])}")
        print(f"📈 Generated {result['total_periods']} periods of data")
        print(f"💾 Data saved to RELIANCE_NS_screener_data/")
        
        # Show sample data
        if result['converted_data']:
            latest = result['converted_data'][0]
            print(f"\n📋 Sample ratios for {latest['report_period']}:")
            sample_ratios = [(k, v) for k, v in latest.items() if k not in ['ticker', 'report_period', 'fiscal_period', 'period', 'currency']][:5]
            for key, value in sample_ratios:
                print(f"  {key}: {value}")

if __name__ == "__main__":
    asyncio.run(main())
