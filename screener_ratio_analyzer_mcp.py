#!/usr/bin/env python3
"""
Screener.in Ratio Analysis MCP Server
Connects to existing Microsoft Edge browser to extract comprehensive financial ratios.
Supports Smart Analyze > Ratio Analysis for any stock with 10-year historical data.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional
import pandas as pd
from datetime import datetime
import re
import os

# MCP imports
from mcp.server.models import InitializationOptions
from mcp.server import NotificationOptions, Server
from mcp.types import Resource, Tool, TextContent, ImageContent, EmbeddedResource
from mcp.server.stdio import stdio_server

# Playwright imports for browser automation
from playwright.async_api import async_playwright, Browser, Page
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("screener-ratio-analyzer-mcp")

class ScreenerRatioAnalyzer:
    def __init__(self):
        self.browser = None
        self.page = None
        self.playwright = None
    
    async def connect_to_existing_edge(self, port: int = 9222):
        """Connect to existing Microsoft Edge browser using CDP."""
        try:
            self.playwright = await async_playwright().start()
            
            # Connect to existing Edge browser on debug port
            self.browser = await self.playwright.chromium.connect_over_cdp(f"http://localhost:{port}")
            
            # Get the existing context with user profile
            contexts = self.browser.contexts
            if contexts:
                # Use the first context (your existing profile)
                context = contexts[0]
                logger.info("Using existing browser context with your profile")
            else:
                # Create new context (this would use default profile)
                context = await self.browser.new_context()
                logger.info("Created new browser context")
            
            # Get existing page or create new one in existing context
            pages = context.pages
            if pages:
                # Use existing page
                self.page = pages[0]
                logger.info(f"Using existing page: {await self.page.title()}")
            else:
                # Create new page in existing context (preserves profile)
                self.page = await context.new_page()
                logger.info("Created new page in existing context")
            
            logger.info("Successfully connected to existing Edge browser with your profile")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to existing Edge browser: {e}")
            logger.error("Make sure Edge is running with --remote-debugging-port=9222")
            return False
    
    async def launch_new_edge(self):
        """This method is deprecated - we only connect to existing Edge."""
        logger.error("Cannot launch new Edge instance - this would lose your profile and extensions")
        logger.error("Please start your existing Edge browser with debugging enabled:")
        logger.error("1. Open Command Prompt as Administrator")        logger.error("2. Close all Edge windows first")
        logger.error("3. Run: \"C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe\" --remote-debugging-port=9222 --user-data-dir=\"%LOCALAPPDATA%\\Microsoft\\Edge\\User Data\"")
        return False

    async def navigate_to_ratio_analysis(self, stock_symbol: str):
        """Navigate to ratio analysis following the exact sequence: Smart Analysis > Wait > Scroll > Ratio Analysis."""
        try:
            # Step 1: Navigate to company page
            url = f"https://www.screener.in/company/{stock_symbol}/"
            logger.info(f"🌐 Navigating to: {url}")
            await self.page.goto(url, wait_until="networkidle")
            logger.info("📄 Page loaded successfully")
            
            # Step 2: Click Smart Analysis button (enhanced detection)
            logger.info("🔍 Looking for Smart Analysis button...")
            
            # Wait for page to fully load and JavaScript to initialize
            await asyncio.sleep(3)
            
            # Try multiple approaches to find Smart Analysis
            smart_analysis_clicked = False
            
            # Approach 1: Look for exact "Smart Analysis" text
            logger.info("🎯 Approach 1: Looking for exact 'Smart Analysis' text...")
            try:
                # Wait for the element to be present
                smart_btn = await self.page.wait_for_selector('text="Smart Analysis"', timeout=5000)
                if smart_btn and await smart_btn.is_visible():
                    await smart_btn.click()
                    logger.info("✅ Clicked Smart Analysis using exact text match")
                    smart_analysis_clicked = True
            except Exception as e:
                logger.debug(f"Exact text match failed: {e}")
            
            # Approach 2: Try common button patterns
            if not smart_analysis_clicked:
                logger.info("🎯 Approach 2: Trying button patterns...")
                smart_analysis_selectors = [
                    'button:has-text("Smart Analysis")',
                    'a:has-text("Smart Analysis")', 
                    '.btn:has-text("Smart Analysis")',
                    '[data-bs-target*="analysis"]',
                    '[data-target*="analysis"]',
                    'button[onclick*="analysis"]',
                    '.smart-analysis',
                    '#smart-analysis',
                    '.analysis-btn'
                ]
                
                for selector in smart_analysis_selectors:
                    try:
                        element = await self.page.wait_for_selector(selector, timeout=2000)
                        if element and await element.is_visible():
                            await element.click()
                            logger.info(f"✅ Clicked Smart Analysis using selector: {selector}")
                            smart_analysis_clicked = True
                            break
                    except Exception as e:
                        logger.debug(f"Selector {selector} failed: {e}")
            
            # Approach 3: Search all clickable elements
            if not smart_analysis_clicked:
                logger.info("🎯 Approach 3: Searching all clickable elements...")
                clickable_elements = await self.page.query_selector_all('a, button, .btn, [onclick], [data-bs-target], [data-target]')
                
                for element in clickable_elements:
                    try:
                        text = await element.inner_text()
                        if text and ('smart' in text.lower() and 'analysis' in text.lower()) or 'analysis' in text.lower():
                            is_visible = await element.is_visible()
                            if is_visible and len(text.strip()) < 50:
                                logger.info(f"🎯 Found potential Analysis button: '{text.strip()}'")
                                await element.click()
                                logger.info(f"✅ Clicked Analysis button with text: '{text.strip()}'")
                                smart_analysis_clicked = True
                                break
                    except Exception as e:
                        continue
            
            # Approach 4: Try JavaScript click
            if not smart_analysis_clicked:
                logger.info("🎯 Approach 4: Trying JavaScript execution...")
                try:
                    # Look for elements that might trigger analysis
                    js_code = """
                    // Look for buttons or links that might be the Smart Analysis button
                    let analysisButtons = [];
                    let allElements = document.querySelectorAll('a, button, .btn, [onclick]');
                    
                    for (let elem of allElements) {
                        let text = elem.innerText || elem.textContent || '';
                        if (text.toLowerCase().includes('analysis') || text.toLowerCase().includes('smart')) {
                            analysisButtons.push({
                                element: elem,
                                text: text.trim(),
                                tag: elem.tagName,
                                classes: elem.className
                            });
                        }
                    }
                    
                    // Try to click the most likely candidate
                    if (analysisButtons.length > 0) {
                        let bestMatch = analysisButtons.find(btn => 
                            btn.text.toLowerCase().includes('smart analysis') ||
                            btn.text.toLowerCase() === 'analysis'
                        ) || analysisButtons[0];
                        
                        bestMatch.element.click();
                        return bestMatch.text;
                    }
                    return null;
                    """
                    
                    clicked_text = await self.page.evaluate(js_code)
                    if clicked_text:
                        logger.info(f"✅ JavaScript clicked element with text: '{clicked_text}'")
                        smart_analysis_clicked = True
                except Exception as e:
                    logger.debug(f"JavaScript approach failed: {e}")
            
            if not smart_analysis_clicked:
                logger.warning("⚠️ Could not find Smart Analysis button, proceeding to look for ratios...")
                # Continue anyway - sometimes the ratios are already visible            
            # Step 3: Wait exactly 4 seconds as requested
            logger.info("⏳ Waiting exactly 4 seconds for content to load...")
            await asyncio.sleep(4)
            
            # Step 4: Scroll down to find ratios section
            logger.info("� Scrolling down to find ratios section...")
            
            # Multiple scroll approaches
            scroll_attempts = [
                "window.scrollTo(0, document.body.scrollHeight / 3)",  # Scroll 1/3 down
                "window.scrollTo(0, document.body.scrollHeight / 2)",  # Scroll 1/2 down  
                "window.scrollTo(0, document.body.scrollHeight * 0.7)"  # Scroll 70% down
            ]
            
            for i, scroll_js in enumerate(scroll_attempts):
                await self.page.evaluate(scroll_js)
                await asyncio.sleep(1)
                
                # Check if ratio section is visible after each scroll
                ratio_section_visible = await self.page.evaluate("""
                    () => {
                        let ratioElements = document.querySelectorAll('*');
                        for (let elem of ratioElements) {
                            let text = elem.textContent || '';
                            if (text.toLowerCase().includes('ratio') && 
                                (text.toLowerCase().includes('leverage') || 
                                 text.toLowerCase().includes('efficiency') || 
                                 text.toLowerCase().includes('profitability'))) {
                                return true;
                            }
                        }
                        return false;
                    }
                """)
                
                if ratio_section_visible:
                    logger.info(f"✅ Found ratio section after scroll attempt {i+1}")
                    break
            
            # Step 5: Enhanced Ratio Analysis button detection
            logger.info("🔍 Looking for Ratio Analysis button...")
            
            ratio_analysis_clicked = False
            
            # Approach 1: Direct text search
            try:
                ratio_btn = await self.page.wait_for_selector('text="Ratio Analysis"', timeout=3000)
                if ratio_btn and await ratio_btn.is_visible():
                    await ratio_btn.click()
                    logger.info("✅ Clicked Ratio Analysis using exact text match")
                    ratio_analysis_clicked = True
            except:
                pass
            
            # Approach 2: Multiple selectors
            if not ratio_analysis_clicked:
                ratio_analysis_selectors = [
                    'button:has-text("Ratio Analysis")',
                    'a:has-text("Ratio Analysis")',
                    '.btn:has-text("Ratio")',
                    'button:has-text("Ratio")',
                    'a:has-text("Ratio")',
                    '[data-bs-target*="ratio"]',
                    '[data-target*="ratio"]',
                    'button[onclick*="ratio"]',
                    '.ratio-analysis',
                    '#ratio-analysis'
                ]
                
                for selector in ratio_analysis_selectors:
                    try:
                        element = await self.page.wait_for_selector(selector, timeout=2000)
                        if element and await element.is_visible():
                            await element.click()
                            logger.info(f"✅ Clicked Ratio Analysis using selector: {selector}")
                            ratio_analysis_clicked = True
                            break
                    except Exception as e:
                        logger.debug(f"Selector {selector} failed: {e}")
            
            # Approach 3: Search all elements for ratio-related text
            if not ratio_analysis_clicked:
                logger.info("🎯 Searching for ratio-related clickable elements...")
                all_clickable = await self.page.query_selector_all('a, button, .btn, [onclick]')
                
                for element in all_clickable:
                    try:
                        text = await element.inner_text()
                        if text and 'ratio' in text.lower() and len(text.strip()) < 50:
                            is_visible = await element.is_visible()
                            if is_visible:
                                logger.info(f"🎯 Found ratio element: '{text.strip()}'")
                                await element.click()
                                logger.info(f"✅ Clicked ratio element with text: '{text.strip()}'")
                                ratio_analysis_clicked = True
                                break
                    except:
                        continue
            
            # Approach 4: JavaScript-based clicking
            if not ratio_analysis_clicked:
                logger.info("🎯 Trying JavaScript-based ratio button detection...")
                try:
                    js_result = await self.page.evaluate("""
                        () => {
                            let elements = document.querySelectorAll('a, button, .btn, [onclick]');
                            for (let elem of elements) {
                                let text = elem.textContent || elem.innerText || '';
                                if (text.toLowerCase().includes('ratio')) {
                                    elem.click();
                                    return text.trim();
                                }
                            }
                            return null;
                        }
                    """)
                    
                    if js_result:
                        logger.info(f"✅ JavaScript clicked ratio element: '{js_result}'")
                        ratio_analysis_clicked = True
                except Exception as e:
                    logger.debug(f"JavaScript ratio detection failed: {e}")
            
            if not ratio_analysis_clicked:
                logger.warning("⚠️ Could not find Ratio Analysis button, but proceeding to extract visible data...")
            
            # Step 6: Wait for new data to load
            logger.info("⏳ Waiting for ratio data to load...")
            await asyncio.sleep(3)
            
            logger.info("🎉 Navigation sequence completed!")
            return True
            
        except Exception as e:
            logger.error(f"Failed to navigate to ratio analysis: {e}")
            return False
      async def extract_financial_ratios(self, stock_symbol: str) -> Dict[str, Any]:
        """Extract all financial ratios from the newly loaded ratio analysis tables."""
        try:
            ratios_data = {
                "stock_symbol": stock_symbol,
                "extraction_timestamp": datetime.now().isoformat(),
                "leverage_ratios": {},
                "efficiency_ratios": {},
                "profitability_ratios": {},
                "capital_allocation_ratios": {},
                "valuation_ratios": {},
                "other_ratios": {},
                "yearly_data": {}
            }
            
            # Wait for tables to load
            await asyncio.sleep(3)
            
            logger.info("📊 Starting comprehensive ratio extraction...")
            
            # Method 1: Extract structured tables
            tables = await self.page.query_selector_all('table')
            logger.info(f"� Found {len(tables)} tables on the page")
            
            for i, table in enumerate(tables):
                try:
                    # Get table content
                    table_html = await table.inner_html()
                    
                    # Check if this table contains ratio data
                    table_text = await table.inner_text()
                    if not any(keyword in table_text.lower() for keyword in ['ratio', 'leverage', 'efficiency', 'profitability', 'valuation', 'roe', 'roa', 'debt']):
                        continue
                    
                    # Parse with pandas
                    df = pd.read_html(f"<table>{table_html}</table>")[0]
                    
                    # Determine table type by content
                    table_type = "other_ratios"
                    if "leverage" in table_text.lower() or "debt" in table_text.lower():
                        table_type = "leverage_ratios"
                    elif "efficiency" in table_text.lower() or "turnover" in table_text.lower():
                        table_type = "efficiency_ratios"
                    elif "profitability" in table_text.lower() or "margin" in table_text.lower() or "roe" in table_text.lower():
                        table_type = "profitability_ratios"
                    elif "valuation" in table_text.lower() or "pe" in table_text.lower() or "price" in table_text.lower():
                        table_type = "valuation_ratios"
                    elif "capital" in table_text.lower():
                        table_type = "capital_allocation_ratios"
                    
                    # Process different table structures
                    if len(df.columns) >= 2:
                        # Handle time-series data (metrics over years)
                        if len(df.columns) > 2:  # Multi-year data
                            for _, row in df.iterrows():
                                if pd.notna(row.iloc[0]):
                                    metric_name = str(row.iloc[0]).strip()
                                    if metric_name and metric_name != 'Metric' and not metric_name.startswith('Year'):
                                        yearly_values = {}
                                        for col_idx in range(1, len(df.columns)):
                                            year_col = df.columns[col_idx]
                                            if pd.notna(row.iloc[col_idx]):
                                                yearly_values[str(year_col)] = str(row.iloc[col_idx]).strip()
                                        
                                        if yearly_values:
                                            ratios_data[table_type][metric_name] = yearly_values
                                            
                                            # Also store in yearly_data structure
                                            for year, value in yearly_values.items():
                                                if year not in ratios_data["yearly_data"]:
                                                    ratios_data["yearly_data"][year] = {}
                                                ratios_data["yearly_data"][year][metric_name] = value
                        
                        else:  # Simple key-value pairs
                            df.columns = ['Metric', 'Value']
                            for _, row in df.iterrows():
                                if pd.notna(row['Metric']) and pd.notna(row['Value']):
                                    metric = str(row['Metric']).strip()
                                    value = str(row['Value']).strip()
                                    if metric and value and metric != 'Metric':
                                        ratios_data[table_type][metric] = value
                    
                    logger.info(f"✅ Processed table {i+1} as {table_type}")
                
                except Exception as e:
                    logger.debug(f"Could not parse table {i+1}: {e}")
                    continue
            
            # Method 2: Extract from cards/sections
            logger.info("🔍 Looking for ratio cards and sections...")
            
            # Look for specific ratio sections
            ratio_sections = await self.page.query_selector_all('.card, .section, .ratio-section, [class*="ratio"], [class*="metric"]')
            
            for section in ratio_sections:
                try:
                    section_text = await section.inner_text()
                    if len(section_text) > 500:  # Skip large sections
                        continue
                    
                    # Look for ratio patterns like "ROE: 15.2%" or "Debt to Equity: 0.45"
                    import re
                    ratio_pattern = r'([A-Za-z\s/]+):\s*([0-9.,%-]+)'
                    matches = re.findall(ratio_pattern, section_text)
                    
                    for match in matches:
                        metric_name = match[0].strip()
                        metric_value = match[1].strip()
                        
                        # Categorize the metric
                        metric_lower = metric_name.lower()
                        if any(word in metric_lower for word in ['debt', 'leverage', 'interest']):
                            ratios_data["leverage_ratios"][metric_name] = metric_value
                        elif any(word in metric_lower for word in ['turnover', 'efficiency', 'days', 'working']):
                            ratios_data["efficiency_ratios"][metric_name] = metric_value
                        elif any(word in metric_lower for word in ['margin', 'roe', 'roa', 'roic', 'profit']):
                            ratios_data["profitability_ratios"][metric_name] = metric_value
                        elif any(word in metric_lower for word in ['pe', 'pb', 'price', 'valuation', 'ev']):
                            ratios_data["valuation_ratios"][metric_name] = metric_value
                        elif any(word in metric_lower for word in ['capital', 'dividend', 'payout']):
                            ratios_data["capital_allocation_ratios"][metric_name] = metric_value
                        else:
                            ratios_data["other_ratios"][metric_name] = metric_value
                
                except Exception as e:
                    continue
            
            # Method 3: JavaScript-based extraction for dynamically loaded content
            logger.info("🔍 Running JavaScript-based extraction...")
            
            try:
                js_ratios = await self.page.evaluate("""
                    () => {
                        let ratios = {
                            leverage: {},
                            efficiency: {},
                            profitability: {},
                            valuation: {},
                            capital: {},
                            other: {}
                        };
                        
                        // Look for all text content that might contain ratios
                        let allElements = document.querySelectorAll('*');
                        
                        for (let elem of allElements) {
                            let text = elem.textContent || '';
                            
                            // Skip large elements
                            if (text.length > 200) continue;
                            
                            // Look for patterns like "ROE 15.2%" or "Debt/Equity: 0.45"
                            let ratioMatches = text.match(/([A-Za-z\s\/]+)\s*:?\s*([0-9.,%-]+)/g);
                            
                            if (ratioMatches) {
                                for (let match of ratioMatches) {
                                    let parts = match.split(/[:]/);
                                    if (parts.length === 2) {
                                        let metric = parts[0].trim();
                                        let value = parts[1].trim();
                                        
                                        let metricLower = metric.toLowerCase();
                                        
                                        if (metricLower.includes('debt') || metricLower.includes('leverage')) {
                                            ratios.leverage[metric] = value;
                                        } else if (metricLower.includes('turnover') || metricLower.includes('efficiency')) {
                                            ratios.efficiency[metric] = value;
                                        } else if (metricLower.includes('roe') || metricLower.includes('margin') || metricLower.includes('profit')) {
                                            ratios.profitability[metric] = value;
                                        } else if (metricLower.includes('pe') || metricLower.includes('price') || metricLower.includes('valuation')) {
                                            ratios.valuation[metric] = value;
                                        } else if (metricLower.includes('capital') || metricLower.includes('dividend')) {
                                            ratios.capital[metric] = value;
                                        } else {
                                            ratios.other[metric] = value;
                                        }
                                    }
                                }
                            }
                        }
                        
                        return ratios;
                    }
                """)
                
                # Merge JavaScript results
                for category, js_ratios_cat in js_ratios.items():
                    if js_ratios_cat:
                        category_key = f"{category}_ratios" if category != "other" else "other_ratios"
                        if category_key in ratios_data:
                            ratios_data[category_key].update(js_ratios_cat)
                
                logger.info("✅ JavaScript extraction completed")
                
            except Exception as e:
                logger.debug(f"JavaScript extraction failed: {e}")
            
            # Calculate total extracted ratios
            total_ratios = sum(len(category_data) for category_data in ratios_data.values() 
                             if isinstance(category_data, dict) and category_data)
            
            logger.info(f"🎯 Total ratios extracted: {total_ratios}")
            logger.info(f"📊 Leverage ratios: {len(ratios_data['leverage_ratios'])}")
            logger.info(f"📊 Efficiency ratios: {len(ratios_data['efficiency_ratios'])}")
            logger.info(f"📊 Profitability ratios: {len(ratios_data['profitability_ratios'])}")
            logger.info(f"📊 Valuation ratios: {len(ratios_data['valuation_ratios'])}")
            logger.info(f"📊 Capital allocation ratios: {len(ratios_data['capital_allocation_ratios'])}")
            logger.info(f"📊 Years of data: {len(ratios_data['yearly_data'])}")
            
            return ratios_data
            
        except Exception as e:
            logger.error(f"Failed to extract financial ratios: {e}")
            return {"error": str(e), "stock_symbol": stock_symbol}
    
    async def cleanup(self):
        """Clean up browser resources."""
        try:
            if self.page:
                await self.page.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            logger.info("🧹 Cleaned up browser resources")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# MCP Server Setup
app = Server("screener-ratio-analyzer")

@app.list_tools()
async def handle_list_tools() -> List[Tool]:
    """List available tools."""
    return [
        Tool(
            name="extract_financial_ratios",
            description="Extract comprehensive financial ratios from screener.in using Smart Analysis > Ratio Analysis workflow",
            inputSchema={
                "type": "object",
                "properties": {
                    "stock_symbol": {
                        "type": "string",
                        "description": "Stock symbol (e.g., RELIANCE, TCS, INFY)"
                    }
                },
                "required": ["stock_symbol"]
            }
        ),
        Tool(
            name="navigate_to_ratio_analysis",
            description="Navigate to ratio analysis page following exact sequence: Smart Analysis > Wait 4s > Scroll > Ratio Analysis",
            inputSchema={
                "type": "object",
                "properties": {
                    "stock_symbol": {
                        "type": "string",
                        "description": "Stock symbol to navigate to"
                    }
                },
                "required": ["stock_symbol"]
            }
        )
    ]

@app.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> List[TextContent]:
    """Handle tool calls."""
    analyzer = ScreenerRatioAnalyzer()
    
    try:
        # Connect to existing Edge browser
        connected = await analyzer.connect_to_existing_edge()
        if not connected:
            return [TextContent(
                type="text",
                text="❌ Could not connect to Edge browser. Please ensure Edge is running with --remote-debugging-port=9222"
            )]
        
        if name == "navigate_to_ratio_analysis":
            stock_symbol = arguments.get("stock_symbol", "").upper()
            if not stock_symbol:
                return [TextContent(type="text", text="❌ Stock symbol is required")]
            
            success = await analyzer.navigate_to_ratio_analysis(stock_symbol)
            
            if success:
                return [TextContent(
                    type="text",
                    text=f"✅ Successfully navigated to ratio analysis for {stock_symbol} using the exact sequence: Smart Analysis > Wait 4s > Scroll > Ratio Analysis"
                )]
            else:
                return [TextContent(
                    type="text",
                    text=f"❌ Failed to navigate to ratio analysis for {stock_symbol}"
                )]
        
        elif name == "extract_financial_ratios":
            stock_symbol = arguments.get("stock_symbol", "").upper()
            if not stock_symbol:
                return [TextContent(type="text", text="❌ Stock symbol is required")]
            
            # Navigate first
            nav_success = await analyzer.navigate_to_ratio_analysis(stock_symbol)
            if not nav_success:
                return [TextContent(
                    type="text",
                    text=f"❌ Failed to navigate to ratio analysis for {stock_symbol}"
                )]
            
            # Extract ratios
            ratios_data = await analyzer.extract_financial_ratios(stock_symbol)
            
            if "error" in ratios_data:
                return [TextContent(
                    type="text",
                    text=f"❌ Failed to extract ratios: {ratios_data['error']}"
                )]
              # Format the output nicely
            output = f"📊 Financial Ratios Analysis for {stock_symbol}\n"
            output += f"🕒 Extracted at: {ratios_data['extraction_timestamp']}\n"
            output += "=" * 60 + "\n\n"
            
            # Display categorized ratios
            categories = [
                ("💰 Leverage Ratios", "leverage_ratios"),
                ("⚡ Efficiency Ratios", "efficiency_ratios"), 
                ("💹 Profitability Ratios", "profitability_ratios"),
                ("💎 Valuation Ratios", "valuation_ratios"),
                ("🏛️ Capital Allocation Ratios", "capital_allocation_ratios"),
                ("📋 Other Ratios", "other_ratios")
            ]
            
            for category_name, category_key in categories:
                if category_key in ratios_data and ratios_data[category_key]:
                    output += f"{category_name}:\n"
                    for metric, value in ratios_data[category_key].items():
                        if isinstance(value, dict):  # Multi-year data
                            output += f"  � {metric}:\n"
                            for year, year_value in value.items():
                                output += f"    {year}: {year_value}\n"
                        else:
                            output += f"  • {metric}: {value}\n"
                    output += "\n"
            
            # Display yearly data summary if available
            if ratios_data.get("yearly_data"):
                output += "📅 Yearly Data Summary:\n"
                years = sorted(ratios_data["yearly_data"].keys(), reverse=True)
                for year in years[:5]:  # Show last 5 years
                    year_data = ratios_data["yearly_data"][year]
                    if year_data:
                        output += f"  📊 {year}: {len(year_data)} metrics\n"
                output += "\n"
            
            # Add extraction summary
            total_metrics = sum(len(category_data) for category_data in ratios_data.values() 
                              if isinstance(category_data, dict) and category_data)
            output += f"📈 Summary: {total_metrics} total metrics extracted across all categories\n"
            
            return [TextContent(type="text", text=output)]
        
        else:
            return [TextContent(type="text", text=f"❌ Unknown tool: {name}")]
    
    except Exception as e:
        logger.error(f"Error in tool call: {e}")
        return [TextContent(type="text", text=f"❌ Error: {str(e)}")]
    
    finally:
        await analyzer.cleanup()

async def main():
    """Run the MCP server."""
    async with stdio_server() as (read_stream, write_stream):
        await app.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="screener-ratio-analyzer",
                server_version="1.0.0",
                capabilities=app.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())
