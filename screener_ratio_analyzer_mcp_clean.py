#!/usr/bin/env python3
"""
Screener.in Ratio Analysis MCP Server
Connects to existing Microsoft Edge browser to extract comprehensive financial ratios.
Supports Smart Analyze > Ratio Analysis for any stock with 10-year historical data.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional
import pandas as pd
from datetime import datetime
import re
import os

# MCP imports
from mcp.server.models import InitializationOptions
from mcp.server import NotificationOptions, Server
from mcp.types import Resource, Tool, TextContent, ImageContent, EmbeddedResource
from mcp.server.stdio import stdio_server

# Playwright imports for browser automation
from playwright.async_api import async_playwright, Browser, Page
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("screener-ratio-analyzer-mcp")

class ScreenerRatioAnalyzer:
    def __init__(self):
        self.browser = None
        self.page = None
        self.playwright = None
    
    async def connect_to_existing_edge(self, port: int = 9222):
        """Connect to existing Microsoft Edge browser using CDP."""
        try:
            self.playwright = await async_playwright().start()
            
            # Connect to existing Edge browser on debug port
            self.browser = await self.playwright.chromium.connect_over_cdp(f"http://localhost:{port}")
            
            # Get the existing context with user profile
            contexts = self.browser.contexts
            if contexts:
                # Use the first context (your existing profile)
                context = contexts[0]
                logger.info("Using existing browser context with your profile")
            else:
                # Create new context (this would use default profile)
                context = await self.browser.new_context()
                logger.info("Created new browser context")
            
            # Get existing page or create new one in existing context
            pages = context.pages
            if pages:
                # Use existing page
                self.page = pages[0]
                logger.info(f"Using existing page: {await self.page.title()}")
            else:
                # Create new page in existing context (preserves profile)
                self.page = await context.new_page()
                logger.info("Created new page in existing context")
            
            logger.info("Successfully connected to existing Edge browser with your profile")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to existing Edge browser: {e}")
            logger.error("Make sure Edge is running with --remote-debugging-port=9222")
            return False
    
    async def launch_new_edge(self):
        """This method is deprecated - we only connect to existing Edge."""
        logger.error("Cannot launch new Edge instance - this would lose your profile and extensions")
        logger.error("Please start your existing Edge browser with debugging enabled:")
        logger.error("1. Open Command Prompt as Administrator")
        logger.error("2. Close all Edge windows first")
        logger.error("3. Run: \"C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe\" --remote-debugging-port=9222 --user-data-dir=\"%LOCALAPPDATA%\\Microsoft\\Edge\\User Data\"")
        return False
    
    async def navigate_to_ratio_analysis(self, stock_symbol: str):
        """Navigate to ratio analysis following the exact sequence: Smart Analysis > Wait > Scroll > Ratio Analysis."""
        try:
            # Step 1: Navigate to company page
            url = f"https://www.screener.in/company/{stock_symbol}/"
            logger.info(f"🌐 Navigating to: {url}")
            await self.page.goto(url, wait_until="networkidle")
            logger.info("📄 Page loaded successfully")
            
            # Step 2: Click Smart Analysis button (try multiple selectors)
            logger.info("🔍 Looking for Smart Analysis button...")
            smart_analysis_selectors = [
                'a[href*="analysis"]',
                'button:has-text("Smart Analysis")',
                'a:has-text("Smart Analysis")',
                '.nav-link:has-text("Analysis")',
                'a[data-bs-target="#smartanalysis"]',
                'button[data-bs-target="#smartanalysis"]'
            ]
            
            smart_analysis_clicked = False
            for selector in smart_analysis_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=5000)
                    if element:
                        await element.click()
                        logger.info(f"✅ Clicked Smart Analysis using selector: {selector}")
                        smart_analysis_clicked = True
                        break
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue
            
            if not smart_analysis_clicked:
                logger.error("❌ Could not find or click Smart Analysis button")
                return False
            
            # Step 3: Wait exactly 4 seconds as requested
            logger.info("⏳ Waiting exactly 4 seconds for Smart Analysis to load...")
            await asyncio.sleep(4)
            
            # Step 4: Scroll down to find ratios table
            logger.info("📜 Scrolling down to find ratios section...")
            await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight / 2)")
            await asyncio.sleep(1)
            
            # Step 5: Click Ratio Analysis button
            logger.info("🔍 Looking for Ratio Analysis button...")
            ratio_analysis_selectors = [
                'button:has-text("Ratio Analysis")',
                'a:has-text("Ratio Analysis")',
                '.btn:has-text("Ratio")',
                'button[data-bs-target*="ratio"]',
                'a[href*="ratio"]'
            ]
            
            ratio_analysis_clicked = False
            for selector in ratio_analysis_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=5000)
                    if element:
                        await element.click()
                        logger.info(f"✅ Clicked Ratio Analysis using selector: {selector}")
                        ratio_analysis_clicked = True
                        break
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue
            
            if not ratio_analysis_clicked:
                logger.error("❌ Could not find or click Ratio Analysis button")
                # Try scrolling more and looking again
                await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await asyncio.sleep(2)
                
                for selector in ratio_analysis_selectors:
                    try:
                        element = await self.page.wait_for_selector(selector, timeout=3000)
                        if element:
                            await element.click()
                            logger.info(f"✅ Clicked Ratio Analysis after second scroll using: {selector}")
                            ratio_analysis_clicked = True
                            break
                    except Exception as e:
                        continue
                
                if not ratio_analysis_clicked:
                    logger.error("❌ Still could not find Ratio Analysis button after scrolling")
                    return False
            
            # Step 6: Wait for new data to load
            logger.info("⏳ Waiting for ratio data to load...")
            await asyncio.sleep(3)
            
            logger.info("🎉 Successfully completed navigation sequence!")
            return True
            
        except Exception as e:
            logger.error(f"Failed to navigate to ratio analysis: {e}")
            return False
    
    async def extract_financial_ratios(self, stock_symbol: str) -> Dict[str, Any]:
        """Extract all financial ratios from the newly loaded ratio analysis tables."""
        try:
            ratios_data = {
                "stock_symbol": stock_symbol,
                "extraction_timestamp": datetime.now().isoformat(),
                "ratios": {}
            }
            
            # Wait for tables to load
            await asyncio.sleep(2)
            
            # Extract all tables from the page
            tables = await self.page.query_selector_all('table')
            logger.info(f"📊 Found {len(tables)} tables on the page")
            
            for i, table in enumerate(tables):
                try:
                    # Get table content
                    table_html = await table.inner_html()
                    
                    # Parse with pandas
                    df = pd.read_html(f"<table>{table_html}</table>")[0]
                    
                    # Clean up the dataframe
                    if len(df.columns) >= 2:
                        df.columns = ['Metric', 'Value'] + list(df.columns[2:])
                        
                        # Convert to dictionary
                        table_dict = {}
                        for _, row in df.iterrows():
                            if pd.notna(row['Metric']) and pd.notna(row['Value']):
                                metric = str(row['Metric']).strip()
                                value = str(row['Value']).strip()
                                if metric and value and metric != 'Metric':
                                    table_dict[metric] = value
                        
                        if table_dict:
                            ratios_data["ratios"][f"table_{i+1}"] = table_dict
                            logger.info(f"✅ Extracted {len(table_dict)} ratios from table {i+1}")
                
                except Exception as e:
                    logger.debug(f"Could not parse table {i+1}: {e}")
                    continue
            
            # Also try to extract any ratio cards or individual metrics
            ratio_elements = await self.page.query_selector_all('[class*="ratio"], [class*="metric"], .card-body')
            
            additional_ratios = {}
            for element in ratio_elements:
                try:
                    text = await element.inner_text()
                    if ':' in text and len(text.strip()) < 100:  # Likely a ratio
                        parts = text.split(':')
                        if len(parts) == 2:
                            key = parts[0].strip()
                            value = parts[1].strip()
                            additional_ratios[key] = value
                except:
                    continue
            
            if additional_ratios:
                ratios_data["ratios"]["additional_metrics"] = additional_ratios
                logger.info(f"✅ Extracted {len(additional_ratios)} additional metrics")
            
            total_ratios = sum(len(table_data) for table_data in ratios_data["ratios"].values() if isinstance(table_data, dict))
            logger.info(f"🎯 Total ratios extracted: {total_ratios}")
            
            return ratios_data
            
        except Exception as e:
            logger.error(f"Failed to extract financial ratios: {e}")
            return {"error": str(e), "stock_symbol": stock_symbol}
    
    async def cleanup(self):
        """Clean up browser resources."""
        try:
            if self.page:
                await self.page.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            logger.info("🧹 Cleaned up browser resources")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# MCP Server Setup
app = Server("screener-ratio-analyzer")

@app.list_tools()
async def handle_list_tools() -> List[Tool]:
    """List available tools."""
    return [
        Tool(
            name="extract_financial_ratios",
            description="Extract comprehensive financial ratios from screener.in using Smart Analysis > Ratio Analysis workflow",
            inputSchema={
                "type": "object",
                "properties": {
                    "stock_symbol": {
                        "type": "string",
                        "description": "Stock symbol (e.g., RELIANCE, TCS, INFY)"
                    }
                },
                "required": ["stock_symbol"]
            }
        ),
        Tool(
            name="navigate_to_ratio_analysis",
            description="Navigate to ratio analysis page following exact sequence: Smart Analysis > Wait 4s > Scroll > Ratio Analysis",
            inputSchema={
                "type": "object",
                "properties": {
                    "stock_symbol": {
                        "type": "string",
                        "description": "Stock symbol to navigate to"
                    }
                },
                "required": ["stock_symbol"]
            }
        )
    ]

@app.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> List[TextContent]:
    """Handle tool calls."""
    analyzer = ScreenerRatioAnalyzer()
    
    try:
        # Connect to existing Edge browser
        connected = await analyzer.connect_to_existing_edge()
        if not connected:
            return [TextContent(
                type="text",
                text="❌ Could not connect to Edge browser. Please ensure Edge is running with --remote-debugging-port=9222"
            )]
        
        if name == "navigate_to_ratio_analysis":
            stock_symbol = arguments.get("stock_symbol", "").upper()
            if not stock_symbol:
                return [TextContent(type="text", text="❌ Stock symbol is required")]
            
            success = await analyzer.navigate_to_ratio_analysis(stock_symbol)
            
            if success:
                return [TextContent(
                    type="text",
                    text=f"✅ Successfully navigated to ratio analysis for {stock_symbol} using the exact sequence: Smart Analysis > Wait 4s > Scroll > Ratio Analysis"
                )]
            else:
                return [TextContent(
                    type="text",
                    text=f"❌ Failed to navigate to ratio analysis for {stock_symbol}"
                )]
        
        elif name == "extract_financial_ratios":
            stock_symbol = arguments.get("stock_symbol", "").upper()
            if not stock_symbol:
                return [TextContent(type="text", text="❌ Stock symbol is required")]
            
            # Navigate first
            nav_success = await analyzer.navigate_to_ratio_analysis(stock_symbol)
            if not nav_success:
                return [TextContent(
                    type="text",
                    text=f"❌ Failed to navigate to ratio analysis for {stock_symbol}"
                )]
            
            # Extract ratios
            ratios_data = await analyzer.extract_financial_ratios(stock_symbol)
            
            if "error" in ratios_data:
                return [TextContent(
                    type="text",
                    text=f"❌ Failed to extract ratios: {ratios_data['error']}"
                )]
            
            # Format the output nicely
            output = f"📊 Financial Ratios for {stock_symbol}\n"
            output += f"🕒 Extracted at: {ratios_data['extraction_timestamp']}\n\n"
            
            for table_name, table_data in ratios_data["ratios"].items():
                if isinstance(table_data, dict):
                    output += f"📋 {table_name.replace('_', ' ').title()}:\n"
                    for metric, value in table_data.items():
                        output += f"  • {metric}: {value}\n"
                    output += "\n"
            
            return [TextContent(type="text", text=output)]
        
        else:
            return [TextContent(type="text", text=f"❌ Unknown tool: {name}")]
    
    except Exception as e:
        logger.error(f"Error in tool call: {e}")
        return [TextContent(type="text", text=f"❌ Error: {str(e)}")]
    
    finally:
        await analyzer.cleanup()

async def main():
    """Run the MCP server."""
    async with stdio_server() as (read_stream, write_stream):
        await app.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="screener-ratio-analyzer",
                server_version="1.0.0",
                capabilities=app.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())
