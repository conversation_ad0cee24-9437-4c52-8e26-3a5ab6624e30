#!/usr/bin/env python3
"""
Screener Ratio Analyzer - New Version
Connects to existing Edge browser and extracts financial ratios from screener.in
Uses Playwright for fast and reliable automation.
"""

import asyncio
import json
import logging
import re
import time
from datetime import datetime
from typing import Any, Dict, List, Optional
import pandas as pd
import os

# Playwright imports
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("screener-ratio-analyzer-new")

class ScreenerRatioAnalyzer:
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        
    async def connect_to_existing_edge(self, debug_port: int = 9222):
        """Connect to existing Edge browser with debug mode enabled."""
        try:
            self.playwright = await async_playwright().start()
            
            # Connect to existing browser
            self.browser = await self.playwright.chromium.connect_over_cdp(f"http://localhost:{debug_port}")
            
            # Get existing context or create new one
            contexts = self.browser.contexts
            if contexts:
                self.context = contexts[0]
            else:
                self.context = await self.browser.new_context()
            
            # Get existing page or create new one
            pages = self.context.pages
            if pages:
                self.page = pages[0]
            else:
                self.page = await self.context.new_page()
            
            logger.info("✅ Successfully connected to existing Edge browser")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to Edge browser: {e}")
            return False
    
    async def navigate_to_screener(self, symbol: str = "RELIANCE"):
        """Navigate to screener.in company page."""
        try:
            url = f"https://www.screener.in/company/{symbol}/"
            logger.info(f"🌐 Navigating to {url}")
            
            await self.page.goto(url, wait_until="networkidle", timeout=30000)
            await asyncio.sleep(2)
            
            logger.info("✅ Successfully navigated to screener.in")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to navigate to screener.in: {e}")
            return False
    
    async def click_smart_analyze(self):
        """Click on Smart Analyze button."""
        try:
            logger.info("🔍 Looking for Smart Analyze button...")
            
            # Multiple selectors for Smart Analyze button
            selectors = [
                "text='Smart Analyze'",
                "button:has-text('Smart Analyze')",
                "a:has-text('Smart Analyze')",
                "[data-action='smart-analyze']",
                ".smart-analyze",
                "#smart-analyze"
            ]
            
            for selector in selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=5000)
                    if element:
                        await element.scroll_into_view_if_needed()
                        await asyncio.sleep(1)
                        await element.click()
                        logger.info("✅ Successfully clicked Smart Analyze button")
                        await asyncio.sleep(4)  # Wait 4 seconds as specified
                        return True
                except:
                    continue
            
            logger.warning("⚠️ Smart Analyze button not found with standard selectors")
            return False
            
        except Exception as e:
            logger.error(f"❌ Failed to click Smart Analyze: {e}")
            return False
    
    async def scroll_to_ratios_and_click_analysis(self):
        """Scroll down to ratios table and click ratio analysis button."""
        try:
            logger.info("📊 Scrolling to ratios section...")
            
            # Scroll down to find ratios section
            await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight / 2)")
            await asyncio.sleep(2)
            
            # Look for ratio analysis button
            logger.info("🔍 Looking for Ratio Analysis button...")
            
            selectors = [
                "text='Ratio Analysis'",
                "button:has-text('Ratio Analysis')",
                "a:has-text('Ratio Analysis')",
                "[data-action='ratio-analysis']",
                ".ratio-analysis"
            ]
            
            for selector in selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=5000)
                    if element:
                        await element.scroll_into_view_if_needed()
                        await asyncio.sleep(1)
                        await element.click()
                        logger.info("✅ Successfully clicked Ratio Analysis button")
                        await asyncio.sleep(3)  # Wait for new data to load
                        return True
                except:
                    continue
            
            logger.warning("⚠️ Ratio Analysis button not found")
            return False
            
        except Exception as e:
            logger.error(f"❌ Failed to click Ratio Analysis: {e}")
            return False
    
    async def extract_ratio_tables(self):
        """Extract all ratio tables from the page."""
        try:
            logger.info("📈 Extracting ratio tables...")
            
            # Wait for tables to load
            await asyncio.sleep(3)
            
            # Get all tables on the page
            tables = await self.page.query_selector_all("table")
            
            extracted_data = {
                "tables": [],
                "metadata": {
                    "url": self.page.url,
                    "title": await self.page.title(),
                    "extraction_time": datetime.now().isoformat(),
                    "total_tables": len(tables)
                }
            }
            
            logger.info(f"🔍 Found {len(tables)} tables on the page")
            
            for i, table in enumerate(tables):
                try:
                    table_data = await self.extract_table_data(table)
                    if table_data and len(table_data) > 1:
                        extracted_data["tables"].append({
                            "table_index": i,
                            "data": table_data,
                            "headers": table_data[0] if table_data else [],
                            "row_count": len(table_data) - 1
                        })
                        logger.info(f"  ✅ Table {i}: {len(table_data)-1} rows, {len(table_data[0]) if table_data else 0} columns")
                except Exception as e:
                    logger.warning(f"  ⚠️ Table {i}: Failed to extract - {e}")
            
            logger.info(f"✅ Successfully extracted {len(extracted_data['tables'])} tables")
            return extracted_data
            
        except Exception as e:
            logger.error(f"❌ Failed to extract ratio tables: {e}")
            return None
    
    async def extract_table_data(self, table):
        """Extract data from a single table element."""
        try:
            rows = await table.query_selector_all("tr")
            if not rows:
                return None
            
            table_data = []
            
            for row in rows:
                # Get all cells (th or td)
                cells = await row.query_selector_all("th, td")
                
                row_data = []
                for cell in cells:
                    cell_text = await cell.inner_text()
                    row_data.append(cell_text.strip())
                
                if row_data and any(row_data):  # Only add non-empty rows
                    table_data.append(row_data)
            
            return table_data if len(table_data) > 1 else None
            
        except Exception as e:
            logger.warning(f"Failed to extract table data: {e}")
            return None
    
    def convert_to_aapl_format(self, extracted_data: Dict, symbol: str = "RELIANCE") -> List[Dict]:
        """Convert extracted data to AAPL format with comprehensive mapping."""
        
        # Comprehensive ratio mapping
        ratio_mapping = {
            # Valuation Ratios
            "pe ratio": "price_to_earnings_ratio",
            "pe": "price_to_earnings_ratio",
            "price to earnings": "price_to_earnings_ratio",
            "p/e": "price_to_earnings_ratio",
            "pb ratio": "price_to_book_ratio",
            "pb": "price_to_book_ratio",
            "price to book": "price_to_book_ratio",
            "p/b": "price_to_book_ratio",
            "ps ratio": "price_to_sales_ratio",
            "ps": "price_to_sales_ratio",
            "price to sales": "price_to_sales_ratio",
            "p/s": "price_to_sales_ratio",
            "ev/ebitda": "enterprise_value_to_ebitda_ratio",
            "ev/sales": "enterprise_value_to_revenue_ratio",
            "ev/revenue": "enterprise_value_to_revenue_ratio",
            "enterprise value/ebitda": "enterprise_value_to_ebitda_ratio",
            "enterprise value/sales": "enterprise_value_to_revenue_ratio",
            "market cap": "market_cap",
            "enterprise value": "enterprise_value",
            
            # Profitability Ratios
            "roe": "return_on_equity",
            "return on equity": "return_on_equity",
            "roa": "return_on_assets",
            "return on assets": "return_on_assets",
            "roic": "return_on_invested_capital",
            "return on invested capital": "return_on_invested_capital",
            "return on capital employed": "return_on_invested_capital",
            "roce": "return_on_invested_capital",
            "gross margin": "gross_margin",
            "gross profit margin": "gross_margin",
            "operating margin": "operating_margin",
            "operating profit margin": "operating_margin",
            "net margin": "net_margin",
            "net profit margin": "net_margin",
            "profit margin": "net_margin",
            "ebitda margin": "ebitda_margin",
            "ebit margin": "ebit_margin",
            
            # Efficiency Ratios
            "asset turnover": "asset_turnover",
            "total asset turnover": "asset_turnover",
            "inventory turnover": "inventory_turnover",
            "receivables turnover": "receivables_turnover",
            "accounts receivable turnover": "receivables_turnover",
            "working capital turnover": "working_capital_turnover",
            "fixed asset turnover": "fixed_asset_turnover",
            "days sales outstanding": "days_sales_outstanding",
            "dso": "days_sales_outstanding",
            "operating cycle": "operating_cycle",
            
            # Leverage/Liquidity Ratios
            "debt to equity": "debt_to_equity",
            "debt/equity": "debt_to_equity",
            "debt equity ratio": "debt_to_equity",
            "debt to assets": "debt_to_assets",
            "debt/assets": "debt_to_assets",
            "debt ratio": "debt_to_assets",
            "interest coverage": "interest_coverage",
            "interest coverage ratio": "interest_coverage",
            "times interest earned": "interest_coverage",
            "current ratio": "current_ratio",
            "quick ratio": "quick_ratio",
            "acid test ratio": "quick_ratio",
            "cash ratio": "cash_ratio",
            "operating cash flow ratio": "operating_cash_flow_ratio",
            
            # Growth Ratios
            "revenue growth": "revenue_growth",
            "sales growth": "revenue_growth",
            "profit growth": "earnings_growth",
            "earnings growth": "earnings_growth",
            "net income growth": "earnings_growth",
            "eps growth": "earnings_per_share_growth",
            "earnings per share growth": "earnings_per_share_growth",
            "book value growth": "book_value_growth",
            "free cash flow growth": "free_cash_flow_growth",
            "fcf growth": "free_cash_flow_growth",
            "operating income growth": "operating_income_growth",
            "ebitda growth": "ebitda_growth",
            
            # Per Share Metrics
            "earnings per share": "earnings_per_share",
            "eps": "earnings_per_share",
            "book value per share": "book_value_per_share",
            "bvps": "book_value_per_share",
            "free cash flow per share": "free_cash_flow_per_share",
            "fcf per share": "free_cash_flow_per_share",
            "revenue per share": "revenue_per_share",
            "sales per share": "revenue_per_share",
            
            # Additional Ratios
            "peg ratio": "peg_ratio",
            "price/earnings to growth": "peg_ratio",
            "dividend yield": "dividend_yield",
            "payout ratio": "payout_ratio",
            "dividend payout ratio": "payout_ratio",
            "free cash flow yield": "free_cash_flow_yield",
            "fcf yield": "free_cash_flow_yield",
        }
        
        converted_data = []
        
        logger.info("🔄 Converting extracted data to AAPL format...")
        
        # Process all tables
        for table_info in extracted_data.get("tables", []):
            table_data = table_info.get("data", [])
            
            if not table_data or len(table_data) < 2:
                continue
            
            headers = table_data[0]
            logger.info(f"  📊 Processing table with headers: {headers[:5]}...")
            
            # Find year columns
            year_columns = []
            for i, header in enumerate(headers):
                header_str = str(header).strip()
                if re.search(r'(20\d{2}|FY\s*\d{2,4}|TTM|Mar\s*\d{2,4})', header_str, re.IGNORECASE):
                    year_columns.append((i, header_str))
            
            if not year_columns:
                logger.info(f"    ⚠️ No year columns found in this table")
                continue
            
            logger.info(f"    ✅ Found {len(year_columns)} year columns: {[col[1] for col in year_columns]}")
            
            # Process each row
            ratios_found = 0
            for row in table_data[1:]:
                if not row or len(row) == 0:
                    continue
                
                ratio_name = row[0].lower().strip()
                
                # Find matching AAPL format name
                aapl_name = None
                best_match = ""
                for screener_key, aapl_key in ratio_mapping.items():
                    if screener_key in ratio_name:
                        if len(screener_key) > len(best_match):
                            aapl_name = aapl_key
                            best_match = screener_key
                
                if not aapl_name:
                    continue
                
                ratios_found += 1
                
                # Create records for each year
                for col_index, year_header in year_columns:
                    if col_index < len(row):
                        value = self.clean_numeric_value(row[col_index])
                        
                        # Find or create record for this year
                        year_record = None
                        for record in converted_data:
                            if record.get("report_period") == year_header:
                                year_record = record
                                break
                        
                        if not year_record:
                            year_record = {
                                "ticker": f"{symbol}.NS",
                                "report_period": year_header,
                                "fiscal_period": year_header,
                                "period": "annual" if "FY" in str(year_header) or "Mar" in str(year_header) else "ttm",
                                "currency": "INR"
                            }
                            converted_data.append(year_record)
                        
                        year_record[aapl_name] = value
            
            logger.info(f"    ✅ Extracted {ratios_found} ratios from this table")
        
        # Sort by year (most recent first)
        converted_data.sort(key=lambda x: self.extract_year_from_period(x.get("report_period", "")), reverse=True)
        
        logger.info(f"✅ Conversion complete: {len(converted_data)} periods with financial data")
        return converted_data
    
    def extract_year_from_period(self, period_str: str) -> int:
        """Extract year from period string for sorting."""
        try:
            match = re.search(r'20\d{2}', str(period_str))
            if match:
                return int(match.group())
            
            match = re.search(r'(\d{2})', str(period_str))
            if match:
                year_2d = int(match.group())
                return 2000 + year_2d if year_2d < 50 else 1900 + year_2d
            
            return 0
        except:
            return 0
    
    def clean_numeric_value(self, value_str: str) -> float:
        """Clean and convert string values to numeric."""
        if not value_str or value_str in ['-', 'N/A', 'NA', '', 'nil', 'Nil', '--']:
            return 0.0
        
        try:
            cleaned = str(value_str).strip()
            is_percentage = '%' in cleaned
            
            # Remove common characters
            cleaned = re.sub(r'[,%₹$\s]', '', cleaned)
            
            # Handle negative values in parentheses
            if '(' in cleaned and ')' in cleaned:
                cleaned = '-' + cleaned.replace('(', '').replace(')', '')
            
            # Handle multipliers
            multiplier = 1
            if 'cr' in cleaned.lower() or 'crore' in cleaned.lower():
                cleaned = re.sub(r'(cr|crore)', '', cleaned, flags=re.IGNORECASE)
                multiplier = 10000000
            elif 'l' in cleaned.lower() or 'lakh' in cleaned.lower():
                cleaned = re.sub(r'(l|lakh)', '', cleaned, flags=re.IGNORECASE)
                multiplier = 100000
            elif 'k' in cleaned.lower():
                cleaned = re.sub(r'k', '', cleaned, flags=re.IGNORECASE)
                multiplier = 1000
            elif 'm' in cleaned.lower():
                cleaned = re.sub(r'm', '', cleaned, flags=re.IGNORECASE)
                multiplier = 1000000
            elif 'b' in cleaned.lower():
                cleaned = re.sub(r'b', '', cleaned, flags=re.IGNORECASE)
                multiplier = 1000000000
            
            # Remove any remaining non-numeric characters
            cleaned = re.sub(r'[^0-9.-]', '', cleaned)
            
            if not cleaned or cleaned in ['-', '.']:
                return 0.0
            
            numeric_value = float(cleaned) * multiplier
            
            # Convert percentages to decimals
            if is_percentage:
                numeric_value = numeric_value / 100
            
            return numeric_value
            
        except:
            return 0.0
    
    def save_data(self, data: Any, filename: str) -> Optional[str]:
        """Save data to file."""
        try:
            output_dir = "RELIANCE_NS_screener_data"
            os.makedirs(output_dir, exist_ok=True)
            
            filepath = os.path.join(output_dir, filename)
            
            if filename.endswith('.json'):
                with open(filepath, 'w') as f:
                    json.dump(data, f, indent=2, default=str)
            elif filename.endswith('.csv') and isinstance(data, list):
                df = pd.DataFrame(data)
                df.to_csv(filepath, index=False)
            
            logger.info(f"💾 Saved data to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"❌ Failed to save data: {e}")
            return None
    
    async def close(self):
        """Close browser connection."""
        if self.playwright:
            await self.playwright.stop()

# Main function to extract ratios from screener.in
async def extract_screener_ratios(symbol: str = "RELIANCE", debug_port: int = 9222) -> Dict[str, Any]:
    """Main function to extract ratios from screener.in."""
    
    analyzer = ScreenerRatioAnalyzer()
    
    try:
        # Connect to existing Edge browser
        if not await analyzer.connect_to_existing_edge(debug_port):
            return {"error": "Failed to connect to Edge browser. Make sure Edge is running with debug mode."}
        
        # Navigate to screener.in
        if not await analyzer.navigate_to_screener(symbol):
            return {"error": f"Failed to navigate to screener.in for {symbol}"}
        
        # Click Smart Analyze
        if not await analyzer.click_smart_analyze():
            return {"error": "Failed to click Smart Analyze button"}
        
        # Scroll and click Ratio Analysis
        if not await analyzer.scroll_to_ratios_and_click_analysis():
            return {"error": "Failed to click Ratio Analysis button"}
        
        # Extract ratio tables
        extracted_data = await analyzer.extract_ratio_tables()
        if not extracted_data:
            return {"error": "Failed to extract ratio data"}
        
        # Convert to AAPL format
        converted_data = analyzer.convert_to_aapl_format(extracted_data, symbol)
        
        # Save data
        analyzer.save_data(extracted_data, "raw_extracted_data.json")
        analyzer.save_data(converted_data, f"financial_metrics_{symbol}_SCREENER.csv")
        analyzer.save_data(converted_data, f"financial_metrics_{symbol}_SCREENER.json")
        
        return {
            "status": "success",
            "symbol": symbol,
            "total_tables": len(extracted_data.get("tables", [])),
            "total_periods": len(converted_data),
            "converted_data": converted_data,
            "metadata": extracted_data.get("metadata", {})
        }
        
    except Exception as e:
        logger.error(f"❌ Error in extract_screener_ratios: {e}")
        return {"error": str(e)}
    
    finally:
        await analyzer.close()

# Simple CLI interface for testing
async def main():
    """Main CLI function."""
    print("=" * 80)
    print("SCREENER RATIO ANALYZER - NEW VERSION")
    print("=" * 80)
    
    symbol = input("Enter stock symbol (default: RELIANCE): ").strip() or "RELIANCE"
    
    print(f"\n🎯 Extracting ratios for {symbol}...")
    print("📋 Make sure Edge is running with debug mode (run setup_edge_debug.ps1)")
    
    result = await extract_screener_ratios(symbol)
    
    if "error" in result:
        print(f"❌ Error: {result['error']}")
    else:
        print(f"✅ Success!")
        print(f"📊 Extracted {result['total_tables']} tables")
        print(f"📈 Generated {result['total_periods']} periods of data")
        print(f"💾 Data saved to RELIANCE_NS_screener_data/")

if __name__ == "__main__":
    asyncio.run(main())
