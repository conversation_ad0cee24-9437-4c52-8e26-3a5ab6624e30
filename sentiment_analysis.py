"""
<PERSON><PERSON><PERSON> to run sentiment analysis on any stock using mock data.
This script copies the sentiment_agent code from the AI Hedge Fund project
but uses mock API data for any ticker.
"""

import sys
import os
import json
import argparse
from colorama import Fore, Style, init
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Initialize colorama
init(autoreset=True)

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Import the mock API functions directly
from src.tools.mock_api import (
    get_insider_trades,
    get_company_news
)

# Import necessary functions from the project
from src.utils.progress import progress

print(f"{Fore.GREEN}Successfully imported mock API functions{Style.RESET_ALL}")

def sentiment_analysis(ticker, end_date=None):
    """
    Run sentiment analysis on a ticker using the same logic as the sentiment_agent
    but with mock data for any ticker.
    """
    # If end_date is not provided, use today's date
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")
    
    print(f"\nRunning sentiment analysis for {Fore.CYAN}{ticker}{Style.RESET_ALL} as of {end_date}...")
    
    progress.update_status("sentiment_analysis", ticker, "Fetching insider trades")
    # Get the insider trades
    insider_trades = get_insider_trades(
        ticker=ticker,
        end_date=end_date,
        limit=1000,
    )

    progress.update_status("sentiment_analysis", ticker, "Analyzing trading patterns")
    # Get the signals from the insider trades
    transaction_shares = pd.Series([t.transaction_shares for t in insider_trades]).dropna()
    insider_signals = np.where(transaction_shares < 0, "bearish", "bullish").tolist()

    progress.update_status("sentiment_analysis", ticker, "Fetching company news")
    # Get the company news
    company_news = get_company_news(ticker, end_date, limit=100)

    # Get the sentiment from the company news
    sentiment = pd.Series([n.sentiment for n in company_news]).dropna()
    news_signals = np.where(sentiment == "negative", "bearish", 
                          np.where(sentiment == "positive", "bullish", "neutral")).tolist()
    
    progress.update_status("sentiment_analysis", ticker, "Combining signals")
    # Combine signals from both sources with weights
    insider_weight = 0.3
    news_weight = 0.7
    
    # Calculate weighted signal counts
    bullish_signals = (
        insider_signals.count("bullish") * insider_weight +
        news_signals.count("bullish") * news_weight
    )
    bearish_signals = (
        insider_signals.count("bearish") * insider_weight +
        news_signals.count("bearish") * news_weight
    )

    if bullish_signals > bearish_signals:
        overall_signal = "bullish"
    elif bearish_signals > bullish_signals:
        overall_signal = "bearish"
    else:
        overall_signal = "neutral"

    # Calculate confidence level based on the weighted proportion
    total_weighted_signals = len(insider_signals) * insider_weight + len(news_signals) * news_weight
    confidence = 0  # Default confidence when there are no signals
    if total_weighted_signals > 0:
        confidence = round(max(bullish_signals, bearish_signals) / total_weighted_signals, 2) * 100
    
    # Create detailed reasoning
    reasoning = {
        "insider_trades": {
            "total": len(insider_signals),
            "bullish": insider_signals.count("bullish"),
            "bearish": insider_signals.count("bearish"),
            "weighted_bullish": insider_signals.count("bullish") * insider_weight,
            "weighted_bearish": insider_signals.count("bearish") * insider_weight,
        },
        "news_sentiment": {
            "total": len(news_signals),
            "bullish": news_signals.count("bullish"),
            "bearish": news_signals.count("bearish"),
            "neutral": news_signals.count("neutral"),
            "weighted_bullish": news_signals.count("bullish") * news_weight,
            "weighted_bearish": news_signals.count("bearish") * news_weight,
        },
        "summary": f"Weighted Bullish signals: {bullish_signals:.1f}, Weighted Bearish signals: {bearish_signals:.1f}"
    }

    # Create the final analysis
    sentiment_analysis = {
        "signal": overall_signal,
        "confidence": confidence,
        "reasoning": reasoning,
    }

    progress.update_status("sentiment_analysis", ticker, "Done")

    # Print the analysis
    print(f"\n{Fore.GREEN}Sentiment Analysis for {ticker}:{Style.RESET_ALL}")
    print(f"Signal: {Fore.CYAN if overall_signal == 'bullish' else Fore.RED if overall_signal == 'bearish' else Fore.YELLOW}{overall_signal.upper()}{Style.RESET_ALL}")
    print(f"Confidence: {confidence:.1f}%")
    
    # Print the reasoning
    print(f"\n{Fore.GREEN}Insider Trading:{Style.RESET_ALL}")
    print(f"Total: {reasoning['insider_trades']['total']}")
    print(f"Bullish: {reasoning['insider_trades']['bullish']} ({reasoning['insider_trades']['weighted_bullish']:.1f} weighted)")
    print(f"Bearish: {reasoning['insider_trades']['bearish']} ({reasoning['insider_trades']['weighted_bearish']:.1f} weighted)")
    
    print(f"\n{Fore.GREEN}News Sentiment:{Style.RESET_ALL}")
    print(f"Total: {reasoning['news_sentiment']['total']}")
    print(f"Bullish: {reasoning['news_sentiment']['bullish']} ({reasoning['news_sentiment']['weighted_bullish']:.1f} weighted)")
    print(f"Bearish: {reasoning['news_sentiment']['bearish']} ({reasoning['news_sentiment']['weighted_bearish']:.1f} weighted)")
    print(f"Neutral: {reasoning['news_sentiment']['neutral']}")
    
    print(f"\n{Fore.GREEN}Summary:{Style.RESET_ALL}")
    print(reasoning['summary'])
    
    # Save the analysis to a file
    with open(f"{ticker}_sentiment_analysis.json", "w") as f:
        json.dump(sentiment_analysis, f, indent=2)
    
    print(f"\nAnalysis saved to {Fore.GREEN}{ticker}_sentiment_analysis.json{Style.RESET_ALL}")
    
    # Print some sample news and insider trades
    print(f"\n{Fore.GREEN}Sample News:{Style.RESET_ALL}")
    for i, news in enumerate(company_news[:5]):
        sentiment_color = Fore.CYAN if news.sentiment == "positive" else Fore.RED if news.sentiment == "negative" else Fore.YELLOW
        print(f"{i+1}. {news.title} - {sentiment_color}{news.sentiment.upper()}{Style.RESET_ALL} ({news.date})")
    
    print(f"\n{Fore.GREEN}Sample Insider Trades:{Style.RESET_ALL}")
    for i, trade in enumerate(insider_trades[:5]):
        trade_type = "SELLING" if trade.transaction_shares < 0 else "BUYING"
        trade_color = Fore.RED if trade.transaction_shares < 0 else Fore.CYAN
        print(f"{i+1}. {trade.name} ({trade.title}): {trade_color}{trade_type} {abs(trade.transaction_shares)} shares{Style.RESET_ALL} at ${trade.transaction_price_per_share:.2f} ({trade.transaction_date})")
    
    return sentiment_analysis

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run sentiment analysis on any stock using mock data.')
    parser.add_argument('ticker', type=str, help='Stock ticker symbol (e.g., AMD, AAPL)')
    parser.add_argument('--date', type=str, help='End date for analysis (YYYY-MM-DD format)', default=None)
    args = parser.parse_args()
    
    # Run the analysis
    sentiment_analysis(args.ticker, args.date)
