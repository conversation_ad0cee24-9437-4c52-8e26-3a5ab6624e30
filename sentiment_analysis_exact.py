"""
<PERSON><PERSON><PERSON> to run sentiment analysis on any stock using mock data.
This script exactly copies the sentiment_agent code from the AI Hedge Fund project
but uses mock API data for any ticker.
"""

import sys
import os
import json
import argparse
from datetime import datetime
from colorama import Fore, Style, init
import pandas as pd
import numpy as np

# Initialize colorama
init(autoreset=True)

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Import the mock API functions directly
from src.tools.mock_api import (
    get_insider_trades,
    get_company_news
)

# Import necessary functions from the project
from src.utils.progress import progress
from src.graph.state import show_agent_reasoning

print(f"{Fore.GREEN}Successfully imported mock API functions{Style.RESET_ALL}")

def sentiment_agent_mock(ticker, end_date=None):
    """
    Exact copy of the sentiment_agent function from the AI Hedge Fund project,
    but using mock API data for any ticker.
    """
    # If end_date is not provided, use today's date
    if end_date is None:
        end_date = datetime.now().strftime("%Y-%m-%d")
    
    print(f"\nRunning sentiment analysis for {Fore.CYAN}{ticker}{Style.RESET_ALL} as of {end_date}...")
    
    # Initialize sentiment analysis for the ticker
    sentiment_analysis = {}

    progress.update_status("sentiment_agent", ticker, "Fetching insider trades")

    # Get the insider trades
    insider_trades = get_insider_trades(
        ticker=ticker,
        end_date=end_date,
        limit=1000,
    )

    progress.update_status("sentiment_agent", ticker, "Analyzing trading patterns")

    # Get the signals from the insider trades
    transaction_shares = pd.Series([t.transaction_shares for t in insider_trades]).dropna()
    insider_signals = np.where(transaction_shares < 0, "bearish", "bullish").tolist()

    progress.update_status("sentiment_agent", ticker, "Fetching company news")

    # Get the company news
    company_news = get_company_news(ticker, end_date, limit=100)

    # Get the sentiment from the company news
    sentiment = pd.Series([n.sentiment for n in company_news]).dropna()
    news_signals = np.where(sentiment == "negative", "bearish", 
                          np.where(sentiment == "positive", "bullish", "neutral")).tolist()
    
    progress.update_status("sentiment_agent", ticker, "Combining signals")
    # Combine signals from both sources with weights
    insider_weight = 0.3
    news_weight = 0.7
    
    # Calculate weighted signal counts
    bullish_signals = (
        insider_signals.count("bullish") * insider_weight +
        news_signals.count("bullish") * news_weight
    )
    bearish_signals = (
        insider_signals.count("bearish") * insider_weight +
        news_signals.count("bearish") * news_weight
    )

    if bullish_signals > bearish_signals:
        overall_signal = "bullish"
    elif bearish_signals > bullish_signals:
        overall_signal = "bearish"
    else:
        overall_signal = "neutral"

    # Calculate confidence level based on the weighted proportion
    total_weighted_signals = len(insider_signals) * insider_weight + len(news_signals) * news_weight
    confidence = 0  # Default confidence when there are no signals
    if total_weighted_signals > 0:
        confidence = round(max(bullish_signals, bearish_signals) / total_weighted_signals, 2) * 100
    reasoning = f"Weighted Bullish signals: {bullish_signals:.1f}, Weighted Bearish signals: {bearish_signals:.1f}"

    sentiment_analysis[ticker] = {
        "signal": overall_signal,
        "confidence": confidence,
        "reasoning": reasoning,
    }

    progress.update_status("sentiment_agent", ticker, "Done")

    # Print the analysis
    print(f"\n{Fore.GREEN}Sentiment Analysis for {ticker}:{Style.RESET_ALL}")
    print(f"Signal: {Fore.CYAN if overall_signal == 'bullish' else Fore.RED if overall_signal == 'bearish' else Fore.YELLOW}{overall_signal.upper()}{Style.RESET_ALL}")
    print(f"Confidence: {confidence:.1f}%")
    print(f"Reasoning: {reasoning}")
    
    # Print some sample data
    print(f"\n{Fore.GREEN}Sample News:{Style.RESET_ALL}")
    for i, news in enumerate(company_news[:5]):
        sentiment_color = Fore.CYAN if news.sentiment == "positive" else Fore.RED if news.sentiment == "negative" else Fore.YELLOW
        print(f"{i+1}. {news.title} - {sentiment_color}{news.sentiment.upper()}{Style.RESET_ALL} ({news.date})")
    
    print(f"\n{Fore.GREEN}Sample Insider Trades:{Style.RESET_ALL}")
    for i, trade in enumerate(insider_trades[:5]):
        trade_type = "SELLING" if trade.transaction_shares < 0 else "BUYING"
        trade_color = Fore.RED if trade.transaction_shares < 0 else Fore.CYAN
        print(f"{i+1}. {trade.name} ({trade.title}): {trade_color}{trade_type} {abs(trade.transaction_shares)} shares{Style.RESET_ALL} at ${trade.transaction_price_per_share:.2f} ({trade.transaction_date})")
    
    # Save the analysis to a file
    with open(f"{ticker}_sentiment_analysis.json", "w") as f:
        json.dump(sentiment_analysis, f, indent=2)
    
    print(f"\nAnalysis saved to {Fore.GREEN}{ticker}_sentiment_analysis.json{Style.RESET_ALL}")
    
    # Show reasoning
    show_agent_reasoning(sentiment_analysis, "Sentiment Analysis Agent")
    
    return sentiment_analysis

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run sentiment analysis on any stock using mock data.')
    parser.add_argument('ticker', type=str, help='Stock ticker symbol (e.g., AMD, AAPL)')
    parser.add_argument('--date', type=str, help='End date for analysis (YYYY-MM-DD format)', default=None)
    args = parser.parse_args()
    
    # Run the analysis
    sentiment_agent_mock(args.ticker, args.date)
