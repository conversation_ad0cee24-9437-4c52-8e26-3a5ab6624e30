# Edge Browser Debug Setup Script for Screener Ratio Analyzer MCP
# This script configures Microsoft Edge to allow remote debugging for automation

Write-Host "🔧 Setting up Microsoft Edge for Screener Ratio Analyzer MCP..." -ForegroundColor Green

# Function to check if Edge is running
function Test-EdgeRunning {
    $edgeProcesses = Get-Process -Name "msedge" -ErrorAction SilentlyContinue
    return ($edgeProcesses.Count -gt 0)
}

# Function to check if debug port is available
function Test-DebugPort {
    param($Port = 9222)
    try {
        $tcpConnection = Test-NetConnection -ComputerName "localhost" -Port $Port -WarningAction SilentlyContinue
        return $tcpConnection.TcpTestSucceeded
    }
    catch {
        return $false
    }
}

# Function to get Edge user data directory
function Get-EdgeUserDataDir {
    $defaultPath = "$env:LOCALAPPDATA\Microsoft\Edge\User Data"
    if (Test-Path $defaultPath) {
        return $defaultPath
    }
    
    # Alternative paths
    $altPaths = @(
        "$env:APPDATA\Microsoft\Edge\User Data",
        "$env:USERPROFILE\AppData\Local\Microsoft\Edge\User Data"
    )
    
    foreach ($path in $altPaths) {
        if (Test-Path $path) {
            return $path
        }
    }
    
    return $defaultPath  # Use default even if it doesn't exist
}

# Check if Edge is already running with debug mode
if (Test-DebugPort -Port 9222) {
    Write-Host "✅ Edge is already running with debug port 9222 enabled" -ForegroundColor Green
    Write-Host "🎯 Ready to run MCP server!" -ForegroundColor Cyan
    exit 0
}

# If Edge is running but without debug mode, we need to restart it
if (Test-EdgeRunning) {
    Write-Host "⚠️  Edge is running but debug mode is not enabled" -ForegroundColor Yellow
    Write-Host "📋 To enable automation, Edge needs to be restarted with debug flags" -ForegroundColor Yellow
    
    $response = Read-Host "Do you want to close Edge and restart with debug mode? (y/N)"
    
    if ($response -eq "y" -or $response -eq "Y") {
        Write-Host "🔄 Closing existing Edge processes..." -ForegroundColor Yellow
        Get-Process -Name "msedge" -ErrorAction SilentlyContinue | Stop-Process -Force
        Start-Sleep -Seconds 3
        
        # Wait for processes to fully close
        $timeout = 0
        while ((Test-EdgeRunning) -and ($timeout -lt 10)) {
            Start-Sleep -Seconds 1
            $timeout++
        }
        
        if (Test-EdgeRunning) {
            Write-Host "❌ Could not close Edge processes. Please manually close Edge and run this script again." -ForegroundColor Red
            exit 1
        }
          Write-Host "✅ Edge processes closed successfully" -ForegroundColor Green
    }
    else {
        Write-Host "❌ User cancelled. Please manually close Edge and restart with debug flags." -ForegroundColor Red
        Write-Host "💡 Manual command: msedge.exe --remote-debugging-port=9222" -ForegroundColor Cyan
        exit 1
    }
}

# Find Edge executable
$edgePaths = @(
    "${env:ProgramFiles(x86)}\Microsoft\Edge\Application\msedge.exe",
    "$env:ProgramFiles\Microsoft\Edge\Application\msedge.exe",
    "$env:LOCALAPPDATA\Microsoft\Edge\Application\msedge.exe"
)

$edgeExe = $null
foreach ($path in $edgePaths) {
    if (Test-Path $path) {
        $edgeExe = $path
        break
    }
}

if (-not $edgeExe) {
    Write-Host "❌ Microsoft Edge executable not found!" -ForegroundColor Red
    Write-Host "🔍 Searched in:" -ForegroundColor Yellow
    foreach ($path in $edgePaths) {
        Write-Host "   - $path" -ForegroundColor Gray
    }
    exit 1
}

Write-Host "✅ Found Edge executable: $edgeExe" -ForegroundColor Green

# Get user data directory
$userDataDir = Get-EdgeUserDataDir
Write-Host "📂 Using user data directory: $userDataDir" -ForegroundColor Cyan

# Start Edge with debug flags
Write-Host "🚀 Starting Edge with remote debugging enabled..." -ForegroundColor Green

$arguments = @(
    "--remote-debugging-port=9222",
    "--user-data-dir=`"$userDataDir`"",
    "--disable-web-security",
    "--disable-features=VizDisplayCompositor",
    "--enable-automation"
)

try {
    $process = Start-Process -FilePath $edgeExe -ArgumentList $arguments -PassThru
    Write-Host "✅ Edge started with PID: $($process.Id)" -ForegroundColor Green
    
    # Wait for debug port to become available
    Write-Host "⏳ Waiting for debug port to become available..." -ForegroundColor Yellow
    
    $maxWait = 30  # seconds
    $waited = 0
    while (-not (Test-DebugPort -Port 9222) -and ($waited -lt $maxWait)) {
        Start-Sleep -Seconds 1
        $waited++
        Write-Progress -Activity "Waiting for Edge to start" -Status "Waited $waited seconds" -PercentComplete (($waited / $maxWait) * 100)
    }
    
    Write-Progress -Completed -Activity "Waiting for Edge to start"
    
    if (Test-DebugPort -Port 9222) {
        Write-Host "🎉 Edge is ready for automation!" -ForegroundColor Green
        Write-Host "🌐 Debug endpoint: http://localhost:9222" -ForegroundColor Cyan
        Write-Host "🎯 You can now run the MCP server!" -ForegroundColor Cyan
        
        # Optional: Open screener.in
        $openScreener = Read-Host "Do you want to open screener.in/company/RELIANCE for testing? (y/N)"
        if ($openScreener -eq "y" -or $openScreener -eq "Y") {
            Start-Process $edgeExe -ArgumentList "https://www.screener.in/company/RELIANCE/"
        }
        
        Write-Host "`n📋 Next steps:" -ForegroundColor Yellow
        Write-Host "   1. Keep this Edge window open" -ForegroundColor Gray
        Write-Host "   2. Run: python screener_ratio_analyzer_mcp.py" -ForegroundColor Gray
        Write-Host "   3. Or run: npm start" -ForegroundColor Gray
        
    }
    else {
        Write-Host "❌ Debug port did not become available within $maxWait seconds" -ForegroundColor Red
        Write-Host "🔍 Please check if another process is using port 9222" -ForegroundColor Yellow
        exit 1
    }
}
catch {
    Write-Host "❌ Failed to start Edge: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n✅ Setup completed successfully!" -ForegroundColor Green
