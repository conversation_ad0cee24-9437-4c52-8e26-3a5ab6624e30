"""
Test script to check if the API module is working correctly with AAPL.
AAPL is in the free tier, so it should work without an API key.
"""

import sys
import os
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Import the API functions directly
from src.tools.api import (
    get_prices,
    get_financial_metrics,
    search_line_items,
    get_insider_trades,
    get_company_news,
    get_market_cap
)

def test_aapl_api():
    """Test the API functions with AAPL."""
    ticker = "AAPL"
    end_date = "2023-12-31"
    start_date = "2023-12-01"
    
    print(f"\nTesting API functions with {Fore.CYAN}{ticker}{Style.RESET_ALL}...")
    print(f"Date range: {Fore.YELLOW}{start_date}{Style.RESET_ALL} to {Fore.YELLOW}{end_date}{Style.RESET_ALL}")
    
    try:
        # Test get_prices
        print(f"\nTesting get_prices for {ticker}...")
        prices = get_prices(ticker, start_date, end_date)
        print(f"{Fore.GREEN}Success!{Style.RESET_ALL} Got {len(prices)} price records")
        
        # Test get_financial_metrics
        print(f"\nTesting get_financial_metrics for {ticker}...")
        metrics = get_financial_metrics(ticker, end_date)
        print(f"{Fore.GREEN}Success!{Style.RESET_ALL} Got {len(metrics)} financial metrics records")
        
        # Test search_line_items
        print(f"\nTesting search_line_items for {ticker}...")
        line_items = search_line_items(ticker, ["revenue", "net_income"], end_date)
        print(f"{Fore.GREEN}Success!{Style.RESET_ALL} Got {len(line_items)} line items records")
        
        # Test get_insider_trades
        print(f"\nTesting get_insider_trades for {ticker}...")
        insider_trades = get_insider_trades(ticker, end_date)
        print(f"{Fore.GREEN}Success!{Style.RESET_ALL} Got {len(insider_trades)} insider trades records")
        
        # Test get_company_news
        print(f"\nTesting get_company_news for {ticker}...")
        news = get_company_news(ticker, end_date)
        print(f"{Fore.GREEN}Success!{Style.RESET_ALL} Got {len(news)} company news records")
        
        # Test get_market_cap
        print(f"\nTesting get_market_cap for {ticker}...")
        market_cap = get_market_cap(ticker, end_date)
        print(f"{Fore.GREEN}Success!{Style.RESET_ALL} Market cap: ${market_cap:,.2f}")
        
        print(f"\n{Fore.GREEN}All tests passed!{Style.RESET_ALL} The API module is working correctly.")
        return True
    
    except Exception as e:
        print(f"\n{Fore.RED}Error:{Style.RESET_ALL} {e}")
        print(f"\n{Fore.RED}The API module is corrupted.{Style.RESET_ALL} Restoring from backup...")
        return False

if __name__ == "__main__":
    if not test_aapl_api():
        # Restore the API module from backup
        backup_path = os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src', 'tools', 'api.py.bak')
        api_path = os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src', 'tools', 'api.py')
        
        if os.path.exists(backup_path):
            try:
                with open(backup_path, 'r') as f:
                    backup_code = f.read()
                
                with open(api_path, 'w') as f:
                    f.write(backup_code)
                
                print(f"{Fore.GREEN}API module restored from backup.{Style.RESET_ALL}")
            except Exception as e:
                print(f"{Fore.RED}Error restoring API module:{Style.RESET_ALL} {e}")
        else:
            print(f"{Fore.RED}Backup file not found:{Style.RESET_ALL} {backup_path}")
    else:
        print(f"\n{Fore.GREEN}No need to restore the API module.{Style.RESET_ALL}")
