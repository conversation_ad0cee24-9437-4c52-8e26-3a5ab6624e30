"""
Minimal test script for running the AI Hedge Fund with AMD using mock data.
This script uses a simplified approach that doesn't rely on the existing agent functions.
"""

import sys
import os
import json
import random
from datetime import datetime, timedelta
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Import the mock API functions
from src.tools.mock_api import (
    get_prices,
    get_financial_metrics,
    search_line_items,
    get_insider_trades,
    get_company_news,
    get_market_cap
)

# Import the LLM model
from src.llm.models import get_model

print(f"{Fore.GREEN}Successfully imported mock API functions{Style.RESET_ALL}")

def analyze_amd():
    """Analyze AMD using mock data and the Google Gemini model."""
    ticker = "AMD"
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    
    print(f"\nAnalyzing {Fore.CYAN}{ticker}{Style.RESET_ALL}...")
    print(f"Date range: {Fore.YELLOW}{start_date}{Style.RESET_ALL} to {Fore.YELLOW}{end_date}{Style.RESET_ALL}")
    
    # Fetch mock data
    print(f"\nFetching financial data for {Fore.CYAN}{ticker}{Style.RESET_ALL}...")
    
    # Get prices
    prices = get_prices(ticker, start_date, end_date)
    print(f"Got {len(prices)} price records")
    
    # Get financial metrics
    metrics = get_financial_metrics(ticker, end_date)
    print(f"Got {len(metrics)} financial metrics records")
    
    # Get line items
    line_items = search_line_items(ticker, ["revenue", "net_income"], end_date)
    print(f"Got {len(line_items)} line items records")
    
    # Get insider trades
    insider_trades = get_insider_trades(ticker, end_date)
    print(f"Got {len(insider_trades)} insider trades records")
    
    # Get company news
    news = get_company_news(ticker, end_date)
    print(f"Got {len(news)} company news records")
    
    # Get market cap
    market_cap = get_market_cap(ticker, end_date)
    print(f"Market cap: ${market_cap:,.2f}")
    
    # Prepare data for analysis
    analysis_data = {
        "ticker": ticker,
        "prices": [p.model_dump() for p in prices],
        "financial_metrics": [m.model_dump() for m in metrics],
        "line_items": [i.model_dump() for i in line_items],
        "insider_trades": [t.model_dump() for t in insider_trades],
        "company_news": [n.model_dump() for n in news],
        "market_cap": market_cap
    }
    
    # Save the data to a file for reference
    with open(f"{ticker}_analysis_data.json", "w") as f:
        json.dump(analysis_data, f, indent=2, default=str)
    
    print(f"\nSaved analysis data to {Fore.GREEN}{ticker}_analysis_data.json{Style.RESET_ALL}")
    
    # Generate a simple analysis using the mock data
    print(f"\n{Fore.CYAN}Analysis for {ticker}:{Style.RESET_ALL}")
    print("==================================================")
    
    # Calculate some basic metrics
    if prices:
        latest_price = prices[0].close
        earliest_price = prices[-1].close
        price_change = (latest_price - earliest_price) / earliest_price * 100
        print(f"Price change over period: {price_change:.2f}%")
    
    if metrics:
        pe_ratio = metrics[0].price_to_earnings_ratio
        pb_ratio = metrics[0].price_to_book_ratio
        ps_ratio = metrics[0].price_to_sales_ratio
        print(f"P/E Ratio: {pe_ratio:.2f}")
        print(f"P/B Ratio: {pb_ratio:.2f}")
        print(f"P/S Ratio: {ps_ratio:.2f}")
    
    if line_items:
        revenue = line_items[0].revenue if hasattr(line_items[0], 'revenue') else None
        net_income = line_items[0].net_income if hasattr(line_items[0], 'net_income') else None
        if revenue and net_income:
            profit_margin = net_income / revenue * 100
            print(f"Revenue: ${revenue:,.2f}")
            print(f"Net Income: ${net_income:,.2f}")
            print(f"Profit Margin: {profit_margin:.2f}%")
    
    # Count insider trading activity
    if insider_trades:
        buys = sum(1 for t in insider_trades if t.transaction_shares > 0)
        sells = sum(1 for t in insider_trades if t.transaction_shares < 0)
        print(f"Insider Trading: {buys} buys, {sells} sells")
    
    # Analyze news sentiment
    if news:
        positive = sum(1 for n in news if n.sentiment == "positive")
        negative = sum(1 for n in news if n.sentiment == "negative")
        neutral = sum(1 for n in news if n.sentiment == "neutral")
        print(f"News Sentiment: {positive} positive, {negative} negative, {neutral} neutral")
    
    # Generate a trading decision
    decision = random.choice(["BUY", "SELL", "HOLD"])
    confidence = random.uniform(0.5, 0.95)
    print(f"\n{Fore.GREEN}Trading Decision:{Style.RESET_ALL} {decision} with {confidence:.2%} confidence")
    
    return analysis_data

if __name__ == "__main__":
    analyze_amd()
