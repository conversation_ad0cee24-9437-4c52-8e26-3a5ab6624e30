"""
Test script for running the AI Hedge Fund with mock data.
This script is a direct copy of main.py with the API functions patched to use mock data.
"""

import sys
import os

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# IMPORTANT: Monkey patch the API functions to use our mock API
# This needs to be done before any other imports that might use the API
import src.tools.api as real_api
from src.tools.mock_api import (
    get_prices,
    get_financial_metrics,
    search_line_items,
    get_insider_trades,
    get_company_news,
    get_market_cap,
    prices_to_df,
    get_price_data
)

# Replace the real API functions with our mock API functions
real_api.get_prices = get_prices
real_api.get_financial_metrics = get_financial_metrics
real_api.search_line_items = search_line_items
real_api.get_insider_trades = get_insider_trades
real_api.get_company_news = get_company_news
real_api.get_market_cap = get_market_cap
real_api.prices_to_df = prices_to_df
real_api.get_price_data = get_price_data

from colorama import Fore, Style, init
init(autoreset=True)
print(f"{Fore.GREEN}Successfully patched API functions to use mock data{Style.RESET_ALL}")

# Now import everything from the original main.py
from dotenv import load_dotenv
from langchain_core.messages import HumanMessage
from langgraph.graph import END, StateGraph
from colorama import Fore, Back, Style, init
import questionary
from src.agents.ben_graham import ben_graham_agent
from src.agents.bill_ackman import bill_ackman_agent
from src.agents.fundamentals import fundamentals_agent
from src.agents.portfolio_manager import portfolio_management_agent
from src.agents.technicals import technical_analyst_agent
from src.agents.risk_manager import risk_management_agent
from src.agents.sentiment import sentiment_agent
from src.agents.warren_buffett import warren_buffett_agent
from src.graph.state import AgentState
from src.agents.valuation import valuation_agent
from src.utils.display import print_trading_output
from src.utils.analysts import ANALYST_ORDER, get_analyst_nodes
from src.utils.progress import progress
from src.llm.models import LLM_ORDER, get_model_info

import argparse
from datetime import datetime
from dateutil.relativedelta import relativedelta
from tabulate import tabulate
from src.utils.visualize import save_graph_as_png
import json

# Import all other agents
from src.agents.cathie_wood import cathie_wood_agent
from src.agents.charlie_munger import charlie_munger_agent
from src.agents.michael_burry import michael_burry_agent
from src.agents.peter_lynch import peter_lynch_agent
from src.agents.phil_fisher import phil_fisher_agent
from src.agents.stanley_druckenmiller import stanley_druckenmiller_agent

# Load environment variables from .env file
load_dotenv()

init(autoreset=True)


def parse_hedge_fund_response(response):
    """Parses a JSON string and returns a dictionary."""
    try:
        return json.loads(response)
    except json.JSONDecodeError as e:
        print(f"JSON decoding error: {e}\nResponse: {repr(response)}")
        return None
    except TypeError as e:
        print(f"Invalid response type (expected string, got {type(response).__name__}): {e}")
        return None
    except Exception as e:
        print(f"Unexpected error while parsing response: {e}\nResponse: {repr(response)}")
        return None



##### Run the Hedge Fund #####
def run_hedge_fund(
    tickers,
    start_date,
    end_date,
    portfolio,
    show_reasoning=False,
    selected_analysts=None,
    model_name="gpt-4o",
):
    """Run the hedge fund with the given parameters."""
    # Create the initial state
    initial_state = AgentState(
        tickers=tickers,
        portfolio=portfolio,
        start_date=start_date,
        end_date=end_date,
        ticker_analyses={},
        model_name=model_name,
    )

    # Create the workflow
    workflow = create_workflow(selected_analysts)
    app = workflow.compile()

    # Run the workflow
    for ticker in tickers:
        print(f"\nAnalyzing {Fore.CYAN}{ticker}{Style.RESET_ALL}...")
        
        # Update the state with the current ticker
        state = initial_state.model_copy(deep=True)
        state.current_ticker = ticker
        
        # Run the workflow for this ticker
        final_state = app.invoke(state)
        
        # Update the initial state with the results
        initial_state.ticker_analyses[ticker] = final_state.ticker_analyses[ticker]
        initial_state.portfolio = final_state.portfolio
    
    # Print the trading output
    print_trading_output(initial_state, show_reasoning)
    
    return initial_state


def create_workflow(selected_analysts=None):
    """Create the workflow graph with the selected analysts."""
    # Create a new graph
    workflow = StateGraph(AgentState)
    
    # Get all analyst nodes
    all_nodes = get_analyst_nodes()
    
    # Filter nodes based on selected analysts
    if selected_analysts:
        nodes = {}
        for key, (node_name, node_fn) in all_nodes.items():
            if key in selected_analysts:
                nodes[node_name] = node_fn
    else:
        nodes = {node_name: node_fn for key, (node_name, node_fn) in all_nodes.items()}
    
    # Add nodes to the graph
    for name, node_fn in nodes.items():
        workflow.add_node(name, node_fn)
    
    # Add the portfolio management node
    workflow.add_node("portfolio_management", portfolio_management_agent)
    
    # Connect the nodes
    node_names = list(nodes.keys())
    for i, name in enumerate(node_names):
        if i == 0:
            # First node
            workflow.set_entry_point(name)
        else:
            # Connect to previous node
            prev_name = node_names[i-1]
            workflow.add_edge(prev_name, name)
    
    # Connect the last analyst to the portfolio management node
    if node_names:
        last_name = node_names[-1]
        workflow.add_edge(last_name, "portfolio_management")
    else:
        # If no analysts were selected, set portfolio management as the entry point
        workflow.set_entry_point("portfolio_management")
    
    # Connect the portfolio management node to the end
    workflow.add_edge("portfolio_management", END)
    
    return workflow


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="AI Hedge Fund")
    parser.add_argument("--tickers", type=str, help="Comma-separated list of tickers to analyze")
    parser.add_argument("--start-date", type=str, help="Start date for analysis (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=str, help="End date for analysis (YYYY-MM-DD)")
    parser.add_argument("--show-reasoning", action="store_true", help="Show agent reasoning")
    parser.add_argument("--show-agent-graph", action="store_true", help="Show agent graph")
    parser.add_argument("--analysts", type=str, help="Comma-separated list of analysts to use")
    parser.add_argument("--model", type=str, help="LLM model to use")
    args = parser.parse_args()

    # Set default tickers
    if args.tickers:
        tickers = args.tickers.split(",")
    else:
        tickers = ["AMD"]  # Default to AMD for testing with mock data
        print(f"\nUsing mock data for {Fore.CYAN}{', '.join(tickers)}{Style.RESET_ALL}")

    # Set default dates
    end_date = datetime.now().strftime("%Y-%m-%d")
    if args.end_date:
        end_date = args.end_date
    
    start_date = (datetime.strptime(end_date, "%Y-%m-%d") - relativedelta(months=1)).strftime("%Y-%m-%d")
    if args.start_date:
        start_date = args.start_date

    # Set default portfolio
    portfolio = {
        "positions": {},
        "total_cash": 100000.0,
    }

    # Set default model
    model_name = args.model if args.model else None

    # Set default analysts
    selected_analysts = []
    if args.analysts:
        selected_analysts = args.analysts.split(",")
    else:
        # Prompt the user to select analysts
        print(f"{Fore.CYAN}Select the analysts you want to use:{Style.RESET_ALL}")
        selected_analysts = questionary.checkbox(
            "Select analysts:",
            choices=[questionary.Choice(display, value=value) for display, value in ANALYST_ORDER],
            style=questionary.Style([
                ("selected", "fg:green bold"),
                ("pointer", "fg:green bold"),
                ("highlighted", "fg:green"),
                ("answer", "fg:green bold"),
            ])
        ).ask()

        if not selected_analysts:
            print("\n\nInterrupt received. Exiting...")
            sys.exit(0)
        else:
            print(f"\nSelected analysts: {Fore.GREEN + Style.BRIGHT}{', '.join(selected_analysts)}{Style.RESET_ALL}\n")

    # Prompt the user to select a model if not provided
    if not model_name:
        model_choice = questionary.select(
            "Select your LLM model:",
            choices=[questionary.Choice(display, value=value) for display, value, _ in LLM_ORDER],
            style=questionary.Style([
                ("selected", "fg:green bold"),
                ("pointer", "fg:green bold"),
                ("highlighted", "fg:green"),
                ("answer", "fg:green bold"),
            ])
        ).ask()

        if not model_choice:
            print("\n\nInterrupt received. Exiting...")
            sys.exit(0)
        else:
            # Get model info using the helper function
            model_info = get_model_info(model_choice)
            if model_info:
                model_provider = model_info.provider.value
                print(f"\nSelected {Fore.CYAN}{model_provider}{Style.RESET_ALL} model: {Fore.GREEN + Style.BRIGHT}{model_choice}{Style.RESET_ALL}\n")
            else:
                model_provider = "Unknown"
                print(f"\nSelected model: {Fore.GREEN + Style.BRIGHT}{model_choice}{Style.RESET_ALL}\n")
            
            model_name = model_choice

    # Create the workflow with selected analysts
    workflow = create_workflow(selected_analysts)
    app = workflow.compile()

    if args.show_agent_graph:
        file_path = ""
        if selected_analysts is not None:
            for selected_analyst in selected_analysts:
                file_path += selected_analyst + "_"
        file_path += "graph.png"
        save_graph_as_png(workflow, file_path)
        print(f"Agent graph saved to {file_path}")

    # Run the hedge fund
    run_hedge_fund(
        tickers=tickers,
        start_date=start_date,
        end_date=end_date,
        portfolio=portfolio,
        show_reasoning=args.show_reasoning,
        selected_analysts=selected_analysts,
        model_name=model_name,
    )
