"""
Simplified test script for running the AI Hedge Fund with AMD using mock data.
This script uses a fixed set of analysts to avoid node type conflicts.
"""

import sys
import os

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

from dotenv import load_dotenv
from langchain_core.messages import HumanMessage
from langgraph.graph import END, StateGraph
from colorama import Fore, Style, init
from src.graph.state import AgentState
from src.utils.display import print_trading_output

# Import only a few analysts to avoid conflicts
from src.agents.warren_buffett import warren_buffett_agent
from src.agents.peter_lynch import peter_lynch_agent
from src.agents.portfolio_manager import portfolio_management_agent

import argparse
from datetime import datetime
from dateutil.relativedelta import relativedelta
import json

# Load environment variables from .env file
load_dotenv()

init(autoreset=True)

# IMPORTANT: Create a mock API module
# We'll use this instead of the real API module
from src.tools.mock_api import (
    get_prices,
    get_financial_metrics,
    search_line_items,
    get_insider_trades,
    get_company_news,
    get_market_cap,
    prices_to_df,
    get_price_data
)

# Create a mock API module
import sys
class MockAPI:
    def __init__(self):
        self.get_prices = get_prices
        self.get_financial_metrics = get_financial_metrics
        self.search_line_items = search_line_items
        self.get_insider_trades = get_insider_trades
        self.get_company_news = get_company_news
        self.get_market_cap = get_market_cap
        self.prices_to_df = prices_to_df
        self.get_price_data = get_price_data

# Replace the real API module with our mock API module
# We need to patch both 'tools.api' (for relative imports) and 'src.tools.api' (for absolute imports)
mock_api = MockAPI()
sys.modules['tools.api'] = mock_api
sys.modules['src.tools.api'] = mock_api

print(f"{Fore.GREEN}Successfully patched API functions to use mock data{Style.RESET_ALL}")


def parse_hedge_fund_response(response):
    """Parses a JSON string and returns a dictionary."""
    try:
        return json.loads(response)
    except json.JSONDecodeError as e:
        print(f"JSON decoding error: {e}\nResponse: {repr(response)}")
        return None
    except TypeError as e:
        print(f"Invalid response type (expected string, got {type(response).__name__}): {e}")
        return None
    except Exception as e:
        print(f"Unexpected error while parsing response: {e}\nResponse: {repr(response)}")
        return None


def run_hedge_fund(
    tickers,
    start_date,
    end_date,
    portfolio,
    model_name="gpt-4o",
):
    """Run the hedge fund with the given parameters."""
    # Create the initial state
    initial_state = {
        "messages": [
            HumanMessage(
                content="Make trading decisions based on the provided data.",
            )
        ],
        "data": {
            "tickers": tickers,
            "portfolio": portfolio,
            "start_date": start_date,
            "end_date": end_date,
            "analyst_signals": {},
            "current_ticker": tickers[0],  # Start with the first ticker
        },
        "metadata": {
            "show_reasoning": True,
            "model_name": model_name,
        }
    }

    # Create the workflow
    workflow = create_workflow()
    app = workflow.compile()

    # Run the workflow
    for ticker in tickers:
        print(f"\nAnalyzing {Fore.CYAN}{ticker}{Style.RESET_ALL}...")

        # Create a new state for each ticker
        state = {
            "messages": [
                HumanMessage(
                    content="Make trading decisions based on the provided data.",
                )
            ],
            "data": {
                "tickers": tickers,
                "portfolio": initial_state["data"]["portfolio"],
                "start_date": start_date,
                "end_date": end_date,
                "analyst_signals": {},
                "current_ticker": ticker,
            },
            "metadata": {
                "show_reasoning": True,
                "model_name": model_name,
            }
        }

        # Run the workflow for this ticker
        final_state = app.invoke(state)

        # Update the initial state with the results
        if "analyst_signals" not in initial_state["data"]:
            initial_state["data"]["analyst_signals"] = {}
        initial_state["data"]["analyst_signals"][ticker] = final_state["data"]["analyst_signals"].get(ticker, {})
        initial_state["data"]["portfolio"] = final_state["data"]["portfolio"]

    # Print the final results
    print(f"\n{Fore.GREEN}Analysis complete!{Style.RESET_ALL}")
    print(f"\nPortfolio: {json.dumps(initial_state['data']['portfolio'], indent=2)}")

    return initial_state


# Create wrapper functions that adapt our agents to the expected state format
def warren_buffett_wrapper(state):
    # The original agent expects an AgentState (TypedDict)
    return warren_buffett_agent(state)

def peter_lynch_wrapper(state):
    # The original agent expects an AgentState (TypedDict)
    return peter_lynch_agent(state)

def portfolio_management_wrapper(state):
    # The original agent expects an AgentState (TypedDict)
    return portfolio_management_agent(state)

def create_workflow():
    """Create a simple workflow graph with Warren Buffett and Peter Lynch."""
    # Create a new graph
    workflow = StateGraph(AgentState)

    # Add the analyst nodes with our wrapper functions
    workflow.add_node("warren_buffett", warren_buffett_wrapper)
    workflow.add_node("peter_lynch", peter_lynch_wrapper)
    workflow.add_node("portfolio_management", portfolio_management_wrapper)

    # Connect the nodes
    workflow.set_entry_point("warren_buffett")
    workflow.add_edge("warren_buffett", "peter_lynch")
    workflow.add_edge("peter_lynch", "portfolio_management")
    workflow.add_edge("portfolio_management", END)

    return workflow


if __name__ == "__main__":
    # Set default tickers to AMD (our mock data)
    tickers = ["AMD"]

    # Set default dates
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.strptime(end_date, "%Y-%m-%d") - relativedelta(months=1)).strftime("%Y-%m-%d")

    # Set default portfolio
    portfolio = {
        "positions": {},
        "total_cash": 100000.0,
    }

    # Set default model (use your Google API key)
    model_name = "gemini-2.5-pro-exp-03-25"

    print(f"\nRunning analysis for {Fore.CYAN}AMD{Style.RESET_ALL} using mock data...")
    print(f"Using model: {Fore.GREEN}{model_name}{Style.RESET_ALL}")
    print(f"Date range: {Fore.YELLOW}{start_date}{Style.RESET_ALL} to {Fore.YELLOW}{end_date}{Style.RESET_ALL}")
    print(f"Analysts: {Fore.MAGENTA}Warren Buffett, Peter Lynch{Style.RESET_ALL}")

    # Run the hedge fund
    run_hedge_fund(
        tickers=tickers,
        start_date=start_date,
        end_date=end_date,
        portfolio=portfolio,
        model_name=model_name,
    )
