"""
Test script for running the AI Hedge Fund with the Mock Sentiment Analyst.
"""

import sys
import os
import json
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from colorama import Fore, Style, init
from langgraph.graph import END, StateGraph

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai-hedge-fund', 'src'))

# Import necessary functions from the project
from src.graph.state import AgentState
from src.utils.display import print_trading_output
from src.utils.analysts import ANALYST_ORDER, get_analyst_nodes
from src.utils.progress import progress
from src.llm.models import LLM_ORDER, get_model_info
from src.agents.portfolio_manager import portfolio_management_agent
from src.agents.risk_manager import risk_management_agent
from src.main import run_hedge_fund

import argparse
from tabulate import tabulate
from src.utils.visualize import save_graph_as_png

# Initialize colorama
init(autoreset=True)

def start(state: AgentState):
    """Initialize the workflow with the input message."""
    return state


def create_workflow(selected_analysts=None):
    """Create the workflow with selected analysts."""
    workflow = StateGraph(AgentState)
    workflow.add_node("start_node", start)

    # Get analyst nodes from the configuration
    analyst_nodes = get_analyst_nodes()

    # Default to all analysts if none selected
    if selected_analysts is None:
        selected_analysts = list(analyst_nodes.keys())
    # Add selected analyst nodes
    for analyst_key in selected_analysts:
        node_name, node_func = analyst_nodes[analyst_key]
        workflow.add_node(node_name, node_func)
        workflow.add_edge("start_node", node_name)

    # Always add risk and portfolio management
    workflow.add_node("risk_management_agent", risk_management_agent)
    workflow.add_node("portfolio_management_agent", portfolio_management_agent)

    # Connect selected analysts to risk management
    for analyst_key in selected_analysts:
        node_name = analyst_nodes[analyst_key][0]
        workflow.add_edge(node_name, "risk_management_agent")

    workflow.add_edge("risk_management_agent", "portfolio_management_agent")
    workflow.add_edge("portfolio_management_agent", END)

    workflow.set_entry_point("start_node")
    return workflow

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test the Mock Sentiment Analyst")
    parser.add_argument("--tickers", type=str, default="AMD", help="Comma-separated list of tickers to analyze")
    parser.add_argument("--start-date", type=str, help="Start date for analysis (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=str, help="End date for analysis (YYYY-MM-DD)")
    parser.add_argument("--show-reasoning", action="store_true", help="Show agent reasoning")
    parser.add_argument("--show-agent-graph", action="store_true", help="Show agent graph")
    parser.add_argument("--analysts", type=str, default="mock_sentiment_analyst", help="Comma-separated list of analysts to use")
    parser.add_argument("--model", type=str, default="gpt-4o", help="LLM model to use")
    args = parser.parse_args()

    # Parse tickers
    tickers = [ticker.strip() for ticker in args.tickers.split(",")]

    # Parse analysts
    selected_analysts = args.analysts.split(",")
    print(f"\nSelected analysts: {Fore.GREEN + Style.BRIGHT}{', '.join(selected_analysts)}{Style.RESET_ALL}\n")

    # Set model
    model_name = args.model
    model_info = get_model_info(model_name)
    if model_info:
        model_provider = model_info.provider.value
        print(f"\nSelected {Fore.CYAN}{model_provider}{Style.RESET_ALL} model: {Fore.GREEN + Style.BRIGHT}{model_name}{Style.RESET_ALL}\n")
    else:
        model_provider = "OpenAI"
        print(f"\nSelected model: {Fore.GREEN + Style.BRIGHT}{model_name}{Style.RESET_ALL}\n")

    # Set dates
    end_date = args.end_date or datetime.now().strftime("%Y-%m-%d")
    if args.start_date:
        start_date = args.start_date
    else:
        # Default to 3 months before end date
        end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")
        start_date_dt = end_date_dt - relativedelta(months=3)
        start_date = start_date_dt.strftime("%Y-%m-%d")

    print(f"Analysis period: {Fore.CYAN}{start_date}{Style.RESET_ALL} to {Fore.CYAN}{end_date}{Style.RESET_ALL}")

    # Create the workflow
    workflow = create_workflow(selected_analysts)
    app = workflow.compile()

    if args.show_agent_graph:
        file_path = ""
        for selected_analyst in selected_analysts:
            file_path += selected_analyst + "_"
        file_path += "graph.png"
        save_graph_as_png(workflow, file_path)
        print(f"Agent graph saved to {file_path}")

    # Initialize portfolio
    portfolio = {
        "cash": 100000.0,
        "positions": {ticker: {"shares": 0, "cost_basis": 0.0} for ticker in tickers},
        "margin_requirement": 0.0,
        "realized_gains": {
            ticker: {
                "long": 0.0,  # Realized gains from long positions
                "short": 0.0,  # Realized gains from short positions
            } for ticker in tickers
        }
    }

    # Run the hedge fund
    result = run_hedge_fund(
        tickers=tickers,
        start_date=start_date,
        end_date=end_date,
        portfolio=portfolio,
        show_reasoning=args.show_reasoning,
        selected_analysts=selected_analysts,
        model_name=model_name,
        model_provider=model_provider,
    )

    print_trading_output(result)
