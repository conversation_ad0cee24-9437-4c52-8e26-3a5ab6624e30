import sys
import os

# Add the project directory to the Python path
project_dir = os.path.join(os.path.dirname(__file__), 'ai-hedge-fund')
src_dir = os.path.join(project_dir, 'src')
sys.path.append(project_dir)
sys.path.append(src_dir)

# Try to import from the project
try:
    from data.models import Price, FinancialMetrics
    print("Successfully imported models!")
except Exception as e:
    print(f"Error importing models: {e}")

# Try to import from the cache module
try:
    from data.cache import Cache, get_cache
    print("Successfully imported cache!")
except Exception as e:
    print(f"Error importing cache: {e}")

# Try to import from the API module
try:
    from tools.api import get_prices, get_financial_metrics
    print("Successfully imported API functions!")
except Exception as e:
    print(f"Error importing API functions: {e}")
