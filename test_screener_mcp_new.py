#!/usr/bin/env python3
"""
Test script for Screener Ratio Analyzer MCP
Tests the complete workflow: navigation, extraction, and data formatting
"""

import asyncio
import json
import logging
from screener_ratio_analyzer_mcp import ScreenerRatioAnalyzer

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_full_workflow():
    """Test the complete workflow for ratio extraction."""
    analyzer = ScreenerRatioAnalyzer()
    
    try:
        print("🚀 Starting Screener Ratio Analyzer Test")
        print("=" * 50)
        
        # Test connection to Edge
        print("\n📡 Step 1: Connecting to Microsoft Edge...")
        connected = await analyzer.connect_to_existing_edge()
        
        if not connected:
            print("❌ Failed to connect to Edge browser")
            print("💡 Make sure Edge is running with: --remote-debugging-port=9222")
            return False
        
        print("✅ Successfully connected to Edge")
        
        # Test navigation
        print("\n🌐 Step 2: Testing navigation to ratio analysis...")
        test_stocks = ["RELIANCE", "TCS", "INFY"]
        
        for stock in test_stocks:
            print(f"\n🎯 Testing with {stock}...")
            
            # Navigate to ratio analysis
            nav_success = await analyzer.navigate_to_ratio_analysis(stock)
            
            if nav_success:
                print(f"✅ Navigation successful for {stock}")
                
                # Extract ratios
                print(f"📊 Extracting ratios for {stock}...")
                ratios_data = await analyzer.extract_financial_ratios(stock)
                
                if "error" not in ratios_data:
                    print(f"✅ Ratio extraction successful for {stock}")
                    
                    # Save results to file
                    filename = f"{stock}_ratios_test_{int(asyncio.get_event_loop().time())}.json"
                    with open(filename, 'w') as f:
                        json.dump(ratios_data, f, indent=2)
                    
                    print(f"💾 Results saved to {filename}")
                    
                    # Print summary
                    print(f"📈 Summary for {stock}:")
                    print(f"   Leverage ratios: {len(ratios_data.get('leverage_ratios', {}))}")
                    print(f"   Efficiency ratios: {len(ratios_data.get('efficiency_ratios', {}))}")
                    print(f"   Profitability ratios: {len(ratios_data.get('profitability_ratios', {}))}")
                    print(f"   Valuation ratios: {len(ratios_data.get('valuation_ratios', {}))}")
                    print(f"   Capital allocation ratios: {len(ratios_data.get('capital_allocation_ratios', {}))}")
                    print(f"   Years of data: {len(ratios_data.get('yearly_data', {}))}")
                    
                    # Test with just one stock for now
                    break
                    
                else:
                    print(f"❌ Ratio extraction failed for {stock}: {ratios_data.get('error')}")
            else:
                print(f"❌ Navigation failed for {stock}")
        
        print("\n🎉 Test completed!")
        return True
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        return False
    
    finally:
        await analyzer.cleanup()

async def test_mcp_tools():
    """Test the MCP tools directly."""
    print("\n🔧 Testing MCP Tools...")
    
    from screener_ratio_analyzer_mcp import handle_call_tool
    
    # Test navigation tool
    print("🧪 Testing navigate_to_ratio_analysis tool...")
    result = await handle_call_tool("navigate_to_ratio_analysis", {"stock_symbol": "RELIANCE"})
    print(f"Result: {result[0].text}")
    
    # Test extraction tool
    print("\n🧪 Testing extract_financial_ratios tool...")
    result = await handle_call_tool("extract_financial_ratios", {"stock_symbol": "RELIANCE"})
    print(f"Result: {result[0].text[:500]}...")  # Show first 500 chars

def print_edge_debug_instructions():
    """Print instructions for setting up Edge debug mode."""
    print("\n" + "=" * 60)
    print("🔧 EDGE DEBUG SETUP INSTRUCTIONS")
    print("=" * 60)
    print("If the test fails to connect to Edge, follow these steps:")
    print()
    print("1. Close all Edge windows")
    print("2. Run the setup script:")
    print("   powershell -ExecutionPolicy Bypass -File setup_edge_debug.ps1")
    print()
    print("OR manually:")
    print("3. Open Command Prompt as Administrator")
    print("4. Run this command:")
    print('   "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe" --remote-debugging-port=9222 --user-data-dir="%LOCALAPPDATA%\\Microsoft\\Edge\\User Data"')
    print()
    print("5. Wait for Edge to open, then run this test again")
    print("=" * 60)

if __name__ == "__main__":
    print("🧪 Screener Ratio Analyzer MCP Test Suite")
    print_edge_debug_instructions()
    
    # Run the test
    asyncio.run(test_full_workflow())
    
    print("\n🔚 Test suite completed!")
