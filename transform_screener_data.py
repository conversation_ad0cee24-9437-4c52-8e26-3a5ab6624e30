"""
Sc<PERSON>t to transform Screener.in data to match the format expected by AI Hedge Fund analysis agents.
"""

import pandas as pd
import os
import json
from datetime import datetime
import re
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

def clean_column_name(col_name):
    """Clean column names by removing special characters and standardizing format."""
    # Remove special characters
    col_name = re.sub(r'[^\w\s]', '', col_name)
    # Convert to lowercase and replace spaces with underscores
    col_name = col_name.lower().replace(' ', '_')
    return col_name

def transform_quarterly_results(input_file, output_file):
    """Transform quarterly results to match the expected format."""
    print(f"{Fore.CYAN}Transforming quarterly results from {input_file}...{Style.RESET_ALL}")

    # Read the CSV file
    df = pd.read_csv(input_file)

    # Clean up column names
    df.columns = [col.replace('Â', '').replace('+', '').strip() for col in df.columns]

    # Create a new DataFrame with the expected structure
    result_df = pd.DataFrame()

    # Convert date columns to proper datetime format
    date_columns = [col for col in df.columns if col not in ['Metric']]

    for col in date_columns:
        # Extract month and year
        match = re.match(r'(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+(\d{4})', col)
        if match:
            month, year = match.groups()
            month_num = {'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06',
                         'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'}[month]
            date_str = f"{year}-{month_num}-01"

            # Create a row for each date
            row = {'date': date_str}

            # Add metrics for this date
            for _, metric_row in df.iterrows():
                metric_name = metric_row['Metric']
                if pd.notna(metric_name) and metric_name.strip():
                    # Clean up metric name
                    clean_metric = clean_column_name(metric_name)
                    # Get the value for this metric and date
                    value = metric_row[col]
                    row[clean_metric] = value

            # Append to result DataFrame
            result_df = pd.concat([result_df, pd.DataFrame([row])], ignore_index=True)

    # Sort by date
    result_df['date'] = pd.to_datetime(result_df['date'])
    result_df = result_df.sort_values('date')

    # Calculate additional metrics
    if 'sales' in result_df.columns and 'net_profit' in result_df.columns:
        # Calculate net profit margin
        result_df['net_profit_margin'] = (result_df['net_profit'] / result_df['sales']) * 100

    if 'sales' in result_df.columns:
        # Calculate revenue growth (YoY)
        result_df['revenue_growth'] = result_df['sales'].pct_change(4) * 100  # 4 quarters = 1 year

    # Convert date back to string format
    result_df['date'] = result_df['date'].dt.strftime('%Y-%m-%d')

    # Save to CSV
    result_df.to_csv(output_file, index=False)
    print(f"{Fore.GREEN}Transformed quarterly results saved to {output_file}{Style.RESET_ALL}")

    return result_df

def transform_annual_financials(input_file, output_file, statement_type):
    """Transform annual financial statements to match the expected format."""
    print(f"{Fore.CYAN}Transforming {statement_type} from {input_file}...{Style.RESET_ALL}")

    # Read the CSV file
    df = pd.read_csv(input_file)

    # Clean up column names
    df.columns = [col.replace('Â', '').replace('+', '').strip() for col in df.columns]

    # Create a new DataFrame with the expected structure
    result_df = pd.DataFrame()

    # Convert date columns to proper datetime format
    date_columns = [col for col in df.columns if col not in ['Metric']]

    for col in date_columns:
        # Extract year
        match = re.match(r'Mar\s+(\d{4})', col)
        if match:
            year = match.group(1)
            date_str = f"{year}-03-31"

            # Create a row for each date
            row = {'date': date_str}

            # Add metrics for this date
            for _, metric_row in df.iterrows():
                metric_name = metric_row['Metric']
                if pd.notna(metric_name) and metric_name.strip():
                    # Clean up metric name
                    clean_metric = clean_column_name(metric_name)
                    # Get the value for this metric and date
                    value = metric_row[col]
                    row[clean_metric] = value

            # Append to result DataFrame
            result_df = pd.concat([result_df, pd.DataFrame([row])], ignore_index=True)

    # Sort by date
    result_df['date'] = pd.to_datetime(result_df['date'])
    result_df = result_df.sort_values('date')

    # Calculate additional metrics based on statement type
    if statement_type == 'income_statement':
        if 'sales' in result_df.columns and 'net_profit' in result_df.columns:
            # Calculate net profit margin
            result_df['net_profit_margin'] = (result_df['net_profit'] / result_df['sales']) * 100

        if 'sales' in result_df.columns:
            # Calculate revenue growth (YoY)
            result_df['revenue_growth'] = result_df['sales'].pct_change() * 100

    elif statement_type == 'balance_sheet':
        if 'total_assets' in result_df.columns and 'total_liabilities' in result_df.columns:
            # Calculate debt-to-equity ratio
            equity = result_df['total_assets'] - result_df['total_liabilities']

            # Check if borrowings column exists, it might have a different name
            borrowings_col = None
            for col in result_df.columns:
                if 'borrow' in col.lower() or 'debt' in col.lower():
                    borrowings_col = col
                    break

            if borrowings_col:
                result_df['debt_to_equity'] = result_df[borrowings_col] / equity
            else:
                print(f"{Fore.YELLOW}Warning: Borrowings column not found, skipping debt-to-equity calculation{Style.RESET_ALL}")

    # Convert date back to string format
    result_df['date'] = result_df['date'].dt.strftime('%Y-%m-%d')

    # Save to CSV
    result_df.to_csv(output_file, index=False)
    print(f"{Fore.GREEN}Transformed {statement_type} saved to {output_file}{Style.RESET_ALL}")

    return result_df

def transform_ratios(input_file, output_file):
    """Transform financial ratios to match the expected format."""
    print(f"{Fore.CYAN}Transforming ratios from {input_file}...{Style.RESET_ALL}")

    # Read the CSV file
    df = pd.read_csv(input_file)

    # Clean up column names
    df.columns = [col.replace('Â', '').replace('+', '').strip() for col in df.columns]

    # Create a new DataFrame with the expected structure
    result_df = pd.DataFrame()

    # Convert date columns to proper datetime format
    date_columns = [col for col in df.columns if col not in ['Metric']]

    for col in date_columns:
        # Extract year
        match = re.match(r'Mar\s+(\d{4})', col)
        if match:
            year = match.group(1)
            date_str = f"{year}-03-31"

            # Create a row for each date
            row = {'date': date_str}

            # Add metrics for this date
            for _, metric_row in df.iterrows():
                metric_name = metric_row['Metric']
                if pd.notna(metric_name) and metric_name.strip():
                    # Clean up metric name
                    clean_metric = clean_column_name(metric_name)
                    # Get the value for this metric and date
                    value = metric_row[col]
                    row[clean_metric] = value

            # Append to result DataFrame
            result_df = pd.concat([result_df, pd.DataFrame([row])], ignore_index=True)

    # Sort by date
    result_df['date'] = pd.to_datetime(result_df['date'])
    result_df = result_df.sort_values('date')

    # Convert date back to string format
    result_df['date'] = result_df['date'].dt.strftime('%Y-%m-%d')

    # Save to CSV
    result_df.to_csv(output_file, index=False)
    print(f"{Fore.GREEN}Transformed ratios saved to {output_file}{Style.RESET_ALL}")

    return result_df

def create_mock_price_data(quarterly_df, output_file):
    """Create mock price data based on quarterly results."""
    print(f"{Fore.CYAN}Creating mock price data...{Style.RESET_ALL}")

    # Get the date range from quarterly results
    dates = pd.to_datetime(quarterly_df['date'])
    start_date = dates.min()
    end_date = dates.max()

    # Create a date range with daily frequency
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')

    # Create a DataFrame with the date range
    price_df = pd.DataFrame({'date': date_range})

    # Convert date to string format
    price_df['date'] = price_df['date'].dt.strftime('%Y-%m-%d')

    # Generate mock price data
    # Start with a base price and add some random variation
    base_price = 100.0
    price_data = []

    for i in range(len(date_range)):
        # Simple price model with some trend and randomness
        if i == 0:
            price = base_price
        else:
            # Add a small random change (-2% to +2%)
            change = (0.5 - 0.5 * (i % 2)) * 0.02  # Alternating positive and negative changes
            price = price_data[-1] * (1 + change)

        price_data.append(price)

    # Add price data to DataFrame
    price_df['close'] = price_data

    # Calculate other price metrics
    price_df['open'] = price_df['close'].shift(1).fillna(price_df['close'])
    price_df['high'] = price_df['close'] * 1.01  # 1% higher than close
    price_df['low'] = price_df['close'] * 0.99   # 1% lower than close
    price_df['volume'] = 1000000  # Constant volume for simplicity

    # Save to CSV
    price_df.to_csv(output_file, index=False)
    print(f"{Fore.GREEN}Mock price data saved to {output_file}{Style.RESET_ALL}")

    return price_df

def create_mock_news_data(quarterly_df, output_file):
    """Create mock news data based on quarterly results."""
    print(f"{Fore.CYAN}Creating mock news data...{Style.RESET_ALL}")

    # Get the date range from quarterly results
    dates = pd.to_datetime(quarterly_df['date'])

    # Create mock news data
    news_data = []

    for date in dates:
        # Create a positive news item
        positive_news = {
            'date': date.strftime('%Y-%m-%d'),
            'title': f"Company Reports Strong Results for {date.strftime('%b %Y')}",
            'content': "The company reported strong financial results, exceeding analyst expectations.",
            'sentiment': 'positive',
            'source': 'Financial Times'
        }
        news_data.append(positive_news)

        # Create a negative news item
        negative_news = {
            'date': (date + pd.Timedelta(days=7)).strftime('%Y-%m-%d'),
            'title': f"Analysts Concerned About Rising Costs",
            'content': "Analysts expressed concerns about the company's rising operational costs.",
            'sentiment': 'negative',
            'source': 'Bloomberg'
        }
        news_data.append(negative_news)

        # Create a neutral news item
        neutral_news = {
            'date': (date + pd.Timedelta(days=14)).strftime('%Y-%m-%d'),
            'title': f"Company Announces New Product Launch",
            'content': "The company announced plans to launch a new product line in the coming quarter.",
            'sentiment': 'neutral',
            'source': 'Reuters'
        }
        news_data.append(neutral_news)

    # Create DataFrame
    news_df = pd.DataFrame(news_data)

    # Save to CSV
    news_df.to_csv(output_file, index=False)
    print(f"{Fore.GREEN}Mock news data saved to {output_file}{Style.RESET_ALL}")

    return news_df

def create_mock_insider_trades(quarterly_df, output_file):
    """Create mock insider trading data based on quarterly results."""
    print(f"{Fore.CYAN}Creating mock insider trading data...{Style.RESET_ALL}")

    # Get the date range from quarterly results
    dates = pd.to_datetime(quarterly_df['date'])

    # Create mock insider trading data
    insider_data = []

    for date in dates:
        # Create a buy transaction
        buy_transaction = {
            'date': (date + pd.Timedelta(days=10)).strftime('%Y-%m-%d'),
            'insider_name': 'John Smith',
            'insider_title': 'CEO',
            'transaction_type': 'BUY',
            'transaction_shares': 10000,
            'transaction_price': 100.0,
            'total_shares_owned': 100000
        }
        insider_data.append(buy_transaction)

        # Create a sell transaction
        sell_transaction = {
            'date': (date + pd.Timedelta(days=20)).strftime('%Y-%m-%d'),
            'insider_name': 'Jane Doe',
            'insider_title': 'CFO',
            'transaction_type': 'SELL',
            'transaction_shares': 5000,
            'transaction_price': 105.0,
            'total_shares_owned': 50000
        }
        insider_data.append(sell_transaction)

    # Create DataFrame
    insider_df = pd.DataFrame(insider_data)

    # Save to CSV
    insider_df.to_csv(output_file, index=False)
    print(f"{Fore.GREEN}Mock insider trading data saved to {output_file}{Style.RESET_ALL}")

    return insider_df

def transform_screener_data(input_dir, output_dir):
    """Transform Screener.in data to match the format expected by AI Hedge Fund analysis agents."""
    print(f"{Fore.CYAN}Transforming Screener.in data from {input_dir} to {output_dir}...{Style.RESET_ALL}")

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Transform quarterly results
    quarterly_file = os.path.join(input_dir, 'Quarterly_Results.csv')
    if os.path.exists(quarterly_file):
        quarterly_df = transform_quarterly_results(
            quarterly_file,
            os.path.join(output_dir, 'quarterly_results.csv')
        )
    else:
        print(f"{Fore.RED}Quarterly results file not found: {quarterly_file}{Style.RESET_ALL}")
        return

    # Transform annual financials
    pl_file = os.path.join(input_dir, 'Profit_&_Loss.csv')
    if os.path.exists(pl_file):
        transform_annual_financials(
            pl_file,
            os.path.join(output_dir, 'income_statement.csv'),
            'income_statement'
        )
    else:
        print(f"{Fore.RED}Profit & Loss file not found: {pl_file}{Style.RESET_ALL}")

    # Transform balance sheet
    bs_file = os.path.join(input_dir, 'Balance_Sheet.csv')
    if os.path.exists(bs_file):
        transform_annual_financials(
            bs_file,
            os.path.join(output_dir, 'balance_sheet.csv'),
            'balance_sheet'
        )
    else:
        print(f"{Fore.RED}Balance Sheet file not found: {bs_file}{Style.RESET_ALL}")

    # Transform ratios
    ratios_file = os.path.join(input_dir, 'Ratios.csv')
    if os.path.exists(ratios_file):
        transform_ratios(
            ratios_file,
            os.path.join(output_dir, 'financial_ratios.csv')
        )
    else:
        print(f"{Fore.RED}Ratios file not found: {ratios_file}{Style.RESET_ALL}")

    # Create mock price data
    create_mock_price_data(
        quarterly_df,
        os.path.join(output_dir, 'price_data.csv')
    )

    # Create mock news data
    create_mock_news_data(
        quarterly_df,
        os.path.join(output_dir, 'news_data.csv')
    )

    # Create mock insider trading data
    create_mock_insider_trades(
        quarterly_df,
        os.path.join(output_dir, 'insider_trades.csv')
    )

    print(f"{Fore.GREEN}Data transformation completed. Transformed data saved to {output_dir}{Style.RESET_ALL}")

if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Transform Screener.in data to match the format expected by AI Hedge Fund analysis agents.')
    parser.add_argument('--input-dir', type=str, default='reliance_data_fixed',
                        help='Directory containing Screener.in data')
    parser.add_argument('--output-dir', type=str, default='reliance_data_transformed',
                        help='Directory to save transformed data')

    args = parser.parse_args()

    # Transform data
    transform_screener_data(args.input_dir, args.output_dir)
